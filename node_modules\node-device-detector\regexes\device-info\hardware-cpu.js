module.exports = {
  "cpu": {
    "1": {
      "name": "MediaTek MT6582M",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "2": {
      "name": "MediaTek MT6592",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1700,
      "process": "28nm",
      "gpu_id": 3
    },
    "3": {
      "name": "HiSilicon Kirin 620",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1200,
      "process": "28nm",
      "gpu_id": 3
    },
    "4": {
      "name": "MediaTek MT6580A",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "5": {
      "name": "MediaTek MT6580",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "6": {
      "name": "Spreadtrum SC7731",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "7": {
      "name": "MediaTek MT6582",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "8": {
      "name": "MediaTek MT6739",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 4
    },
    "9": {
      "name": "MediaTek MT6737",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 5
    },
    "10": {
      "name": "Qualcomm Snapdragon 210 MSM8909",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1100,
      "process": "28nm",
      "gpu_id": 6
    },
    "11": {
      "name": "Qualcomm Snapdragon 212 APQ8009",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 6
    },
    "12": {
      "name": "MediaTek MT8125",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1200,
      "process": "28nm",
      "gpu_id": 7
    },
    "13": {
      "name": "Samsung Exynos 3110",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 1000,
      "process": "45nm",
      "gpu_id": 8
    },
    "14": {
      "name": "Qualcomm Snapdragon S4 Plus MSM8960",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 9
    },
    "15": {
      "name": "Qualcomm Snapdragon S4 Pro MSM8960T",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1700,
      "process": "28nm",
      "gpu_id": 10
    },
    "16": {
      "name": "Intel Atom Z3560",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1830,
      "process": "22nm",
      "gpu_id": 13
    },
    "17": {
      "name": "Qualcomm Snapdragon 410 MSM8916",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1400,
      "process": "28nm",
      "gpu_id": 14
    },
    "18": {
      "name": "Intel Atom Z2560",
      "type": "x86",
      "cores": "2/4",
      "clock_rate": 1600,
      "process": "35nm",
      "gpu_id": 15
    },
    "19": {
      "name": "Qualcomm Snapdragon 630",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2200,
      "process": "14nm",
      "gpu_id": 16
    },
    "20": {
      "name": "Qualcomm Snapdragon 425 MSM8917",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1400,
      "process": "28nm",
      "gpu_id": 17
    },
    "21": {
      "name": "Spreadtrum SC9832",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "22": {
      "name": "Qualcomm Snapdragon 615 MSM8939",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1700,
      "process": "28nm",
      "gpu_id": 18
    },
    "23": {
      "name": "Amlogic S805",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1400,
      "process": "28nm",
      "gpu_id": 1
    },
    "24": {
      "name": "MediaTek MT6572",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1200,
      "process": "28nm",
      "gpu_id": 1
    },
    "25": {
      "name": "Qualcomm Snapdragon 835 MSM8998",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2450,
      "process": "10nm",
      "gpu_id": 19
    },
    "26": {
      "name": "Qualcomm MSM7600",
      "type": "x86",
      "cores": 1,
      "process": "65nm",
      "clock_rate": 528
    },
    "27": {
      "name": "MediaTek MT6589",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1200,
      "process": "28nm",
      "gpu_id": 7
    },
    "28": {
      "name": "HiSilicon Kirin 710",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2200,
      "process": "14nm",
      "gpu_id": 20
    },
    "29": {
      "name": "Texas Instruments OMAP 4460",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1200,
      "process": "45nm",
      "gpu_id": 8
    },
    "30": {
      "name": "HiSilicon Kirin 920",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1700,
      "process": "28nm",
      "gpu_id": 21
    },
    "31": {
      "name": "Qualcomm Snapdragon S1 MSM7227A",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 1000,
      "process": "45nm",
      "gpu_id": 22
    },
    "32": {
      "name": "Qualcomm Snapdragon S4 Plus MSM8930",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1200,
      "process": "28nm",
      "gpu_id": 12
    },
    "33": {
      "name": "Qualcomm Snapdragon 200 MSM8212",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1200,
      "process": "28nm",
      "gpu_id": 23
    },
    "34": {
      "name": "HiSilicon Kirin 910T",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1800,
      "process": "28nm",
      "gpu_id": 3
    },
    "35": {
      "name": "Qualcomm Snapdragon 435 MSM8940",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1400,
      "process": "28nm",
      "gpu_id": 24
    },
    "36": {
      "name": "MediaTek MT6572M",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1200,
      "process": "28nm",
      "gpu_id": 1
    },
    "37": {
      "name": "HiSilicon Kirin 710F",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2200,
      "process": "12nm",
      "gpu_id": 20
    },
    "38": {
      "name": "MediaTek Helio P35 MT6765",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2300,
      "process": "12nm",
      "gpu_id": 25
    },
    "39": {
      "name": "MediaTek MT6753T",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 2
    },
    "40": {
      "name": "MediaTek MT6750",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 26
    },
    "41": {
      "name": "Qualcomm Snapdragon 439",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 24
    },
    "42": {
      "name": "HiSilicon Kirin 659",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2360,
      "process": "16nm",
      "gpu_id": 27
    },
    "43": {
      "name": "Qualcomm Snapdragon 430 MSM8937",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1400,
      "process": "28nm",
      "gpu_id": 24
    },
    "44": {
      "name": "MediaTek Helio A22 MT6761",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 25
    },
    "45": {
      "name": "MediaTek Dimensity 800 MT6873",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "7nm",
      "gpu_id": 28
    },
    "46": {
      "name": "Qualcomm Snapdragon S1 MSM7225A",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 1000,
      "process": "45nm",
      "gpu_id": 22
    },
    "47": {
      "name": "MediaTek Helio P22 MT6762R",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 25
    },
    "48": {
      "name": "HiSilicon Kirin 655",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2100,
      "process": "16nm",
      "gpu_id": 27
    },
    "49": {
      "name": "Qualcomm Snapdragon S2 MSM8255",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 1500,
      "process": "45nm",
      "gpu_id": 29
    },
    "50": {
      "name": "HiSilicon Kirin 970",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2400,
      "process": "10nm",
      "gpu_id": 30
    },
    "51": {
      "name": "HiSilicon K3V2",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "40nm",
      "gpu_id": 31
    },
    "52": {
      "name": "HiSilicon Kirin 980",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2600,
      "process": "7nm",
      "gpu_id": 32
    },
    "53": {
      "name": "HiSilicon Kirin 985 5G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2850,
      "process": "7nm",
      "gpu_id": 33
    },
    "54": {
      "name": "HiSilicon Kirin 990 5G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2860,
      "process": "7nm",
      "gpu_id": 34
    },
    "55": {
      "name": "HiSilicon Kirin 820 5G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2360,
      "process": "7nm",
      "gpu_id": 35
    },
    "56": {
      "name": "MediaTek MT6735P",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1000,
      "process": "28nm",
      "gpu_id": 2
    },
    "57": {
      "name": "HiSilicon Kirin 810",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2270,
      "process": "7nm",
      "gpu_id": 36
    },
    "58": {
      "name": "MediaTek MT6580M",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "59": {
      "name": "Apple A13 Bionic APL1W85",
      "type": "ARM",
      "cores": 6,
      "process": "7nm",
      "clock_rate": 2650
    },
    "60": {
      "name": "MediaTek MT6752M",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1700,
      "process": "28nm",
      "gpu_id": 37
    },
    "61": {
      "name": "Qualcomm Snapdragon 660",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2200,
      "process": "14nm",
      "gpu_id": 38
    },
    "62": {
      "name": "Qualcomm Snapdragon 710",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2200,
      "process": "10nm",
      "gpu_id": 39
    },
    "63": {
      "name": "MediaTek Helio P23 MT6763",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2500,
      "process": "16nm",
      "gpu_id": 40
    },
    "64": {
      "name": "Qualcomm Snapdragon 632",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1800,
      "process": "14nm",
      "gpu_id": 41
    },
    "65": {
      "name": "Qualcomm Snapdragon 617 MSM8952",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 18
    },
    "66": {
      "name": "HiSilicon Kirin 650",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "16nm",
      "gpu_id": 27
    },
    "67": {
      "name": "Samsung Exynos 9825",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2730,
      "process": "7nm",
      "gpu_id": 42
    },
    "68": {
      "name": "Qualcomm Snapdragon 616 MSM8939v2",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1700,
      "process": "28nm",
      "gpu_id": 18
    },
    "69": {
      "name": "MediaTek MT6737T",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 5
    },
    "70": {
      "name": "HiSilicon Kirin 925",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1800,
      "process": "28nm",
      "gpu_id": 43
    },
    "71": {
      "name": "Allwinner A33",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "40nm",
      "gpu_id": 1
    },
    "72": {
      "name": "Rockchip RK3168",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1200,
      "process": "28nm",
      "gpu_id": 8
    },
    "73": {
      "name": "Rockchip RK3066",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1500,
      "process": "40nm",
      "gpu_id": 44
    },
    "74": {
      "name": "MediaTek MT8735",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 2
    },
    "75": {
      "name": "Nvidia Tegra 4",
      "type": "ARM",
      "cores": 4,
      "process": "28nm",
      "clock_rate": 1800
    },
    "76": {
      "name": "MediaTek MT6739W",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 4
    },
    "77": {
      "name": "Marvell PXA310",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 624
    },
    "78": {
      "name": "Qualcomm Snapdragon 400 MSM8226",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1200,
      "process": "28nm",
      "gpu_id": 12
    },
    "79": {
      "name": "MediaTek MT6735",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "28nm",
      "cpu_id": 2
    },
    "80": {
      "name": "Rockchip RK3188",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1800,
      "process": "28nm",
      "cpu_id": 44
    },
    "81": {
      "name": "Qualcomm Snapdragon 625",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "14nm",
      "cpu_id": 41
    },
    "82": {
      "name": "Rockchip RK3288",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1800,
      "process": "28nm",
      "cpu_id": 45
    },
    "83": {
      "name": "Unisoc SC9832E",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "28nm",
      "cpu_id": 46
    },
    "84": {
      "name": "MediaTek MT6739WW",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "28nm",
      "cpu_id": 4
    },
    "85": {
      "name": "MediaTek Helio X10 MT6795",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2200,
      "process": "28nm",
      "gpu_id": 47
    },
    "86": {
      "name": "MediaTek MT6737M",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1250,
      "process": "28nm",
      "gpu_id": 5
    },
    "87": {
      "name": "Nvidia Tegra 2",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1000,
      "process": "40nm"
    },
    "88": {
      "name": "Spreadtrum SC7731",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "89": {
      "name": "MediaTek MT8121",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 7
    },
    "90": {
      "name": "MediaTek MT6575",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 1000,
      "process": "40nm",
      "gpu_id": 48
    },
    "91": {
      "name": "Qualcomm Snapdragon S1 MSM7225",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 1000,
      "process": "45nm",
      "gpu_id": 22
    },
    "92": {
      "name": "AMD A8-6410",
      "type": "x86",
      "cores": 4,
      "clock_rate": 2400,
      "process": "28nm",
      "gpu_id": 49
    },
    "93": {
      "name": "MediaTek MT8321M",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "94": {
      "name": "Qualcomm Snapdragon 800 MSM8974",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2300,
      "process": "28nm",
      "gpu_id": 11
    },
    "95": {
      "name": "Texas Instruments OMAP 4470",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1500,
      "process": "45nm",
      "gpu_id": 8
    },
    "96": {
      "name": "Qualcomm Snapdragon 810 MSM8994",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "20nm",
      "gpu_id": 50
    },
    "97": {
      "name": "MediaTek Helio P60T",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 51
    },
    "98": {
      "name": "MediaTek MT8183",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 51
    },
    "99": {
      "name": "MediaTek MT8173",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2400,
      "process": "28nm",
      "gpu_id": 52
    },
    "100": {
      "name": "MediaTek MT8135",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1700,
      "process": "28nm",
      "gpu_id": 47
    },
    "101": {
      "name": "MediaTek MT8168",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 53
    },
    "102": {
      "name": "Rockchip RK3188T",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1800,
      "gpu_id": 44
    },
    "103": {
      "name": "Spreadtrum SC7731",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "104": {
      "name": "Apple A6x",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1400
    },
    "105": {
      "name": "Apple A5",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1000
    },
    "106": {
      "name": "Apple A8X",
      "type": "ARM",
      "cores": 3,
      "clock_rate": 1500,
      "process": "20nm",
      "gpu_id": 54
    },
    "107": {
      "name": "MediaTek MT8389",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1200,
      "process": "28nm",
      "gpu_id": 7
    },
    "108": {
      "name": "Apple A7",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1400,
      "process": "28nm",
      "gpu_id": 13
    },
    "109": {
      "name": "Unisoc SC9863A",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1600,
      "process": "28nm",
      "gpu_id": 55
    },
    "110": {
      "name": "Qualcomm Snapdragon S4 Play MSM8225Q",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1200,
      "process": "45nm",
      "gpu_id": 56
    },
    "111": {
      "name": "Qualcomm Snapdragon 400 MSM8926",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1200,
      "process": "28nm",
      "gpu_id": 12
    },
    "112": {
      "name": "MediaTek MT6589T",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "gpu_id": 7
    },
    "113": {
      "name": "MediaTek MT6737VWT",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 5
    },
    "114": {
      "name": "MediaTek Helio P70 MT6771",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2100,
      "process": "12nm",
      "gpu_id": 51
    },
    "115": {
      "name": "Qualcomm Snapdragon 652 MSM8976",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1800,
      "process": "28nm",
      "gpu_id": 57
    },
    "116": {
      "name": "Rockchip RK3399",
      "type": "ARM",
      "cores": 6,
      "clock_rate": 2000,
      "gpu_id": 58
    },
    "117": {
      "name": "MediaTek Helio P10 MT6755",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "28nm",
      "gpu_id": 26
    },
    "118": {
      "name": "MediaTek MT6753",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 59
    },
    "119": {
      "name": "Qualcomm Snapdragon 200 8210",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1200,
      "gpu_id": 23
    },
    "120": {
      "name": "MediaTek MT8732",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "gpu_id": 37
    },
    "121": {
      "name": "MediaTek MT8735B",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "gpu_id": 2
    },
    "122": {
      "name": "Apple A14 Bionic",
      "type": "ARM",
      "cores": 3,
      "clock_rate": 3100
    },
    "123": {
      "name": "Apple A4",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 1000
    },
    "124": {
      "name": "Apple A6",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1000
    },
    "125": {
      "name": "Apple A8",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1400,
      "gpu_id": 60
    },
    "126": {
      "name": "Apple A9",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1800
    },
    "127": {
      "name": "Apple A10 Fusion",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2340
    },
    "128": {
      "name": "Apple A11 Bionic",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2390
    },
    "129": {
      "name": "Apple A13 Bionic",
      "type": "ARM",
      "cores": 6,
      "clock_rate": 2660
    },
    "130": {
      "name": "Apple A12 Bionic",
      "type": "ARM",
      "cores": 6,
      "clock_rate": 2490
    },
    "131": {
      "name": "Intel Atom Z2580",
      "type": "x86",
      "cores": "2/4",
      "clock_rate": 2000,
      "gpu_id": 61
    },
    "132": {
      "name": "Intel Atom Z2420",
      "type": "x86",
      "cores": "1/2",
      "clock_rate": 1200,
      "process": "32nm",
      "gpu_id": 8
    },
    "133": {
      "name": "WonderMedia PRIZM WM8950",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 1000,
      "gpu_id": 62
    },
    "134": {
      "name": "Qualcomm Snapdragon S4 Pro APQ8064A",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1700,
      "process": "28nm",
      "gpu_id": 10
    },
    "135": {
      "name": "Intel Atom Z3745",
      "type": "x86",
      "cores": 4,
      "clock_rate": 1860,
      "process": "22nm",
      "gpu_id": 63
    },
    "136": {
      "name": "Intel Atom Z3580",
      "type": "x86",
      "cores": 4,
      "clock_rate": 2330,
      "gpu_id": 13
    },
    "137": {
      "name": "NVIDIA Tegra 3",
      "type": "ARM",
      "cores": 4,
      "process": "28nm",
      "clock_rate": 1700
    },
    "138": {
      "name": "Qualcomm Snapdragon 600 APQ8064T",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1700,
      "process": "28nm",
      "gpu_id": 64
    },
    "139": {
      "name": "Qualcomm Snapdragon 801 MSM8974AB",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2360,
      "process": "28nm",
      "gpu_id": 65
    },
    "140": {
      "name": "Qualcomm Snapdragon S4 Plus MSM8260A",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1700,
      "process": "28nm",
      "gpu_id": 66
    },
    "141": {
      "name": "Qualcomm Snapdragon 865+ 5G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 3100,
      "process": "7nm",
      "gpu_id": 67
    },
    "142": {
      "name": "MediaTek MT6739WM",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1100,
      "process": "28nm",
      "gpu_id": 4
    },
    "143": {
      "name": "MediaTek MT8317",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1200,
      "gpu_id": 48
    },
    "144": {
      "name": "MediaTek MT8163",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "gpu_id": 51
    },
    "145": {
      "name": "Qualcomm Snapdragon S1 MSM7227",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 600,
      "process": "65nm",
      "gpu_id": 22
    },
    "146": {
      "name": "Apple A12Z Bionic",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2490
    },
    "147": {
      "name": "Apple A12X Bionic",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2490
    },
    "148": {
      "name": "Apple A15 Bionic",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 0
    },
    "149": {
      "name": "Qualcomm MSM7227 Turbo",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 832,
      "process": "65nm",
      "gpu_id": 22
    },
    "150": {
      "name": "Qualcomm Snapdragon S1 QSD8250",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 1000,
      "process": "65nm",
      "gpu_id": 22
    },
    "151": {
      "name": "MediaTek MT8111",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "152": {
      "name": "Intel Atom Z2760",
      "type": "x86",
      "cores": "2/4",
      "clock_rate": 1800,
      "gpu_id": 68
    },
    "153": {
      "name": "Intel Core i5-3317U",
      "type": "x86",
      "cores": "2/4",
      "clock_rate": 1700,
      "gpu_id": 69
    },
    "154": {
      "name": "MediaTek MT6732",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "gpu_id": 37
    },
    "155": {
      "name": "Intel Atom Z3735G",
      "type": "x86",
      "cores": 4,
      "clock_rate": 1830,
      "gpu_id": 70
    },
    "156": {
      "name": "Intel Atom Z3740",
      "type": "x86",
      "cores": 4,
      "clock_rate": 1860,
      "gpu_id": 70
    },
    "157": {
      "name": "Qualcomm Snapdragon 808 MSM8992",
      "type": "ARM",
      "cores": 6,
      "clock_rate": 2000,
      "process": "20nm",
      "gpu_id": 71
    },
    "158": {
      "name": "Qualcomm Snapdragon S2 MSM7230",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 800,
      "gpu_id": 29
    },
    "159": {
      "name": "MediaTek MT6732M",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "gpu_id": 37
    },
    "160": {
      "name": "Intel Atom x7 Z8700",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2400,
      "gpu_id": 72
    },
    "161": {
      "name": "NVIDIA Tegra 3 T30L",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500
    },
    "162": {
      "name": "MediaTek MT6750T",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 26
    },
    "163": {
      "name": "MediaTek MT8321",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "164": {
      "name": "MediaTek Helio P22 MT6762D",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 25
    },
    "165": {
      "name": "Spreadtrum SC7731E",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "166": {
      "name": "MediaTek Helio P23 MT6763T",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2500,
      "process": "16nm",
      "gpu_id": 40
    },
    "167": {
      "name": "MediaTek MT6516",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 416,
      "process": "65nm"
    },
    "168": {
      "name": "MediaTek MT8765A",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "gpu_id": 2
    },
    "169": {
      "name": "MediaTek Helio P23 MT6763V",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "gpu_id": 40
    },
    "170": {
      "name": "MediaTek MT6738",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "gpu_id": 26
    },
    "171": {
      "name": "MediaTek Helio P22 MT6762",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 25
    },
    "172": {
      "name": "Qualcomm Snapdragon 429 SDM429",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2000,
      "gpu_id": 73
    },
    "173": {
      "name": "Qualcomm Snapdragon 820 MSM8996",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2200,
      "process": "14nm",
      "gpu_id": 74
    },
    "174": {
      "name": "MediaTek Helio P20 MT6757",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2300,
      "process": "16nm",
      "gpu_id": 75
    },
    "175": {
      "name": "MediaTek MT8766B",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2000,
      "gpu_id": 76
    },
    "176": {
      "name": "MediaTek MT8165A",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "gpu_id": 37
    },
    "177": {
      "name": "Rockchip RK2918",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 1000,
      "gpu_id": 77
    },
    "178": {
      "name": "MediaTek MT6235",
      "type": "",
      "cores": 1,
      "clock_rate": 208
    },
    "179": {
      "name": "MediaTek MT8392",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "gpu_id": 3
    },
    "180": {
      "name": "MediaTek MT6577",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1000,
      "gpu_id": 48
    },
    "181": {
      "name": "Apple A5X",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1000,
      "gpu_id": 78
    },
    "182": {
      "name": "MediaTek MT6591",
      "type": "ARM",
      "cores": 6,
      "clock_rate": 1500,
      "gpu_id": 0
    },
    "183": {
      "name": "MediaTek MT6573",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 650,
      "gpu_id": 48
    },
    "184": {
      "name": "MediaTek MT8127",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "gpu_id": 3
    },
    "185": {
      "name": "Rockchip RK2928",
      "type": "ARM",
      "clock_rate": 1000,
      "cores": 1,
      "gpu_id": 62
    },
    "186": {
      "name": "Qualcomm QSC6155",
      "type": "",
      "clock_rate": 480,
      "cores": 1
    },
    "187": {
      "name": "MediaTek MT8312",
      "type": "ARM",
      "clock_rate": 1300,
      "cores": 2,
      "gpu_id": 62
    },
    "188": {
      "name": "MediaTek MT6268",
      "type": "",
      "clock_rate": "",
      "cores": 1
    },
    "189": {
      "name": "Intel Atom x5 Z8350",
      "type": "x86",
      "clock_rate": 1960,
      "cores": 4,
      "process": "14nm",
      "gpu_id": 72
    },
    "190": {
      "name": "Texas Instruments OMAP 4430",
      "type": "ARM",
      "clock_rate": 1000,
      "cores": 1
    },
    "191": {
      "name": "Apple A5 S5L8940X",
      "type": "ARM",
      "clock_rate": 1000,
      "cores": 1,
      "gpu_id": 79
    },
    "192": {
      "name": "Apple S6",
      "type": "ARM",
      "clock_rate": 0,
      "cores": 0,
      "gpu_id": 0
    },
    "193": {
      "name": "Apple S5",
      "type": "ARM",
      "clock_rate": 0,
      "cores": 0,
      "gpu_id": 0
    },
    "194": {
      "name": "MediaTek Helio X20 MT6797M",
      "type": "ARM",
      "clock_rate": 2100,
      "cores": 10,
      "process": "20nm",
      "gpu_id": 80
    },
    "195": {
      "name": "AllWinner A13",
      "type": "ARM",
      "clock_rate": 1000,
      "cores": 1,
      "gpu_id": 62
    },
    "196": {
      "name": "Qualcomm Snapdragon 653 MSM8976 Pro",
      "type": "ARM",
      "clock_rate": 1950,
      "cores": 8,
      "process": "28nm",
      "gpu_id": 57
    },
    "197": {
      "name": "Qualcomm Snapdragon 855 Plus",
      "type": "ARM",
      "clock_rate": 2960,
      "cores": 8,
      "process": "7nm",
      "gpu_id": 81
    },
    "198": {
      "name": "Qualcomm Snapdragon 845",
      "type": "ARM",
      "clock_rate": 2800,
      "cores": 8,
      "process": "10nm",
      "gpu_id": 82
    },
    "199": {
      "name": "Unisoc Tiger T618",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "gpu_id": 83
    },
    "200": {
      "name": "Qualcomm MSM8225Q",
      "type": "ARM",
      "clock_rate": 1200,
      "cores": 4,
      "gpu_id": 56
    },
    "201": {
      "name": "Action ATM7029",
      "type": "ARM",
      "clock_rate": 1200,
      "cores": 4,
      "gpu_id": 84
    },
    "202": {
      "name": "Qualcomm Snapdragon S3 MSM8260",
      "type": "ARM",
      "clock_rate": 1500,
      "cores": 2,
      "process": "45nm",
      "gpu_id": 85
    },
    "203": {
      "name": "Intel Core M3-6Y30",
      "type": "x86",
      "clock_rate": 2200,
      "cores": "2/4",
      "gpu_id": 86
    },
    "204": {
      "name": "Qualcomm Snapdragon 625 MSM8953",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "process": "14nm",
      "gpu_id": 41
    },
    "205": {
      "name": "Qualcomm Snapdragon 636",
      "type": "ARM",
      "clock_rate": 1800,
      "cores": 8,
      "process": "14nm",
      "gpu_id": 87
    },
    "206": {
      "name": "Intel Atom Z2520",
      "type": "ARM",
      "clock_rate": 1200,
      "cores": "2/4",
      "gpu_id": 88
    },
    "207": {
      "name": "Qualcomm Snapdragon 865",
      "type": "ARM",
      "clock_rate": 2840,
      "cores": 8,
      "process": "7nm",
      "gpu_id": 67
    },
    "208": {
      "name": "Qualcomm Snapdragon 821 MSM8996 Pro",
      "type": "ARM",
      "clock_rate": 2400,
      "cores": 4,
      "process": "14nm",
      "gpu_id": 74
    },
    "209": {
      "name": "Intel Atom Z3590",
      "type": "ARM",
      "clock_rate": 2500,
      "cores": 4,
      "gpu_id": 13
    },
    "210": {
      "name": "MediaTek MT6572А",
      "type": "ARM",
      "clock_rate": 1200,
      "cores": 2,
      "gpu_id": 62
    },
    "211": {
      "name": "Intel Atom x3 C3230",
      "type": "ARM",
      "clock_rate": 1200,
      "cores": 4,
      "process": "28nm",
      "gpu_id": 3
    },
    "212": {
      "name": "Qualcomm Snapdragon S4 Plus MSM8230",
      "type": "ARM",
      "clock_rate": 1200,
      "cores": 2,
      "gpu_id": 12
    },
    "213": {
      "name": "HiSilicon Kirin 710A",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "gpu_id": 20
    },
    "214": {
      "name": "Qualcomm Snapdragon 450",
      "type": "ARM",
      "clock_rate": 1800,
      "cores": 8,
      "process": "14nm",
      "gpu_id": 41
    },
    "215": {
      "name": "Spreadtrum SC9830",
      "type": "ARM",
      "clock_rate": 1500,
      "cores": 4,
      "process": "28nm",
      "gpu_id": 1
    },
    "216": {
      "name": "HiSilicon Kirin 950",
      "type": "ARM",
      "clock_rate": 2300,
      "cores": 8,
      "gpu_id": 80
    },
    "217": {
      "name": "Qualcomm Snapdragon S1 MSM7625",
      "type": "ARM",
      "clock_rate": 600,
      "cores": 1,
      "gpu_id": 22
    },
    "218": {
      "name": "Qualcomm Snapdragon S1 MSM7627A",
      "type": "ARM",
      "clock_rate": 600,
      "cores": 1,
      "gpu_id": 22
    },
    "219": {
      "name": "Qualcomm Snapdragon S4 Play MSM8225",
      "type": "ARM",
      "clock_rate": 1200,
      "cores": 2,
      "process": "45nm",
      "gpu_id": 56
    },
    "220": {
      "name": "Qualcomm Snapdragon S4 Play MSM8625Q",
      "type": "ARM",
      "clock_rate": 1200,
      "cores": 2,
      "process": "45nm",
      "gpu_id": 56
    },
    "221": {
      "name": "HiSilicon Kirin 960",
      "type": "ARM",
      "clock_rate": 2400,
      "cores": 8,
      "process": "16nm",
      "gpu_id": 89
    },
    "222": {
      "name": "HiSilicon Kirin 960s",
      "type": "ARM",
      "clock_rate": 2100,
      "cores": 8,
      "process": "16nm",
      "gpu_id": 89
    },
    "223": {
      "name": "HiSilicon Kirin 935",
      "type": "ARM",
      "clock_rate": 2200,
      "cores": 8,
      "gpu_id": 21
    },
    "224": {
      "name": "Qualcomm Snapdragon 450",
      "type": "ARM",
      "clock_rate": 1800,
      "cores": 8,
      "process": "14nm",
      "gpu_id": 41
    },
    "225": {
      "name": "HiSilicon Kirin 955",
      "type": "ARM",
      "clock_rate": 2500,
      "cores": 8,
      "process": "16nm",
      "gpu_id": 80
    },
    "226": {
      "name": "MediaTek Helio G80",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "process": "12nm",
      "gpu_id": 83
    },
    "227": {
      "name": "HiSilicon Kirin 930",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "process": "28nm",
      "gpu_id": 21
    },
    "228": {
      "name": "Hisilicon Hi3798MV200",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 4,
      "gpu_id": 3
    },
    "229": {
      "name": "Qualcomm Snapdragon 215",
      "type": "ARM",
      "clock_rate": 1300,
      "cores": 4,
      "process": "28nm",
      "gpu_id": 17
    },
    "230": {
      "name": "MediaTek MT8765B",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 2
    },
    "231": {
      "name": "MediaTek MT8765",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1280,
      "process": "28nm",
      "gpu_id": 2
    },
    "232": {
      "name": "MediaTek Helio P25",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2600,
      "process": "16nm",
      "gpu_id": 75
    },
    "233": {
      "name": "Samsung Exynos 4412 Quad",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1400,
      "process": "32nm",
      "gpu_id": 44
    },
    "234": {
      "name": "Qualcomm Snapdragon 855",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2840,
      "process": "7nm",
      "gpu_id": 81
    },
    "235": {
      "name": "Broadcom BCM28155",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1200
    },
    "236": {
      "name": "Broadcom BCM23550",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1200
    },
    "237": {
      "name": "Samsung Exynos 7904",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1800,
      "process": "14nm",
      "gpu_id": 40
    },
    "238": {
      "name": "Spreadtrum SC7730",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "gpu_id": 1
    },
    "239": {
      "name": "Samsung Exynos 4415",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "gpu_id": 1
    },
    "240": {
      "name": "Samsung Exynos 4210",
      "type": "ARM",
      "cores": 1,
      "clock_rate": 1400,
      "gpu_id": 62
    },
    "241": {
      "name": "Samsung Exynos 9810",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2900,
      "process": "10nm",
      "gpu_id": 90
    },
    "242": {
      "name": "Qualcomm Snapdragon 765G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2400,
      "process": "7nm",
      "gpu_id": 91
    },
    "243": {
      "name": "Qualcomm Snapdragon 801 MSM8974AC",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2450,
      "process": "28nm",
      "gpu_id": 65
    },
    "244": {
      "name": "Qualcomm Snapdragon 460",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1800,
      "process": "11nm",
      "gpu_id": 92
    },
    "245": {
      "name": "AllWinner A10",
      "type": "ARM",
      "clock_rate": 1200,
      "cores": 1,
      "gpu_id": 62
    },
    "246": {
      "name": "MediaTek MT6515",
      "type": "ARM",
      "clock_rate": 1000,
      "cores": 1,
      "gpu_id": 48
    },
    "247": {
      "name": "MediaTek MT6737W",
      "type": "ARM",
      "clock_rate": 1300,
      "cores": 4,
      "gpu_id": 2
    },
    "248": {
      "name": "Samsung Exynos 7884",
      "type": "ARM",
      "clock_rate": 1560,
      "cores": 8,
      "process": "14nm",
      "gpu_id": 40
    },
    "249": {
      "name": "Samsung Exynos 990",
      "type": "ARM",
      "clock_rate": 2730,
      "cores": 8,
      "process": "7nm",
      "gpu_id": 93
    },
    "250": {
      "name": "Samsung Exynos 850",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "process": "8nm",
      "gpu_id": 53
    },
    "251": {
      "name": "Samsung Exynos 7578",
      "type": "ARM",
      "clock_rate": 1500,
      "cores": 4,
      "process": "28nm",
      "gpu_id": 5
    },
    "252": {
      "name": "Samsung Exynos 7870 Octa",
      "type": "ARM",
      "clock_rate": 1600,
      "cores": 8,
      "process": "14nm",
      "gpu_id": 27
    },
    "253": {
      "name": "MediaTek Helio P65 MT6768",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "process": "12nm",
      "gpu_id": 83
    },
    "254": {
      "name": "Samsung Exynos 7580 Octa",
      "type": "ARM",
      "clock_rate": 1600,
      "cores": 8,
      "process": "28nm",
      "gpu_id": 5
    },
    "255": {
      "name": "Samsung Exynos 7880",
      "type": "ARM",
      "clock_rate": 1900,
      "cores": 8,
      "process": "14nm",
      "gpu_id": 94
    },
    "256": {
      "name": "Samsung Exynos 9610",
      "type": "ARM",
      "clock_rate": 2300,
      "cores": 8,
      "process": "10nm",
      "gpu_id": 51
    },
    "257": {
      "name": "Samsung Exynos 9611",
      "type": "ARM",
      "clock_rate": 2300,
      "cores": 8,
      "process": "10nm",
      "gpu_id": 51
    },
    "258": {
      "name": "Qualcomm Snapdragon 675",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "process": "11nm",
      "gpu_id": 95
    },
    "259": {
      "name": "Samsung Exynos 7885",
      "type": "ARM",
      "clock_rate": 2200,
      "cores": 8,
      "process": "14nm",
      "gpu_id": 95
    },
    "260": {
      "name": "Qualcomm Snapdragon 730G",
      "type": "ARM",
      "clock_rate": 2200,
      "cores": 8,
      "process": "8nm",
      "gpu_id": 96
    },
    "261": {
      "name": "Qualcomm Snapdragon 730",
      "type": "ARM",
      "clock_rate": 2200,
      "cores": 8,
      "process": "8nm",
      "gpu_id": 96
    },
    "262": {
      "name": "Samsung Exynos 980",
      "type": "ARM",
      "clock_rate": 2200,
      "cores": 8,
      "process": "8nm",
      "gpu_id": 97
    },
    "263": {
      "name": "Samsung Exynos 5430",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "process": "20nm",
      "gpu_id": 43
    },
    "264": {
      "name": "Samsung Exynos 7420",
      "type": "ARM",
      "clock_rate": 2100,
      "cores": 8,
      "process": "14nm",
      "gpu_id": 98
    },
    "265": {
      "name": "Broadcom BCM21553",
      "type": "ARM",
      "clock_rate": 832,
      "cores": 1,
      "process": "65nm"
    },
    "266": {
      "name": "ST-Ericsson NovaThor U8500",
      "type": "ARM",
      "clock_rate": 1000,
      "cores": 2,
      "process": "45nm",
      "gpu_id": 62
    },
    "267": {
      "name": "Samsung Exynos 2100 5G",
      "type": "ARM",
      "clock_rate": 2900,
      "cores": 8,
      "process": "5nm",
      "gpu_id": 99
    },
    "268": {
      "name": "Qualcomm Snapdragon 653 MSM8976 Pro",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "process": "28nm",
      "gpu_id": 57
    },
    "269": {
      "name": "Samsung Exynos 9820",
      "type": "ARM",
      "clock_rate": 2700,
      "cores": 8,
      "process": "8nm",
      "gpu_id": 42
    },
    "270": {
      "name": "Samsung Exynos 8890",
      "type": "ARM",
      "clock_rate": 2600,
      "cores": 8,
      "process": "14nm",
      "gpu_id": 100
    },
    "271": {
      "name": "Samsung Exynos 8895",
      "type": "ARM",
      "clock_rate": 2300,
      "cores": 8,
      "process": "10nm",
      "gpu_id": 101
    },
    "272": {
      "name": "Qualcomm Snapdragon 626 MSM8953 Pro",
      "type": "ARM",
      "clock_rate": 2200,
      "cores": 8,
      "process": "14nm",
      "gpu_id": 41
    },
    "273": {
      "name": "Qualcomm Snapdragon 720G",
      "type": "ARM",
      "clock_rate": 2300,
      "cores": 8,
      "process": "8nm",
      "gpu_id": 96
    },
    "274": {
      "name": "MediaTek Dimensity 720",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "process": "7nm",
      "gpu_id": 102
    },
    "275": {
      "name": "Qualcomm Snapdragon 750 5G",
      "type": "ARM",
      "clock_rate": 2200,
      "cores": 8,
      "process": "8nm",
      "gpu_id": 103
    },
    "276": {
      "name": "Qualcomm Snapdragon 805 APQ8084",
      "type": "ARM",
      "clock_rate": 2700,
      "cores": 4,
      "process": "28nm",
      "gpu_id": 104
    },
    "277": {
      "name": "Spreadtrum SC7715",
      "type": "ARM",
      "clock_rate": 1200,
      "cores": 1,
      "process": "40nm",
      "gpu_id": 62
    },
    "278": {
      "name": "Samsung Exynos 3475",
      "type": "ARM",
      "clock_rate": 1300,
      "cores": 4,
      "process": "28nm",
      "gpu_id": 2
    },
    "279": {
      "name": "Samsung Exynos 7570",
      "type": "ARM",
      "clock_rate": 1400,
      "cores": 4,
      "process": "14nm",
      "gpu_id": 5
    },
    "280": {
      "name": "Spreadtrum SC6815A",
      "type": "ARM",
      "clock_rate": 1000,
      "cores": 1,
      "process": "40nm",
      "gpu_id": 62
    },
    "281": {
      "name": "Qualcomm Snapdragon 427",
      "type": "ARM",
      "clock_rate": 1400,
      "cores": 4,
      "process": "28nm",
      "gpu_id": 17
    },
    "282": {
      "name": "Qualcomm Snapdragon 435 SDM435",
      "type": "ARM",
      "clock_rate": 1400,
      "cores": 8,
      "process": "28nm",
      "gpu_id": 24
    },
    "283": {
      "name": "Qualcomm Snapdragon 665",
      "type": "ARM",
      "clock_rate": 2200,
      "cores": 8,
      "process": "11nm",
      "gpu_id": 92
    },
    "284": {
      "name": "MediaTek Helio G25",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "process": "12nm",
      "gpu_id": 25
    },
    "285": {
      "name": "Qualcomm Snapdragon 662",
      "type": "ARM",
      "clock_rate": 2000,
      "cores": 8,
      "process": "11nm",
      "gpu_id": 92
    },
    "286": {
      "name": "Qualcomm Snapdragon S2 MSM8255",
      "type": "ARM",
      "clock_rate": 1500,
      "cores": 1,
      "process": "45nm",
      "gpu_id": 29
    },
    "287": {
      "name": "Spreadtrum SC7731G",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "288": {
      "name": "MediaTek MT6571",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 0
    },
    "289": {
      "name": "Qualcomm Snapdragon 712",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2300,
      "process": "10nm",
      "gpu_id": 39
    },
    "290": {
      "name": "MediaTek Helio P60",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 51
    },
    "291": {
      "name": "MediaTek Helio X20 MT6797",
      "type": "ARM",
      "cores": 10,
      "clock_rate": 2300,
      "process": "20nm",
      "gpu_id": 80
    },
    "292": {
      "name": "MediaTek Helio P20 MT6757",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2600,
      "process": "16nm",
      "gpu_id": 75
    },
    "293": {
      "name": "MediaTek MT6739WA",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 4
    },
    "294": {
      "name": "Qualcomm Snapdragon 400 MSM8928",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1600,
      "process": "28nm",
      "gpu_id": 12
    },
    "295": {
      "name": "Qualcomm Snapdragon S4 Plus MSM8227",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1000,
      "process": "28nm",
      "gpu_id": 12
    },
    "296": {
      "name": "MediaTek Dimensity 1000+",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2600,
      "process": "7nm",
      "gpu_id": 105
    },
    "297": {
      "name": "MediaTek Dimensity 820 5G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2600,
      "process": "7nm",
      "gpu_id": 106
    },
    "298": {
      "name": "Qualcomm Snapdragon 670",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2016,
      "process": "10nm",
      "gpu_id": 39
    },
    "299": {
      "name": "Samsung Exynos 880",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "8nm",
      "gpu_id": 97
    },
    "300": {
      "name": "MediaTek Dimensity 800U",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2400,
      "process": "7nm",
      "gpu_id": 102
    },
    "301": {
      "name": "Samsung Exynos 1080",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2800,
      "process": "5nm",
      "gpu_id": 107
    },
    "302": {
      "name": "MediaTek Dimensity 700",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2200,
      "process": "7nm",
      "gpu_id": 108
    },
    "303": {
      "name": "Qualcomm Snapdragon 768G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2800,
      "process": "7nm",
      "gpu_id": 91
    },
    "304": {
      "name": "Qualcomm Snapdragon 870 5G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 3200,
      "process": "7nm",
      "gpu_id": 67
    },
    "305": {
      "name": "Qualcomm Snapdragon 888 5G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2840,
      "process": "5nm",
      "gpu_id": 109
    },
    "306": {
      "name": "MediaTek Dimensity 1100",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2600,
      "process": "6nm",
      "gpu_id": 110
    },
    "307": {
      "name": "Qualcomm Snapdragon 480 5G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "8nm",
      "gpu_id": 103
    },
    "308": {
      "name": "MediaTek MT6735A",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "28nm",
      "cpu_id": 2
    },
    "309": {
      "name": "MediaTek Helio P25 MT6757CH",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2600,
      "process": "16nm",
      "gpu_id": 75
    },
    "310": {
      "name": "MediaTek MT6735M",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1000,
      "process": "28nm",
      "gpu_id": 2
    },
    "311": {
      "name": "Intel Atom x3-C3130",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1000,
      "process": "28nm",
      "gpu_id": 1
    },
    "312": {
      "name": "MediaTek MT6750S",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 26
    },
    "313": {
      "name": "MediaTek MT6592M",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1400,
      "process": "28nm",
      "gpu_id": 3
    },
    "314": {
      "name": "Amlogic S905W",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2000,
      "process": "28nm",
      "gpu_id": 111
    },
    "315": {
      "name": "Intel Atom x5 Z8300",
      "type": "x86",
      "cores": 4,
      "clock_rate": 1840,
      "process": "14nm",
      "gpu_id": 72
    },
    "316": {
      "name": "Allwinner A31S",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 0,
      "process": "40nm",
      "gpu_id": 61
    },
    "317": {
      "name": "Allwinner A20",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1000,
      "process": "40nm"
    },
    "318": {
      "name": "Unisoc T610",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1800,
      "process": "12nm",
      "gpu_id": 83
    },
    "319": {
      "name": "MediaTek Helio G70",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 83
    },
    "320": {
      "name": "MediaTek Dimensity 800U",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2400,
      "process": "7nm",
      "gpu_id": 102
    },
    "321": {
      "name": "MediaTek Helio G95",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2050,
      "process": "12nm",
      "gpu_id": 112
    },
    "322": {
      "name": "MediaTek Helio G85",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 83
    },
    "323": {
      "name": "MediaTek Helio G35",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2300,
      "process": "12nm",
      "gpu_id": 25
    },
    "324": {
      "name": "MediaTek Dimensity 1200 5G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 3000,
      "process": "6nm",
      "gpu_id": 110
    },
    "325": {
      "name": "MediaTek Helio G90T",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2050,
      "process": "12nm",
      "gpu_id": 112
    },
    "326": {
      "name": "MediaTek Dimensity 1100 5G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2600,
      "process": "6nm",
      "gpu_id": 110
    },
    "327": {
      "name": "HiSilicon Kirin 990",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2860,
      "process": "7nm",
      "gpu_id": 34
    },
    "328": {
      "name": "Qualcomm Snapdragon 650 MSM8956",
      "type": "ARM",
      "cores": 6,
      "clock_rate": 1800,
      "process": "28nm",
      "gpu_id": 57
    },
    "329": {
      "name": "Intel Atom x3 C3200",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1200,
      "process": "28nm",
      "gpu_id": 3
    },
    "330": {
      "name": "Spreadtrum SC7731C",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "331": {
      "name": "Rockchip RK3128",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1200,
      "process": "40nm",
      "gpu_id": 1
    },
    "332": {
      "name": "MediaTek MT6752",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1700,
      "process": "28nm",
      "gpu_id": 37
    },
    "333": {
      "name": "Amlogic S905",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2000,
      "process": "28nm",
      "gpu_id": 111
    },
    "334": {
      "name": "MediaTek Helio G80 MT6769V/CU",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 113
    },
    "335": {
      "name": "Spreadtrum SC9830A",
      "type": "ARM",
      "clock_rate": 1500,
      "cores": 4,
      "process": "28nm",
      "gpu_id": 1
    },
    "336": {
      "name": "MediaTek MT6580W",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "337": {
      "name": "MediaTek Helio A20 MT6761D",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1800,
      "process": "12nm",
      "gpu_id": 76
    },
    "338": {
      "name": "MediaTek MT6580P",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "339": {
      "name": "MediaTek MT8753P",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1000,
      "process": "28nm",
      "gpu_id": 2
    },
    "340": {
      "name": "MediaTek Helio А25",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1800,
      "process": "12nm",
      "gpu_id": 25
    },
    "341": {
      "name": "Spreadtrum SC7721E",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "342": {
      "name": "MediaTek Helio P35 MT6765V",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2300,
      "process": "12nm",
      "gpu_id": 25
    },
    "343": {
      "name": "MediaTek Helio P25 MT6757CD",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2600,
      "process": "16nm",
      "gpu_id": 75
    },
    "344": {
      "name": "MediaTek Dimensity 800 MT6873V",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "7nm",
      "gpu_id": 28
    },
    "345": {
      "name": "MediaTek Helio P95 MT6779V",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2200,
      "process": "12nm",
      "gpu_id": 114
    },
    "346": {
      "name": "MediaTek Helio P90 MT6779",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2200,
      "process": "12nm",
      "gpu_id": 114
    },
    "347": {
      "name": "MediaTek Dimensity 1000L MT6885Z",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2200,
      "process": "12nm",
      "gpu_id": 110
    },
    "348": {
      "name": "MediaTek Helio G35 MT6765G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2300,
      "process": "12nm",
      "gpu_id": 25
    },
    "349": {
      "name": "MediaTek Helio G88",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 113
    },
    "350": {
      "name": "MediaTek Helio G96",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2050,
      "process": "12nm",
      "gpu_id": 115
    },
    "351": {
      "name": "Allwinner A31",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 533,
      "process": "40nm",
      "gpu_id": 7
    },
    "352": {
      "name": "Amlogic AML8726-M6",
      "type": "ARM",
      "cores": 2,
      "clock_rate": 1500,
      "process": "40nm",
      "gpu_id": 1
    },
    "353": {
      "name": "MediaTek Helio P25 MT6757T",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2600,
      "process": "16nm",
      "gpu_id": 75
    },
    "354": {
      "name": "Unisoc Tiger T606",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1600,
      "process": "12nm",
      "gpu_id": 116
    },
    "355": {
      "name": "MediaTek Helio A25",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1800,
      "process": "12nm",
      "gpu_id": 25
    },
    "356": {
      "name": "Dimensity 810",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2400,
      "process": "6nm",
      "gpu_id": 108
    },
    "357": {
      "name": "Qualcomm Snapdragon 8 Gen 1",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 3000,
      "process": "4nm",
      "gpu_id": 117
    },
    "358": {
      "name": "Qualcomm Snapdragon 695 5G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2200,
      "process": "6nm",
      "gpu_id": 103
    },
    "359": {
      "name": "Qualcomm Snapdragon 778G 5G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2400,
      "process": "6nm",
      "gpu_id": 118
    },
    "360": {
      "name": "Spreadtrum SC9830A",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1500,
      "process": "28nm",
      "gpu_id": 1
    },
    "361": {
      "name": "Spreadtrum SC9832A",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 1300,
      "process": "28nm",
      "gpu_id": 1
    },
    "362": {
      "name": "MediaTek Dimensity 9000",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 3500,
      "process": "4nm",
      "gpu_id": 119
    },
    "363": {
      "name": "Qualcomm Snapdragon 680 4G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2400,
      "process": "6nm",
      "gpu_id": 92
    },
    "364": {
      "name": "Qualcomm Snapdragon 732G",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2300,
      "process": "8nm",
      "gpu_id": 96
    },
    "365": {
      "name": "Dimensity 810",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2400,
      "process": "6nm",
      "gpu_id": 115
    },
    "366": {
      "name": "Unisoc Tiger T612",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 1800,
      "process": "12nm",
      "gpu_id": 116
    },
    "367": {
      "name": "MediaTek Helio G36",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2200,
      "process": "12nm",
      "gpu_id": 25
    },
    "368": {
      "name": "MediaTek Dimensity 7050",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2600,
      "process": "6nm",
      "gpu_id": 121
    },
    "369": {
      "name": "MediaTek Dimensity 1300",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 3000,
      "process": "6nm",
      "gpu_id": 110
    },
    "370": {
      "name": "Qualcomm Snapdragon 7 Gen 1",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2400,
      "process": "4nm",
      "gpu_id": 122
    },
    "371": {
      "name": "MediaTek Dimensity 8100 Max",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2400,
      "process": "5nm",
      "gpu_id": 123
    },
    "372": {
      "name": "MediaTek Dimensity 810",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 2400,
      "process": "6nm",
      "gpu_id": 115
    },
    "373": {
      "name": "Qualcomm Snapdragon 8+ Gen 1",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 3000,
      "process": "4nm",
      "gpu_id": 117
    },
    "374": {
      "name": "Unisoc Tiger T310",
      "type": "ARM",
      "cores": 4,
      "clock_rate": 2000,
      "process": "12nm",
      "gpu_id": 76
    },
    "375": {
      "name": "MediaTek Dimensity 9300",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 3250,
      "process": "4nm",
      "gpu_id": 124
    },
    "376": {
      "name": "MediaTek Dimensity 9200",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 3050,
      "process": "4nm",
      "gpu_id": 125
    },
    "377": {
      "name": "Qualcomm Snapdragon 8 Gen 2",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 3200,
      "process": "4nm",
      "gpu_id": 126
    },
    "378": {
      "name": "Qualcomm Snapdragon 8 Gen 3",
      "type": "ARM",
      "cores": 8,
      "clock_rate": 3300,
      "process": "4nm",
      "gpu_id": 127
    }
  }
};
