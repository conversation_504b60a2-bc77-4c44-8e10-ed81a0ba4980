{"name": "@peculiar/asn1-schema", "version": "2.3.15", "description": "Decorators for ASN.1 schemas building", "files": ["build/**/*.{js,d.ts}", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/PeculiarVentures/asn1-schema/issues"}, "homepage": "https://github.com/PeculiarVentures/asn1-schema/tree/master/packages/schema#readme", "keywords": ["asn", "serialize", "parse", "convert", "decorator"], "license": "MIT", "author": "PeculiarVentures, LLC", "main": "build/cjs/index.js", "module": "build/es2015/index.js", "types": "build/types/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"test": "mocha", "clear": "<PERSON><PERSON><PERSON> build", "build": "npm run build:module && npm run build:types", "build:module": "npm run build:cjs && npm run build:es2015", "build:cjs": "tsc -p tsconfig.compile.json --removeComments --module commonjs --outDir build/cjs", "build:es2015": "tsc -p tsconfig.compile.json --removeComments --module ES2015 --outDir build/es2015", "prebuild:types": "rimraf build/types", "build:types": "tsc -p tsconfig.compile.json --outDir build/types --declaration --emitDeclarationOnly", "rebuild": "npm run clear && npm run build"}, "dependencies": {"asn1js": "^3.0.5", "pvtsutils": "^1.3.6", "tslib": "^2.8.1"}, "contributors": [{"email": "<EMAIL>", "name": "<PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}], "gitHead": "b6c7130efa9bc3ee5e2ea1da5d01aede5182921f"}