module.exports = {
  "mark.via.gg": "Via",
  "mark.via.gp": "Via",
  "mark.via.gq": "Via",
  "mark.via.pm": "Via",
  "mark.viah": "Via",
  "com.pure.mini.browser": "Pure Mini Browser",
  "pure.lite.browser": "Pure Lite Browser",
  "acr.browser.Hexa": "Hexa Web Browser",
  "acr.browser.raisebrowserfull": "Raise Fast Browser",
  "acr.tez.browse": "Browspeed Browser",
  "com.Fast.BrowserUc.lite": "Fast Browser UC Lite",
  "acr.browser.barebones": "Lightning Browser",
  "anar.app.darkweb": "Dark Web Browser",
  "com.darkbrowser": "Dark Browser",
  "com.kiwibrowser.browser": "Kiwi",
  "com.cloudmosa.puffinFree": "Puffin Web Browser",
  "com.cloudmosa.puffin": "Puffin Web Browser",
  "com.cloudmosa.puffinIncognito": "Puffin Incognito Browser",
  "com.cloudmosa.puffinCloudBrowser": "Puffin Cloud Browser",
  "com.aloha.browser": "Aloha Browser",
  "com.cake.browser": "Cake Browser",
  "com.UCMobile.intl": "UC Browser",
  "com.iebrowser.fast": "IE Browser Fast",
  "com.internet.browser.secure": "Internet Browser Secure",
  "acr.browser.linxy": "Vegas Browser",
  "com.oh.bro": "OH Browser",
  "com.oh.brop": "OH Private Browser",
  "com.duckduckgo.mobile.android": "DuckDuckGo Privacy Browser",
  "net.onecook.browser": "Stargon",
  "com.mi.globalbrowser.mini": "Mint Browser",
  "com.hisense.odinbrowser": "Odin Browser",
  "com.brave.browser": "Brave",
  "com.brave.browser_beta": "Brave",
  "org.mozilla.klar": "Firefox Klar",
  "phx.hot.browser": "Anka Browser",
  "com.anka.browser": "Anka Browser",
  "org.mozilla.focus": "Firefox Focus",
  "org.mozilla.tv.firefox": "Firefox Focus",
  "com.vivaldi.browser": "Vivaldi",
  "web.browser.dragon": "Dragon Browser",
  "org.easyweb.browser": "Easy Browser",
  "com.xbrowser.play": "XBrowser Mini",
  "com.sharkeeapp.browser": "Sharkee Browser",
  "com.mobiu.browser": "Lark Browser",
  "com.qflair.browserq": "Pluma",
  "com.noxgroup.app.browser": "Nox Browser",
  "com.jio.web": "JioSphere",
  "com.ume.browser.cust": "Ume Browser",
  "com.ume.browser.international": "Ume Browser",
  "com.ume.browser.bose": "Ume Browser",
  "com.ume.browser.euas": "Ume Browser",
  "com.ume.browser.latinamerican": "Ume Browser",
  "com.ume.browser.mexicotelcel": "Ume Browser",
  "com.ume.browser.venezuelavtelca": "Ume Browser",
  "com.ume.browser.northamerica": "Ume Browser",
  "com.ume.browser.newage": "Ume Browser",
  "com.kuto.browser": "KUTO Mini Browser",
  "com.dolphin.browser.zero": "Dolphin Zero",
  "mobi.mgeek.TunnyBrowser": "Dolphin",
  "nextapp.atlas": "Atlas",
  "org.mozilla.rocket": "Firefox Rocket",
  "com.mx.browser": "Maxthon",
  "com.ecosia.android": "Ecosia",
  "org.lineageos.jelly": "Jelly",
  "com.opera.gx": "Opera GX",
  "br.marcelo.monumentbrowser": "Monument Browser",
  "com.airfind.deltabrowser": "Delta Browser",
  "com.apusapps.browser": "APUS Browser",
  "com.ask.browser": "Ask.com",
  "com.browser.tssomas": "Super Fast Browser",
  "iron.web.jalepano.browser": "SuperFast Browser",
  "yuce.browser.mini": "Ui Browser Mini",
  "SavySoda.PrivateBrowsing": "SavySoda",
  "savannah.internet.web.browser": "Savannah Browser",
  "com.gl9.cloudBrowser": "Surf Browser",
  "com.ucold.browser.secure.browse": "UC Browser Mini",
  "com.mycompany.app.soulbrowser": "Soul Browser",
  "com.quickbig.browser": "Indian UC Mini Browser",
  "com.opera.browser": "Opera",
  "com.opera.mini.native": "Opera Mini",
  "com.wSilverMobBrowser": "SilverMob US",
  "com.ksmobile.cb": "CM Browser",
  "com.cmcm.armorfly": "Armorfly Browser",
  "org.mini.freebrowser": "CM Mini",
  "com.anc.web.browser": "Comfort Browser",
  "fast.explorer.web.browser": "Fast Explorer",
  "net.soti.surf": "SOTI Surf",
  "com.lexi.browser": "Lexi Browser",
  "com.browser.pintar": "Smart Browser",
  "com.belva.browser": "Belva Browser",
  "com.belva.safe.browser": "Belva Browser",
  "com.youcare.browser": "YouCare",
  "org.lilo.mobile.android2020": "Lilo",
  "com.opera.cryptobrowser": "Opera Crypto",
  "AlohaBrowser": "Aloha Browser",
  "mark.via": "Via",
  "com.xpp.floatbrowser": "Float Browser",
  "com.kiddoware.kidsafebrowser": "Kids Safe Browser",
  "com.hideitpro.vbrowser": "vBrowser",
  "com.cgbrowser.rn": "CG Browser",
  "com.azka.browser.anti.blokir": "Azka Browser",
  "com.azka.browser": "Azka Browser",
  "com.micromaxinfo.browser": "Mmx Browser",
  "com.zeesitech.bitchutebrowser": "Bitchute Browser",
  "nova.all.video.downloader": "Nova Video Downloader Pro",
  "tukidev.pronhubbrowser.tanpavpn": "PronHub Browser",
  "com.crowbar.beaverlite": "Frost",
  "com.crowbar.beaverbrowser": "Frost+",
  "com.lenovo.browser": "Lenovo Browser",
  "com.transsion.phoenix": "Phoenix Browser",
  "quick.browser.secure": "Quick Browser",
  "com.asus.browser": "Asus Browser",
  "com.opera.touch": "Opera Touch",
  "com.ghostery.android.ghostery": "Ghostery Privacy Browser",
  "com.oceanhero.search": "OceanHero",
  "com.mebrowser.webapp": "Me Browser",
  "info.plateaukao.einkbro": "EinkBro",
  "com.fevdev.nakedbrowser": "Naked Browser",
  "com.fevdev.nakedbrowserlts": "Naked Browser",
  "com.fevdev.nakedbrowserpro": "Naked Browser Pro",
  "com.yasirshakoor.ducbrowser": "DUC Browser",
  "com.wDesiBrowser_13255326": "Desi Browser",
  "com.huawei.browser": "Huawei Browser Mobile",
  "com.phantom.me": "Phantom.me",
  "com.opera.mini.android": "Opera Mini",
  "jp.ejimax.berrybrowser": "Berry Browser",
  "com.fulldive.mobile": "Fulldive",
  "com.talpa.hibrowser": "Hi Browser",
  "org.midorinext.android": "Midori Lite",
  "reactivephone.msearch": "Smart Search & Web Browser",
  "com.sibimobilelab.amazebrowser": "Amaze Browser",
  "com.alohamobile.browser.lite": "Aloha Browser Lite",
  "com.tcl.browser": "BrowseHere",
  "com.seraphic.openinet.pre": "Open Browser",
  "com.seraphic.openinet.cvte": "Open Browser",
  "privatebrowser.securebrowser.com.klar": "Secure Private Browser",
  "in.pokebbrowser.bukablokirsitus": "HUB Browser",
  "com.wOpenBrowser_12576500": "Open Browser fast 5G",
  "com.wOpenbrowser_13506467": "Open Browser 4U",
  "com.MaxTube.browser": "MaxTube Browser",
  "com.ninexgen.chowbo": "Chowbo",
  "net.pertiller.debuggablebrowser": "Debuggable Browser",
  "com.appssppa.idesktoppcbrowser": "iDesktop PC Browser",
  "pi.browser": "Pi Browser",
  "com.xooloo.internet": "Xooloo Internet",
  "com.u_browser": "U Browser",
  "ai.blokee.browser.android": "Bloket",
  "com.vast.vpn.proxy.unblock": "Vast Browser",
  "com.security.xvpn.z35kb": "X-VPN",
  "com.security.xvpn.z35kb.amazon": "X-VPN",
  "com.security.xvpn.z35kb.huawei": "X-VPN",
  "com.yandex.browser.lite": "Yandex Browser Lite",
  "com.yandex.browser.beta": "Yandex Browser",
  "com.yandex.browser.alpha": "Yandex Browser",
  "cz.seznam.sbrowser": "Seznam Browser",
  "com.morrisxar.nav88": "Office Browser",
  "com.rabbit.incognito.browser": "Rabbit Private Browser",
  "arun.com.chromer": "Lynket Browser",
  "jp.hazuki.yuzubrowser": "Yuzu Browser",
  "com.swiftariel.browser.cherry": "Cherry Browser",
  "id.browser.vivid3": "Vivid Browser Mini",
  "com.browser.yo.indian": "Yo Browser",
  "com.mercandalli.android.browser": "G Browser",
  "com.bf.browser": "BXE Browser",
  "com.qihoo.browser": "360 Secure Browser",
  "com.qihoo.contents": "360 Secure Browser",
  "com.qihoo.haosou": "360 Secure Browser",
  "com.qihoo.padbrowser": "360 Secure Browser",
  "com.qihoo.sonybrowser": "360 Secure Browser",
  "org.zirco": "Zirco Browser",
  "org.tint": "Tint Browser",
  "com.skyfire.browser": "Skyfire",
  "com.sonymobile.smallbrowser": "Sony Small Browser",
  "org.hola": "hola! Browser",
  "it.ideasolutions.amerigo": "Amerigo",
  "org.xbrowser.prosuperfast": "xBrowser Pro Super Fast",
  "org.plus18.android": "18+ Privacy Browser",
  "com.beyond.privatebrowser": "Beyond Private Browser",
  "com.blacklion.browser": "Black Lion Browser",
  "com.opera.mini.native.ShonizME": "Opera Mini",
  "com.tuc.mini.st": "TUC Mini Browser",
  "com.roidtechnologies.appbrowzer": "AppBrowzer",
  "com.futuristic.sx": "SX Browser",
  "hot.fiery.browser": "Fiery Browser",
  "in.nismah.yagi": "YAGI",
  "com.apn.mobile.browser.cherry": "APN Browser",
  "com.apn.mobile.browser.umeatt": "APN Browser",
  "com.apn.mobile.browser.zte": "APN Browser",
  "com.tencent.mtt": "QQ Browser",
  "com.wordly.translate.browser": "NextWord Browser",
  "idm.internet.download.manager": "1DM Browser",
  "idm.internet.download.manager.plus": "1DM+ Browser",
  "com.veeraapps.newadult": "Adult Browser",
  "com.xnxbrowser.rampage": "XNX Browser",
  "com.xtremecast": "XtremeCast",
  "com.xvideobrowserlite.xvideoDownloaderbrowserlite": "X Browser Lite",
  "com.xxnxx.browser.proxy.vpn": "xBrowser",
  "com.sweetbrowser.ice": "Sweet Browser",
  "com.mcent.browser": "mCent",
  "com.htc.sense.browser": "HTC Browser",
  "com.browlser": "Browlser",
  "app.browserhub.download": "Browser Hup Pro",
  "com.flyperinc.flyperlink": "Flyperlink",
  "com.w3engineers.banglabrowser": "Bangla Browser",
  "com.coccoc.trinhduyet": "Coc Coc",
  "com.browser.explore": "Explore Browser",
  "com.microsoft.emmx": "Microsoft Edge",
  "com.explore.web.browser": "Web Browser & Explorer",
  "privacy.explorer.fast.safe.browser": "Privacy Explorer Fast Safe",
  "app.soundy.browser": "Soundy Browser",
  "com.ivvi.browser": "IVVI Browser",
  "com.nomone.vrbrowser": "NOMone VR Browser",
  "com.opus.browser": "Opus Browser",
  "com.arvin.browser": "Arvin",
  "com.pawxy.browser": "Pawxy",
  "com.internet.tvbrowser": "LUJO TV Browser",
  "com.logicui.tvbrowser2": "LogicUI TV Browser",
  "com.opera.browser.afin": "Opera",
  "com.opera.browser.beta": "Opera",
  "com.quark.browser": "Quark",
  "jp.co.yahoo.android.ybrowser": "Yahoo! Japan Browser",
  "com.tv.browser.open": "Open TV Browser",
  "com.ornet.torbrowser": "OrNET Browser",
  "com.browsbit": "BrowsBit",
  "org.mozilla.firefox": "Firefox Mobile",
  "com.yandex.browser": "Yandex Browser",
  "com.opera.mini.native.beta": "Opera Mini",
  "com.sec.android.app.sbrowser": "Samsung Browser",
  "com.sec.android.app.sbrowser.lite": "Samsung Browser Lite",
  "com.browser.elmurzaev": "World Browser",
  "every.browser.inc": "Every Browser",
  "com.mi.globalbrowser": "Mi Browser",
  "nu.tommie.inbrowser": "InBrowser",
  "com.insta.browser": "Insta Browser",
  "com.alohamobile.vertexsurf": "Vertex Surf",
  "com.hollabrowser.meforce": "Holla Web Browser",
  "org.torbrowser.torproject": "Tor Browser",
  "org.torproject.torbrowser": "Tor Browser",
  "com.marslab.browserz": "MarsLab Web Browser",
  "com.mini.web.browser": "Sunflower Browser",
  "com.cavebrowser": "Cave Browser",
  "com.zordo.browser": "Zordo Browser",
  "freedom.theanarch.org.freedom": "Freedom Browser",
  "com.lechneralexander.privatebrowser": "Private Internet Browser",
  "com.airfind.browser": "Airfind Secure Browser",
  "com.securex.browser": "SecureX",
  "com.sec.android.app.sbrowser.beta": "Samsung Browser",
  "threads.thor": "Thor",
  "com.androidbull.incognito.browser": "Incognito Browser",
  "com.mosoft.godzilla": "Godzilla Browser",
  "com.oceanbrowser.mobile.android": "Ocean Browser",
  "com.qmamu.browser": "Qmamu",
  "com.techlastudio.bfbrowser": "BF Browser",
  "com.befaster.bfbrowser": "BF Browser",
  "app.nextinnovations.brokeep": "BroKeep Browser",
  "org.lilo.app": "Lilo",
  "proxy.browser.unblock.sites.proxybrowser.unblocksites": "Proxy Browser",
  "com.hotsurf.browser": "HotBrowser",
  "vpn.video.downloader": "VD Browser",
  "com.aospstudio.tvsearch": "Quick Search TV",
  "com.go.browser": "GO Browser",
  "com.apgsolutionsllc.APGSOLUTIONSLLC0007": "Basic Web Browser",
  "com.phlox.tvwebbrowser": "TV Bro",
  "com.lovense.vibemate": "VibeMate",
  "dev.sect.lotus.browser.videoapp": "Lotus",
  "com.qjy.browser": "QJY TV Browser",
  "com.airwatch.browser": "VMware AirWatch",
  "com.microsoft.intune.mam.managedbrowser": "Intune Managed Browser",
  "com.tencent.bang": "Bang",
  "com.outcoder.browser": "Surfy Browser",
  "ginxdroid.gdm": "GinxDroid Browser",
  "on.browser": "OnBrowser Lite",
  "com.pico.browser.overseas": "PICO Browser",
  "com.cliqz.browser": "Cliqz",
  "org.hola.prem": "hola! Browser",
  "com.baidu.browser.inter": "Baidu Browser",
  "org.torproject.torbrowser_alpha": "Tor Browser",
  "com.ilegendsoft.mercury": "Mercury",
  "com.apn.mobile.browser.coolpad": "APN Browser",
  "com.apn.mobile.browser.infosonics": "APN Browser",
  "net.dezor.browser": "Dezor",
  "com.involta.involtago": "Involta Go",
  "jp.ddo.pigsty.HabitBrowser": "Habit Browser",
  "org.browser.owl": "Owl Browser",
  "com.orbitum.browser": "Orbitum",
  "com.appsverse.photon": "Photon",
  "fr.agrange.bbbrowser": "Keyboard Browser",
  "com.stealthmobile.browser": "Stealth Browser",
  "com.wcd.talkto": "TalkTo",
  "com.foxylab.airfox": "Proxynet",
  "io.github.mthli.goodbrowser": "Good Browser",
  "app.proxyiumbrowser.android": "Proxyium",
  "com.vuhuv": "Vuhuv",
  "com.fast.fireBrowser": "Fire Browser",
  "acr.browser.lightning": "Lightning Browser Plus",
  "birapp.dark.web": "Dark Web",
  "birapp.dark.web.private": "Dark Web Private",
  "com.hihonor.baidu.browser": "HONOR Browser",
  "com.browser.pintar.vpn": "Pintar Browser",
  "com.ucimini.internetbrowser": "Browser Mini",
  "de.baumann.browser": "FOSS Browser",
  "com.oversea.mybrowser": "Peach Browser",
  "com.apptec360.android.browser": "AppTec Secure Browser",
  "com.ojr.browser.anti.blokir": "OJR Browser",
  "com.vworldc.tusk": "TUSK",
  "com.stoutner.privacybrowser.standard": "Privacy Browser",
  "com.techlastudio.proxyfoxbrowser": "ProxyFox",
  "com.vielianztlabs.browser": "ProxyMax",
  "com.keepsolid.privatebrowser": "KeepSolid Browser",
  "com.onionsearchengine.focus": "ONIONBrowser",
  "com.best.quick.browser": "Ai Browser",
  "miada.tv.webbrowser": "Internet Webbrowser",
  "com.kaweapp.webexplorer": "Web Explorer",
  "com.halo.browser": "Halo Browser",
  "com.mmbox.xbrowser": "MMBOX XBrowser",
  "com.tvwebbrowser.v22": "TV-Browser Internet",
  "com.tvwebbrowserpaid.v22": "TV-Browser Internet",
  "xnx.browser.browse.Xnxnewx": "XnBrowse",
  "com.metax.browser": "Open Browser Lite",
  "com.getkeepsafe.browser": "Keepsafe Browser",
  "com.hawk.android.browser": "Hawk Turbo Browser",
  "com.zte.nubrowser": "ZTE Browser",
  "com.cloaktp.browser": "Privacy Pioneer Browser",
  "company.thebrowser.arc": "Arc Search",
  "com.android.webview": "Chrome Webview"
};
