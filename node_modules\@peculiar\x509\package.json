{"name": "@peculiar/x509", "version": "1.12.3", "description": "@peculiar/x509 is an easy to use TypeScript/Javascript library based on @peculiar/asn1-schema that makes generating X.509 Certificates and Certificate Requests as well as validating certificate chains easy", "main": "build/x509.cjs.js", "module": "build/x509.es.js", "unpkg": "build/x509.js", "types": "build/index.d.ts", "scripts": {"test": "mocha", "lint": "eslint --ext .ts src/ test/", "lint:fix": "eslint --fix --ext .ts src/ test/", "build": "npm run build:module", "build:module": "rollup -c", "rebuild": "npm run clear && npm run build", "coverage": "nyc npm test", "coveralls": "nyc report --reporter=text-lcov | coveralls", "pub": "npm run pub:patch", "pub:patch": "npm version patch && npm run git:push", "pub:minor": "npm version minor && npm run git:push", "pub:major": "npm version major && npm run git:push", "git:push": "git push --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/PeculiarVentures/x509.git"}, "keywords": ["x509", "certificate", "csr", "pkcs10", "pkcs7", "cms", "rsa", "ec"], "author": "Peculiar Ventures LLC", "contributors": ["<PERSON><PERSON><PERSON><<EMAIL>>"], "license": "MIT", "bugs": {"url": "https://github.com/PeculiarVentures/x509/issues"}, "homepage": "https://github.com/PeculiarVentures/x509#readme", "files": ["build/**/*.{ts,js}", "README.md", "LICENSE"], "nyc": {"extension": [".ts", ".tsx"], "include": ["src/**/*.ts"], "exclude": ["**/*.d.ts"], "reporter": ["lcov", "text-summary"]}, "devDependencies": {"@babel/core": "^7.24.6", "@babel/preset-env": "^7.24.6", "@peculiar/webcrypto": "^1.5.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.8", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@types/mocha": "^10.0.6", "@types/node": "^20.12.12", "@typescript-eslint/eslint-plugin": "^7.11.0", "@typescript-eslint/parser": "^7.11.0", "eslint": "8.57.0", "mocha": "^10.7.3", "nyc": "^17.0.0", "rimraf": "^6.0.1", "rollup": "^4.18.0", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-typescript2": "^0.36.0", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "dependencies": {"@peculiar/asn1-cms": "^2.3.13", "@peculiar/asn1-csr": "^2.3.13", "@peculiar/asn1-ecc": "^2.3.14", "@peculiar/asn1-pkcs9": "^2.3.13", "@peculiar/asn1-rsa": "^2.3.13", "@peculiar/asn1-schema": "^2.3.13", "@peculiar/asn1-x509": "^2.3.13", "pvtsutils": "^1.3.5", "reflect-metadata": "^0.2.2", "tslib": "^2.7.0", "tsyringe": "^4.8.0"}}