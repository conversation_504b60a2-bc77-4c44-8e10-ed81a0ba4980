.TH "NPM\-TEST" "1" "March 2017" "" ""
.SH "NAME"
\fBnpm-test\fR \- Test a package
.SH SYNOPSIS
.P
.RS 2
.nf
  npm test [\-\- <args>]

  aliases: t, tst
.fi
.RE
.SH DESCRIPTION
.P
This runs a package's "test" script, if one was provided\.
.P
To run tests as a condition of installation, set the \fBnpat\fP config to
true\.
.SH SEE ALSO
.RS 0
.IP \(bu 2
npm help run\-script
.IP \(bu 2
npm help 7 scripts
.IP \(bu 2
npm help start
.IP \(bu 2
npm help restart
.IP \(bu 2
npm help stop

.RE

