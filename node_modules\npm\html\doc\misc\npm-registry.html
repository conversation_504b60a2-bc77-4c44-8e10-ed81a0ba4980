<!doctype html>
<html>
  <title>npm-registry</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/misc/npm-registry.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../misc/npm-registry.html">npm-registry</a></h1> <p>The JavaScript Package Registry</p>
<h2 id="description">DESCRIPTION</h2>
<p>To resolve packages by name and version, npm talks to a registry website
that implements the CommonJS Package Registry specification for reading
package info.</p>
<p>Additionally, npm&#39;s package registry implementation supports several
write APIs as well, to allow for publishing packages and managing user
account information.</p>
<p>The official public npm registry is at <a href="https://registry.npmjs.org/">https://registry.npmjs.org/</a>.  It
is powered by a CouchDB database, of which there is a public mirror at
<a href="https://skimdb.npmjs.com/registry">https://skimdb.npmjs.com/registry</a>.  The code for the couchapp is
available at <a href="https://github.com/npm/npm-registry-couchapp">https://github.com/npm/npm-registry-couchapp</a>.</p>
<p>The registry URL used is determined by the scope of the package (see
<code><a href="../misc/npm-scope.html">npm-scope(7)</a></code>). If no scope is specified, the default registry is used, which is
supplied by the <code>registry</code> config parameter.  See <code><a href="../cli/npm-config.html">npm-config(1)</a></code>,
<code><a href="../files/npmrc.html">npmrc(5)</a></code>, and <code><a href="../misc/npm-config.html">npm-config(7)</a></code> for more on managing npm&#39;s configuration.</p>
<h2 id="can-i-run-my-own-private-registry-">Can I run my own private registry?</h2>
<p>Yes!</p>
<p>The easiest way is to replicate the couch database, and use the same (or
similar) design doc to implement the APIs.</p>
<p>If you set up continuous replication from the official CouchDB, and then
set your internal CouchDB as the registry config, then you&#39;ll be able
to read any published packages, in addition to your private ones, and by
default will only publish internally. </p>
<p>If you then want to publish a package for the whole world to see, you can
simply override the <code>--registry</code> option for that <code>publish</code> command.</p>
<h2 id="i-don-t-want-my-package-published-in-the-official-registry-it-s-private-">I don&#39;t want my package published in the official registry. It&#39;s private.</h2>
<p>Set <code>&quot;private&quot;: true</code> in your package.json to prevent it from being
published at all, or
<code>&quot;publishConfig&quot;:{&quot;registry&quot;:&quot;http://my-internal-registry.local&quot;}</code>
to force it to be published only to your internal registry.</p>
<p>See <code><a href="../files/package.json.html">package.json(5)</a></code> for more info on what goes in the package.json file.</p>
<h2 id="will-you-replicate-from-my-registry-into-the-public-one-">Will you replicate from my registry into the public one?</h2>
<p>No.  If you want things to be public, then publish them into the public
registry using npm.  What little security there is would be for nought
otherwise.</p>
<h2 id="do-i-have-to-use-couchdb-to-build-a-registry-that-npm-can-talk-to-">Do I have to use couchdb to build a registry that npm can talk to?</h2>
<p>No, but it&#39;s way easier.  Basically, yes, you do, or you have to
effectively implement the entire CouchDB API anyway.</p>
<h2 id="is-there-a-website-or-something-to-see-package-docs-and-such-">Is there a website or something to see package docs and such?</h2>
<p>Yes, head over to <a href="https://npmjs.com/">https://npmjs.com/</a></p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../files/npmrc.html">npmrc(5)</a></li>
<li><a href="../misc/npm-developers.html">npm-developers(7)</a></li>
<li><a href="../misc/npm-disputes.html">npm-disputes(7)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-registry &mdash; npm@2.15.12</p>

