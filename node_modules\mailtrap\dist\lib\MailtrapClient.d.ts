import GeneralAPI from "./api/General";
import TestingAPI from "./api/Testing";
import { Mail, SendResponse, MailtrapClientConfig } from "../types/mailtrap";
/**
 * Mailtrap client class. Initializes instance with available methods.
 */
export default class MailtrapClient {
    private axios;
    private testInboxId?;
    private accountId?;
    private bulk;
    private sandbox;
    /**
     * Initalizes axios instance with Mailtrap params.
     */
    constructor({ token, testInboxId, accountId, bulk, sandbox, }: MailtrapClientConfig);
    /**
     * Getter for Testing API. Warns if some of the required keys are missing.
     */
    get testing(): TestingAPI;
    /**
     * Getter for General API.
     */
    get general(): GeneralAPI;
    /**
     * Returns configured host. Checks if `bulk` and `sandbox` modes are activated simultaneously,
     *   then reject with Mailtrap Error.
     * Otherwise returns appropriate host url.
     */
    private determineHost;
    /**
     * Sends mail with given `mail` params. If there is error, rejects with `MailtrapError`.
     */
    send(mail: Mail): Promise<SendResponse>;
}
