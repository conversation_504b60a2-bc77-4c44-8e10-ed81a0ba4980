<!doctype html>
<html>
  <title>npm-disputes</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/misc/npm-disputes.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../misc/npm-disputes.html">npm-disputes</a></h1> <p>Handling Module Name Disputes</p>
<h2 id="synopsis">SYNOPSIS</h2>
<ol>
<li>Get the author email with <code>npm owner ls &lt;pkgname&gt;</code></li>
<li>Email the author, CC <a href="&#109;&#x61;&#x69;&#108;&#x74;&#111;&#x3a;&#x73;&#117;&#x70;&#112;&#111;&#114;&#116;&#64;&#x6e;&#112;&#x6d;&#106;&#115;&#46;&#x63;&#x6f;&#109;">&#x73;&#117;&#x70;&#112;&#111;&#114;&#116;&#64;&#x6e;&#112;&#x6d;&#106;&#115;&#46;&#x63;&#x6f;&#109;</a></li>
<li>After a few weeks, if there&#39;s no resolution, we&#39;ll sort it out.</li>
</ol>
<p>Don&#39;t squat on package names.  Publish code or move out of the way.</p>
<h2 id="description">DESCRIPTION</h2>
<p>There sometimes arise cases where a user publishes a module, and then
later, some other user wants to use that name.  Here are some common
ways that happens (each of these is based on actual events.)</p>
<ol>
<li>Joe writes a JavaScript module <code>foo</code>, which is not node-specific.
Joe doesn&#39;t use node at all.  Bob   wants to use <code>foo</code> in node, so he
wraps it in an npm module.  Some time later, Joe starts using node,
and wants to take over management of his program.</li>
<li>Bob writes an npm module <code>foo</code>, and publishes it.  Perhaps much
later, Joe finds a bug in <code>foo</code>, and fixes it.  He sends a pull
request to Bob, but Bob doesn&#39;t have the time to deal with it,
because he has a new job and a new baby and is focused on his new
erlang project, and kind of not involved with node any more.  Joe
would like to publish a new <code>foo</code>, but can&#39;t, because the name is
taken.</li>
<li>Bob writes a 10-line flow-control library, and calls it <code>foo</code>, and
publishes it to the npm registry.  Being a simple little thing, it
never really has to be updated.  Joe works for Foo Inc, the makers
of the critically acclaimed and widely-marketed <code>foo</code> JavaScript
toolkit framework.  They publish it to npm as <code>foojs</code>, but people are
routinely confused when <code>npm install foo</code> is some different thing.</li>
<li>Bob writes a parser for the widely-known <code>foo</code> file format, because
he needs it for work.  Then, he gets a new job, and never updates the
prototype.  Later on, Joe writes a much more complete <code>foo</code> parser,
but can&#39;t publish, because Bob&#39;s <code>foo</code> is in the way.</li>
</ol>
<p>The validity of Joe&#39;s claim in each situation can be debated.  However,
Joe&#39;s appropriate course of action in each case is the same.</p>
<ol>
<li><code>npm owner ls foo</code>.  This will tell Joe the email address of the
owner (Bob).</li>
<li>Joe emails Bob, explaining the situation <strong>as respectfully as
possible</strong>, and what he would like to do with the module name.  He
adds the npm support staff <a href="&#x6d;&#x61;&#x69;&#x6c;&#x74;&#x6f;&#x3a;&#x73;&#x75;&#112;&#x70;&#x6f;&#114;&#x74;&#64;&#x6e;&#112;&#109;&#x6a;&#115;&#46;&#99;&#111;&#x6d;">&#x73;&#x75;&#112;&#x70;&#x6f;&#114;&#x74;&#64;&#x6e;&#112;&#109;&#x6a;&#115;&#46;&#99;&#111;&#x6d;</a> to the CC list of
the email.  Mention in the email that Bob can run <code>npm owner add
joe foo</code> to add Joe as an owner of the <code>foo</code> package.</li>
<li>After a reasonable amount of time, if Bob has not responded, or if
Bob and Joe can&#39;t come to any sort of resolution, email support
<a href="&#109;&#97;&#105;&#x6c;&#116;&#x6f;&#x3a;&#115;&#117;&#112;&#112;&#x6f;&#x72;&#116;&#64;&#110;&#x70;&#109;&#x6a;&#x73;&#46;&#99;&#111;&#x6d;">&#115;&#117;&#112;&#112;&#x6f;&#x72;&#116;&#64;&#110;&#x70;&#109;&#x6a;&#x73;&#46;&#99;&#111;&#x6d;</a> and we&#39;ll sort it out.  (&quot;Reasonable&quot; is
usually at least 4 weeks, but extra time is allowed around common
holidays.)</li>
</ol>
<h2 id="reasoning">REASONING</h2>
<p>In almost every case so far, the parties involved have been able to reach
an amicable resolution without any major intervention.  Most people
really do want to be reasonable, and are probably not even aware that
they&#39;re in your way.</p>
<p>Module ecosystems are most vibrant and powerful when they are as
self-directed as possible.  If an admin one day deletes something you
had worked on, then that is going to make most people quite upset,
regardless of the justification.  When humans solve their problems by
talking to other humans with respect, everyone has the chance to end up
feeling good about the interaction.</p>
<h2 id="exceptions">EXCEPTIONS</h2>
<p>Some things are not allowed, and will be removed without discussion if
they are brought to the attention of the npm registry admins, including
but not limited to:</p>
<ol>
<li>Malware (that is, a package designed to exploit or harm the machine on
which it is installed).</li>
<li>Violations of copyright or licenses (for example, cloning an
MIT-licensed program, and then removing or changing the copyright and
license statement).</li>
<li>Illegal content.</li>
<li>&quot;Squatting&quot; on a package name that you <em>plan</em> to use, but aren&#39;t
actually using.  Sorry, I don&#39;t care how great the name is, or how
perfect a fit it is for the thing that someday might happen.  If
someone wants to use it today, and you&#39;re just taking up space with
an empty tarball, you&#39;re going to be evicted.</li>
<li>Putting empty packages in the registry.  Packages must have SOME
functionality.  It can be silly, but it can&#39;t be <em>nothing</em>.  (See
also: squatting.)</li>
<li>Doing weird things with the registry, like using it as your own
personal application database or otherwise putting non-packagey
things into it.</li>
</ol>
<p>If you see bad behavior like this, please report it right away.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../misc/npm-registry.html">npm-registry(7)</a></li>
<li><a href="../cli/npm-owner.html">npm-owner(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-disputes &mdash; npm@2.15.12</p>

