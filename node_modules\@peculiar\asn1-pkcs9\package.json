{"name": "@peculiar/asn1-pkcs9", "version": "2.3.15", "description": "ASN.1 schema based on PKCS#9 (RFC 2985)", "keywords": ["asn", "pkcs9", "rfc2985"], "author": "PeculiarVentures, LLC", "license": "MIT", "main": "build/cjs/index.js", "files": ["build/**/*.{js,d.ts}", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/PeculiarVentures/asn1-schema.git"}, "scripts": {"test": "mocha", "clear": "<PERSON><PERSON><PERSON> build", "build": "npm run build:module && npm run build:types", "build:module": "npm run build:cjs && npm run build:es2015", "build:cjs": "tsc -p tsconfig.compile.json --removeComments --module commonjs --outDir build/cjs", "build:es2015": "tsc -p tsconfig.compile.json --removeComments --module ES2015 --outDir build/es2015", "prebuild:types": "rimraf build/types", "build:types": "tsc -p tsconfig.compile.json --outDir build/types --declaration --emitDeclarationOnly", "rebuild": "npm run clear && npm run build"}, "dependencies": {"@peculiar/asn1-cms": "^2.3.15", "@peculiar/asn1-pfx": "^2.3.15", "@peculiar/asn1-pkcs8": "^2.3.15", "@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "@peculiar/asn1-x509-attr": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}, "bugs": {"url": "https://github.com/PeculiarVentures/asn1-schema/issues"}, "homepage": "https://github.com/PeculiarVentures/asn1-schema#readme", "module": "build/es2015/index.js", "types": "build/types/index.d.ts", "gitHead": "b6c7130efa9bc3ee5e2ea1da5d01aede5182921f"}