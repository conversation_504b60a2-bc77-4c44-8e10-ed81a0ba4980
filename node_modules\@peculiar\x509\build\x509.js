/*!
 * MIT License
 * 
 * Copyright (c) Peculiar Ventures. All rights reserved.
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 * 
 */
var x509=function(e){"use strict";var t,r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};
/*! *****************************************************************************
	Copyright (C) Microsoft. All rights reserved.
	Licensed under the Apache License, Version 2.0 (the "License"); you may not use
	this file except in compliance with the License. You may obtain a copy of the
	License at http://www.apache.org/licenses/LICENSE-2.0

	THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
	KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
	WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
	MERCHANTABLITY OR NON-INFRINGEMENT.

	See the Apache Version 2.0 License for specific language governing permissions
	and limitations under the License.
	***************************************************************************** */!function(e){!function(){var t="object"==typeof globalThis?globalThis:"object"==typeof r?r:"object"==typeof self?self:"object"==typeof this?this:function(){try{return Function("return this;")()}catch(e){}}()||function(){try{return(0,eval)("(function() { return this; })()")}catch(e){}}(),i=n(e);function n(e,t){return function(r,i){Object.defineProperty(e,r,{configurable:!0,writable:!0,value:i}),t&&t(r,i)}}void 0!==t.Reflect&&(i=n(t.Reflect,i)),function(e,t){var r=Object.prototype.hasOwnProperty,i="function"==typeof Symbol,n=i&&void 0!==Symbol.toPrimitive?Symbol.toPrimitive:"@@toPrimitive",s=i&&void 0!==Symbol.iterator?Symbol.iterator:"@@iterator",o="function"==typeof Object.create,a={__proto__:[]}instanceof Array,c=!o&&!a,u={create:o?function(){return pe(Object.create(null))}:a?function(){return pe({__proto__:null})}:function(){return pe({})},has:c?function(e,t){return r.call(e,t)}:function(e,t){return t in e},get:c?function(e,t){return r.call(e,t)?e[t]:void 0}:function(e,t){return e[t]}},l=Object.getPrototypeOf(Function),p="function"==typeof Map&&"function"==typeof Map.prototype.entries?Map:ce(),h="function"==typeof Set&&"function"==typeof Set.prototype.entries?Set:ue(),f="function"==typeof WeakMap?WeakMap:le(),y=i?Symbol.for("@reflect-metadata:registry"):void 0,d=ne(),g=se(d);function v(e,t,r,i){if(V(r)){if(!q(e))throw new TypeError;if(!G(t))throw new TypeError;return C(e,t)}if(!q(e))throw new TypeError;if(!$(t))throw new TypeError;if(!$(i)&&!V(i)&&!L(i))throw new TypeError;return L(i)&&(i=void 0),I(e,t,r=_(r),i)}function m(e,t){function r(r,i){if(!$(r))throw new TypeError;if(!V(i)&&!J(i))throw new TypeError;P(e,t,r,i)}return r}function w(e,t,r,i){if(!$(r))throw new TypeError;return V(i)||(i=_(i)),P(e,t,r,i)}function b(e,t,r){if(!$(t))throw new TypeError;return V(r)||(r=_(r)),O(e,t,r)}function A(e,t,r){if(!$(t))throw new TypeError;return V(r)||(r=_(r)),N(e,t,r)}function S(e,t,r){if(!$(t))throw new TypeError;return V(r)||(r=_(r)),T(e,t,r)}function x(e,t,r){if(!$(t))throw new TypeError;return V(r)||(r=_(r)),j(e,t,r)}function B(e,t){if(!$(e))throw new TypeError;return V(t)||(t=_(t)),R(e,t)}function k(e,t){if(!$(e))throw new TypeError;return V(t)||(t=_(t)),U(e,t)}function E(e,t,r){if(!$(t))throw new TypeError;if(V(r)||(r=_(r)),!$(t))throw new TypeError;V(r)||(r=_(r));var i=ae(t,r,!1);return!V(i)&&i.OrdinaryDeleteMetadata(e,t,r)}function C(e,t){for(var r=e.length-1;r>=0;--r){var i=(0,e[r])(t);if(!V(i)&&!L(i)){if(!G(i))throw new TypeError;t=i}}return t}function I(e,t,r,i){for(var n=e.length-1;n>=0;--n){var s=(0,e[n])(t,r,i);if(!V(s)&&!L(s)){if(!$(s))throw new TypeError;i=s}}return i}function O(e,t,r){if(N(e,t,r))return!0;var i=re(t);return!L(i)&&O(e,i,r)}function N(e,t,r){var i=ae(t,r,!1);return!V(i)&&K(i.OrdinaryHasOwnMetadata(e,t,r))}function T(e,t,r){if(N(e,t,r))return j(e,t,r);var i=re(t);return L(i)?void 0:T(e,i,r)}function j(e,t,r){var i=ae(t,r,!1);if(!V(i))return i.OrdinaryGetOwnMetadata(e,t,r)}function P(e,t,r,i){ae(r,i,!0).OrdinaryDefineOwnMetadata(e,t,r,i)}function R(e,t){var r=U(e,t),i=re(e);if(null===i)return r;var n=R(i,t);if(n.length<=0)return r;if(r.length<=0)return n;for(var s=new h,o=[],a=0,c=r;a<c.length;a++){var u=c[a];s.has(u)||(s.add(u),o.push(u))}for(var l=0,p=n;l<p.length;l++){u=p[l];s.has(u)||(s.add(u),o.push(u))}return o}function U(e,t){var r=ae(e,t,!1);return r?r.OrdinaryOwnMetadataKeys(e,t):[]}function D(e){if(null===e)return 1;switch(typeof e){case"undefined":return 0;case"boolean":return 2;case"string":return 3;case"symbol":return 4;case"number":return 5;case"object":return null===e?1:6;default:return 6}}function V(e){return void 0===e}function L(e){return null===e}function H(e){return"symbol"==typeof e}function $(e){return"object"==typeof e?null!==e:"function"==typeof e}function M(e,t){switch(D(e)){case 0:case 1:case 2:case 3:case 4:case 5:return e}var r="string",i=Z(e,n);if(void 0!==i){var s=i.call(e,r);if($(s))throw new TypeError;return s}return F(e)}function F(e,t){var r,i,n=e.toString;if(W(n)&&!$(i=n.call(e)))return i;if(W(r=e.valueOf)&&!$(i=r.call(e)))return i;throw new TypeError}function K(e){return!!e}function z(e){return""+e}function _(e){var t=M(e);return H(t)?t:z(t)}function q(e){return Array.isArray?Array.isArray(e):e instanceof Object?e instanceof Array:"[object Array]"===Object.prototype.toString.call(e)}function W(e){return"function"==typeof e}function G(e){return"function"==typeof e}function J(e){switch(D(e)){case 3:case 4:return!0;default:return!1}}function X(e,t){return e===t||e!=e&&t!=t}function Z(e,t){var r=e[t];if(null!=r){if(!W(r))throw new TypeError;return r}}function Y(e){var t=Z(e,s);if(!W(t))throw new TypeError;var r=t.call(e);if(!$(r))throw new TypeError;return r}function Q(e){return e.value}function ee(e){var t=e.next();return!t.done&&t}function te(e){var t=e.return;t&&t.call(e)}function re(e){var t=Object.getPrototypeOf(e);if("function"!=typeof e||e===l)return t;if(t!==l)return t;var r=e.prototype,i=r&&Object.getPrototypeOf(r);if(null==i||i===Object.prototype)return t;var n=i.constructor;return"function"!=typeof n||n===e?t:n}function ie(){var e,r,i,n;V(y)||void 0===t.Reflect||y in t.Reflect||"function"!=typeof t.Reflect.defineMetadata||(e=oe(t.Reflect));var s=new f,o={registerProvider:a,getProvider:u,setProvider:d};return o;function a(t){if(!Object.isExtensible(o))throw new Error("Cannot add provider to a frozen registry.");switch(!0){case e===t:break;case V(r):r=t;break;case r===t:break;case V(i):i=t;break;case i===t:break;default:void 0===n&&(n=new h),n.add(t)}}function c(t,s){if(!V(r)){if(r.isProviderFor(t,s))return r;if(!V(i)){if(i.isProviderFor(t,s))return r;if(!V(n))for(var o=Y(n);;){var a=ee(o);if(!a)return;var c=Q(a);if(c.isProviderFor(t,s))return te(o),c}}}if(!V(e)&&e.isProviderFor(t,s))return e}function u(e,t){var r,i=s.get(e);return V(i)||(r=i.get(t)),V(r)?(V(r=c(e,t))||(V(i)&&(i=new p,s.set(e,i)),i.set(t,r)),r):r}function l(e){if(V(e))throw new TypeError;return r===e||i===e||!V(n)&&n.has(e)}function d(e,t,r){if(!l(r))throw new Error("Metadata provider not registered.");var i=u(e,t);if(i!==r){if(!V(i))return!1;var n=s.get(e);V(n)&&(n=new p,s.set(e,n)),n.set(t,r)}return!0}}function ne(){var e;return!V(y)&&$(t.Reflect)&&Object.isExtensible(t.Reflect)&&(e=t.Reflect[y]),V(e)&&(e=ie()),!V(y)&&$(t.Reflect)&&Object.isExtensible(t.Reflect)&&Object.defineProperty(t.Reflect,y,{enumerable:!1,configurable:!1,writable:!1,value:e}),e}function se(e){var t=new f,r={isProviderFor:function(e,r){var i=t.get(e);return!V(i)&&i.has(r)},OrdinaryDefineOwnMetadata:o,OrdinaryHasOwnMetadata:n,OrdinaryGetOwnMetadata:s,OrdinaryOwnMetadataKeys:a,OrdinaryDeleteMetadata:c};return d.registerProvider(r),r;function i(i,n,s){var o=t.get(i),a=!1;if(V(o)){if(!s)return;o=new p,t.set(i,o),a=!0}var c=o.get(n);if(V(c)){if(!s)return;if(c=new p,o.set(n,c),!e.setProvider(i,n,r))throw o.delete(n),a&&t.delete(i),new Error("Wrong provider for target.")}return c}function n(e,t,r){var n=i(t,r,!1);return!V(n)&&K(n.has(e))}function s(e,t,r){var n=i(t,r,!1);if(!V(n))return n.get(e)}function o(e,t,r,n){i(r,n,!0).set(e,t)}function a(e,t){var r=[],n=i(e,t,!1);if(V(n))return r;for(var s=Y(n.keys()),o=0;;){var a=ee(s);if(!a)return r.length=o,r;var c=Q(a);try{r[o]=c}catch(e){try{te(s)}finally{throw e}}o++}}function c(e,r,n){var s=i(r,n,!1);if(V(s))return!1;if(!s.delete(e))return!1;if(0===s.size){var o=t.get(r);V(o)||(o.delete(n),0===o.size&&t.delete(o))}return!0}}function oe(e){var t=e.defineMetadata,r=e.hasOwnMetadata,i=e.getOwnMetadata,n=e.getOwnMetadataKeys,s=e.deleteMetadata,o=new f;return{isProviderFor:function(e,t){var r=o.get(e);return!(V(r)||!r.has(t))||!!n(e,t).length&&(V(r)&&(r=new h,o.set(e,r)),r.add(t),!0)},OrdinaryDefineOwnMetadata:t,OrdinaryHasOwnMetadata:r,OrdinaryGetOwnMetadata:i,OrdinaryOwnMetadataKeys:n,OrdinaryDeleteMetadata:s}}function ae(e,t,r){var i=d.getProvider(e,t);if(!V(i))return i;if(r){if(d.setProvider(e,t,g))return g;throw new Error("Illegal state.")}}function ce(){var e={},t=[],r=function(){function e(e,t,r){this._index=0,this._keys=e,this._values=t,this._selector=r}return e.prototype["@@iterator"]=function(){return this},e.prototype[s]=function(){return this},e.prototype.next=function(){var e=this._index;if(e>=0&&e<this._keys.length){var r=this._selector(this._keys[e],this._values[e]);return e+1>=this._keys.length?(this._index=-1,this._keys=t,this._values=t):this._index++,{value:r,done:!1}}return{value:void 0,done:!0}},e.prototype.throw=function(e){throw this._index>=0&&(this._index=-1,this._keys=t,this._values=t),e},e.prototype.return=function(e){return this._index>=0&&(this._index=-1,this._keys=t,this._values=t),{value:e,done:!0}},e}();return function(){function t(){this._keys=[],this._values=[],this._cacheKey=e,this._cacheIndex=-2}return Object.defineProperty(t.prototype,"size",{get:function(){return this._keys.length},enumerable:!0,configurable:!0}),t.prototype.has=function(e){return this._find(e,!1)>=0},t.prototype.get=function(e){var t=this._find(e,!1);return t>=0?this._values[t]:void 0},t.prototype.set=function(e,t){var r=this._find(e,!0);return this._values[r]=t,this},t.prototype.delete=function(t){var r=this._find(t,!1);if(r>=0){for(var i=this._keys.length,n=r+1;n<i;n++)this._keys[n-1]=this._keys[n],this._values[n-1]=this._values[n];return this._keys.length--,this._values.length--,X(t,this._cacheKey)&&(this._cacheKey=e,this._cacheIndex=-2),!0}return!1},t.prototype.clear=function(){this._keys.length=0,this._values.length=0,this._cacheKey=e,this._cacheIndex=-2},t.prototype.keys=function(){return new r(this._keys,this._values,i)},t.prototype.values=function(){return new r(this._keys,this._values,n)},t.prototype.entries=function(){return new r(this._keys,this._values,o)},t.prototype["@@iterator"]=function(){return this.entries()},t.prototype[s]=function(){return this.entries()},t.prototype._find=function(e,t){if(!X(this._cacheKey,e)){this._cacheIndex=-1;for(var r=0;r<this._keys.length;r++)if(X(this._keys[r],e)){this._cacheIndex=r;break}}return this._cacheIndex<0&&t&&(this._cacheIndex=this._keys.length,this._keys.push(e),this._values.push(void 0)),this._cacheIndex},t}();function i(e,t){return e}function n(e,t){return t}function o(e,t){return[e,t]}}function ue(){return function(){function e(){this._map=new p}return Object.defineProperty(e.prototype,"size",{get:function(){return this._map.size},enumerable:!0,configurable:!0}),e.prototype.has=function(e){return this._map.has(e)},e.prototype.add=function(e){return this._map.set(e,e),this},e.prototype.delete=function(e){return this._map.delete(e)},e.prototype.clear=function(){this._map.clear()},e.prototype.keys=function(){return this._map.keys()},e.prototype.values=function(){return this._map.keys()},e.prototype.entries=function(){return this._map.entries()},e.prototype["@@iterator"]=function(){return this.keys()},e.prototype[s]=function(){return this.keys()},e}()}function le(){var e=16,t=u.create(),i=n();return function(){function e(){this._key=n()}return e.prototype.has=function(e){var t=s(e,!1);return void 0!==t&&u.has(t,this._key)},e.prototype.get=function(e){var t=s(e,!1);return void 0!==t?u.get(t,this._key):void 0},e.prototype.set=function(e,t){return s(e,!0)[this._key]=t,this},e.prototype.delete=function(e){var t=s(e,!1);return void 0!==t&&delete t[this._key]},e.prototype.clear=function(){this._key=n()},e}();function n(){var e;do{e="@@WeakMap@@"+c()}while(u.has(t,e));return t[e]=!0,e}function s(e,t){if(!r.call(e,i)){if(!t)return;Object.defineProperty(e,i,{value:u.create()})}return e[i]}function o(e,t){for(var r=0;r<t;++r)e[r]=255*Math.random()|0;return e}function a(e){if("function"==typeof Uint8Array){var t=new Uint8Array(e);return"undefined"!=typeof crypto?crypto.getRandomValues(t):"undefined"!=typeof msCrypto?msCrypto.getRandomValues(t):o(t,e),t}return o(new Array(e),e)}function c(){var t=a(e);t[6]=79&t[6]|64,t[8]=191&t[8]|128;for(var r="",i=0;i<e;++i){var n=t[i];4!==i&&6!==i&&8!==i||(r+="-"),n<16&&(r+="0"),r+=n.toString(16).toLowerCase()}return r}}function pe(e){return e.__=void 0,delete e.__,e}e("decorate",v),e("metadata",m),e("defineMetadata",w),e("hasMetadata",b),e("hasOwnMetadata",A),e("getMetadata",S),e("getOwnMetadata",x),e("getMetadataKeys",B),e("getOwnMetadataKeys",k),e("deleteMetadata",E)}(i,t),void 0===t.Reflect&&(t.Reflect=e)}()}(t||(t={}));class i{static isArrayBuffer(e){return"[object ArrayBuffer]"===Object.prototype.toString.call(e)}static toArrayBuffer(e){return this.isArrayBuffer(e)?e:e.byteLength===e.buffer.byteLength||0===e.byteOffset&&e.byteLength===e.buffer.byteLength?e.buffer:this.toUint8Array(e.buffer).slice(e.byteOffset,e.byteOffset+e.byteLength).buffer}static toUint8Array(e){return this.toView(e,Uint8Array)}static toView(e,t){if(e.constructor===t)return e;if(this.isArrayBuffer(e))return new t(e);if(this.isArrayBufferView(e))return new t(e.buffer,e.byteOffset,e.byteLength);throw new TypeError("The provided value is not of type '(ArrayBuffer or ArrayBufferView)'")}static isBufferSource(e){return this.isArrayBufferView(e)||this.isArrayBuffer(e)}static isArrayBufferView(e){return ArrayBuffer.isView(e)||e&&this.isArrayBuffer(e.buffer)}static isEqual(e,t){const r=i.toUint8Array(e),n=i.toUint8Array(t);if(r.length!==n.byteLength)return!1;for(let e=0;e<r.length;e++)if(r[e]!==n[e])return!1;return!0}static concat(...e){let t;t=!Array.isArray(e[0])||e[1]instanceof Function?Array.isArray(e[0])&&e[1]instanceof Function?e[0]:e[e.length-1]instanceof Function?e.slice(0,e.length-1):e:e[0];let r=0;for(const e of t)r+=e.byteLength;const i=new Uint8Array(r);let n=0;for(const e of t){const t=this.toUint8Array(e);i.set(t,n),n+=t.length}return e[e.length-1]instanceof Function?this.toView(i,e[e.length-1]):i.buffer}}const n="string",s=/^[0-9a-f]+$/i,o=/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/,a=/^[a-zA-Z0-9-_]+$/;class c{static fromString(e){const t=unescape(encodeURIComponent(e)),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r.buffer}static toString(e){const t=i.toUint8Array(e);let r="";for(let e=0;e<t.length;e++)r+=String.fromCharCode(t[e]);return decodeURIComponent(escape(r))}}class u{static toString(e,t=!1){const r=i.toArrayBuffer(e),n=new DataView(r);let s="";for(let e=0;e<r.byteLength;e+=2){const r=n.getUint16(e,t);s+=String.fromCharCode(r)}return s}static fromString(e,t=!1){const r=new ArrayBuffer(2*e.length),i=new DataView(r);for(let r=0;r<e.length;r++)i.setUint16(2*r,e.charCodeAt(r),t);return r}}class l{static isHex(e){return typeof e===n&&s.test(e)}static isBase64(e){return typeof e===n&&o.test(e)}static isBase64Url(e){return typeof e===n&&a.test(e)}static ToString(e,t="utf8"){const r=i.toUint8Array(e);switch(t.toLowerCase()){case"utf8":return this.ToUtf8String(r);case"binary":return this.ToBinary(r);case"hex":return this.ToHex(r);case"base64":return this.ToBase64(r);case"base64url":return this.ToBase64Url(r);case"utf16le":return u.toString(r,!0);case"utf16":case"utf16be":return u.toString(r);default:throw new Error(`Unknown type of encoding '${t}'`)}}static FromString(e,t="utf8"){if(!e)return new ArrayBuffer(0);switch(t.toLowerCase()){case"utf8":return this.FromUtf8String(e);case"binary":return this.FromBinary(e);case"hex":return this.FromHex(e);case"base64":return this.FromBase64(e);case"base64url":return this.FromBase64Url(e);case"utf16le":return u.fromString(e,!0);case"utf16":case"utf16be":return u.fromString(e);default:throw new Error(`Unknown type of encoding '${t}'`)}}static ToBase64(e){const t=i.toUint8Array(e);if("undefined"!=typeof btoa){const e=this.ToString(t,"binary");return btoa(e)}return Buffer.from(t).toString("base64")}static FromBase64(e){const t=this.formatString(e);if(!t)return new ArrayBuffer(0);if(!l.isBase64(t))throw new TypeError("Argument 'base64Text' is not Base64 encoded");return"undefined"!=typeof atob?this.FromBinary(atob(t)):new Uint8Array(Buffer.from(t,"base64")).buffer}static FromBase64Url(e){const t=this.formatString(e);if(!t)return new ArrayBuffer(0);if(!l.isBase64Url(t))throw new TypeError("Argument 'base64url' is not Base64Url encoded");return this.FromBase64(this.Base64Padding(t.replace(/\-/g,"+").replace(/\_/g,"/")))}static ToBase64Url(e){return this.ToBase64(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/\=/g,"")}static FromUtf8String(e,t=l.DEFAULT_UTF8_ENCODING){switch(t){case"ascii":return this.FromBinary(e);case"utf8":return c.fromString(e);case"utf16":case"utf16be":return u.fromString(e);case"utf16le":case"usc2":return u.fromString(e,!0);default:throw new Error(`Unknown type of encoding '${t}'`)}}static ToUtf8String(e,t=l.DEFAULT_UTF8_ENCODING){switch(t){case"ascii":return this.ToBinary(e);case"utf8":return c.toString(e);case"utf16":case"utf16be":return u.toString(e);case"utf16le":case"usc2":return u.toString(e,!0);default:throw new Error(`Unknown type of encoding '${t}'`)}}static FromBinary(e){const t=e.length,r=new Uint8Array(t);for(let i=0;i<t;i++)r[i]=e.charCodeAt(i);return r.buffer}static ToBinary(e){const t=i.toUint8Array(e);let r="";for(let e=0;e<t.length;e++)r+=String.fromCharCode(t[e]);return r}static ToHex(e){const t=i.toUint8Array(e);let r="";const n=t.length;for(let e=0;e<n;e++){const i=t[e];i<16&&(r+="0"),r+=i.toString(16)}return r}static FromHex(e){let t=this.formatString(e);if(!t)return new ArrayBuffer(0);if(!l.isHex(t))throw new TypeError("Argument 'hexString' is not HEX encoded");t.length%2&&(t=`0${t}`);const r=new Uint8Array(t.length/2);for(let e=0;e<t.length;e+=2){const i=t.slice(e,e+2);r[e/2]=parseInt(i,16)}return r.buffer}static ToUtf16String(e,t=!1){return u.toString(e,t)}static FromUtf16String(e,t=!1){return u.fromString(e,t)}static Base64Padding(e){const t=4-e.length%4;if(t<4)for(let r=0;r<t;r++)e+="=";return e}static formatString(e){return(null==e?void 0:e.replace(/[\n\r\t ]/g,""))||""}}function p(e,t){if(!e||!t)return!1;if(e.byteLength!==t.byteLength)return!1;const r=new Uint8Array(e),i=new Uint8Array(t);for(let t=0;t<e.byteLength;t++)if(r[t]!==i[t])return!1;return!0}
/*!
	 Copyright (c) Peculiar Ventures, LLC
	*/function h(e,t){let r=0;if(1===e.length)return e[0];for(let i=e.length-1;i>=0;i--)r+=e[e.length-1-i]*Math.pow(2,t*i);return r}function f(e,t,r=-1){const i=r;let n=e,s=0,o=Math.pow(2,t);for(let r=1;r<8;r++){if(e<o){let e;if(i<0)e=new ArrayBuffer(r),s=r;else{if(i<r)return new ArrayBuffer(0);e=new ArrayBuffer(i),s=i}const o=new Uint8Array(e);for(let e=r-1;e>=0;e--){const r=Math.pow(2,e*t);o[s-e-1]=Math.floor(n/r),n-=o[s-e-1]*r}return e}o*=Math.pow(2,t)}return new ArrayBuffer(0)}function y(...e){let t=0,r=0;for(const r of e)t+=r.length;const i=new ArrayBuffer(t),n=new Uint8Array(i);for(const t of e)n.set(t,r),r+=t.length;return n}function d(){const e=new Uint8Array(this.valueHex);if(this.valueHex.byteLength>=2){const t=255===e[0]&&128&e[1],r=0===e[0]&&!(128&e[1]);(t||r)&&this.warnings.push("Needlessly long format")}const t=new ArrayBuffer(this.valueHex.byteLength),r=new Uint8Array(t);for(let e=0;e<this.valueHex.byteLength;e++)r[e]=0;r[0]=128&e[0];const i=h(r,8),n=new ArrayBuffer(this.valueHex.byteLength),s=new Uint8Array(n);for(let t=0;t<this.valueHex.byteLength;t++)s[t]=e[t];s[0]&=127;return h(s,8)-i}function g(e,t){const r=e.toString(10);if(t<r.length)return"";const i=t-r.length,n=new Array(i);for(let e=0;e<i;e++)n[e]="0";return n.join("").concat(r)}
/*!
	 * Copyright (c) 2014, GMO GlobalSign
	 * Copyright (c) 2015-2022, Peculiar Ventures
	 * All rights reserved.
	 * 
	 * Author 2014-2019, Yury Strozhevsky
	 * 
	 * Redistribution and use in source and binary forms, with or without modification,
	 * are permitted provided that the following conditions are met:
	 * 
	 * * Redistributions of source code must retain the above copyright notice, this
	 *   list of conditions and the following disclaimer.
	 * 
	 * * Redistributions in binary form must reproduce the above copyright notice, this
	 *   list of conditions and the following disclaimer in the documentation and/or
	 *   other materials provided with the distribution.
	 * 
	 * * Neither the name of the copyright holder nor the names of its
	 *   contributors may be used to endorse or promote products derived from
	 *   this software without specific prior written permission.
	 * 
	 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
	 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
	 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
	 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR
	 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
	 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
	 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
	 * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
	 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
	 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
	 * 
	 */function v(){if("undefined"==typeof BigInt)throw new Error("BigInt is not defined. Your environment doesn't implement BigInt.")}function m(e){let t=0,r=0;for(let r=0;r<e.length;r++){t+=e[r].byteLength}const i=new Uint8Array(t);for(let t=0;t<e.length;t++){const n=e[t];i.set(new Uint8Array(n),r),r+=n.byteLength}return i.buffer}function w(e,t,r,i){return t instanceof Uint8Array?t.byteLength?r<0?(e.error="Wrong parameter: inputOffset less than zero",!1):i<0?(e.error="Wrong parameter: inputLength less than zero",!1):!(t.byteLength-r-i<0)||(e.error="End of input reached before message was fully decoded (inconsistent offset and length values)",!1):(e.error="Wrong parameter: inputBuffer has zero length",!1):(e.error="Wrong parameter: inputBuffer must be 'Uint8Array'",!1)}l.DEFAULT_UTF8_ENCODING="utf8";class b{constructor(){this.items=[]}write(e){this.items.push(e)}final(){return m(this.items)}}const A=[new Uint8Array([1])],S="0123456789",x="name",B="valueHexView",k="",E=new ArrayBuffer(0),C=new Uint8Array(0),I="EndOfContent",O="OCTET STRING",N="BIT STRING";function T(e){var t;return(t=class extends e{constructor(...e){var t;super(...e);const r=e[0]||{};this.isHexOnly=null!==(t=r.isHexOnly)&&void 0!==t&&t,this.valueHexView=r.valueHex?i.toUint8Array(r.valueHex):C}get valueHex(){return this.valueHexView.slice().buffer}set valueHex(e){this.valueHexView=new Uint8Array(e)}fromBER(e,t,r){const i=e instanceof ArrayBuffer?new Uint8Array(e):e;if(!w(this,i,t,r))return-1;const n=t+r;return this.valueHexView=i.subarray(t,n),this.valueHexView.length?(this.blockLength=r,n):(this.warnings.push("Zero buffer length"),t)}toBER(e=!1){return this.isHexOnly?e?new ArrayBuffer(this.valueHexView.byteLength):this.valueHexView.byteLength===this.valueHexView.buffer.byteLength?this.valueHexView.buffer:this.valueHexView.slice().buffer:(this.error="Flag 'isHexOnly' is not set, abort",E)}toJSON(){return{...super.toJSON(),isHexOnly:this.isHexOnly,valueHex:l.ToHex(this.valueHexView)}}}).NAME="hexBlock",t}class j{constructor({blockLength:e=0,error:t="",warnings:r=[],valueBeforeDecode:n=C}={}){this.blockLength=e,this.error=t,this.warnings=r,this.valueBeforeDecodeView=i.toUint8Array(n)}static blockName(){return this.NAME}get valueBeforeDecode(){return this.valueBeforeDecodeView.slice().buffer}set valueBeforeDecode(e){this.valueBeforeDecodeView=new Uint8Array(e)}toJSON(){return{blockName:this.constructor.NAME,blockLength:this.blockLength,error:this.error,warnings:this.warnings,valueBeforeDecode:l.ToHex(this.valueBeforeDecodeView)}}}j.NAME="baseBlock";class P extends j{fromBER(e,t,r){throw TypeError("User need to make a specific function in a class which extends 'ValueBlock'")}toBER(e,t){throw TypeError("User need to make a specific function in a class which extends 'ValueBlock'")}}P.NAME="valueBlock";class R extends(T(j)){constructor({idBlock:e={}}={}){var t,r,n,s;super(),e?(this.isHexOnly=null!==(t=e.isHexOnly)&&void 0!==t&&t,this.valueHexView=e.valueHex?i.toUint8Array(e.valueHex):C,this.tagClass=null!==(r=e.tagClass)&&void 0!==r?r:-1,this.tagNumber=null!==(n=e.tagNumber)&&void 0!==n?n:-1,this.isConstructed=null!==(s=e.isConstructed)&&void 0!==s&&s):(this.tagClass=-1,this.tagNumber=-1,this.isConstructed=!1)}toBER(e=!1){let t=0;switch(this.tagClass){case 1:t|=0;break;case 2:t|=64;break;case 3:t|=128;break;case 4:t|=192;break;default:return this.error="Unknown tag class",E}if(this.isConstructed&&(t|=32),this.tagNumber<31&&!this.isHexOnly){const r=new Uint8Array(1);if(!e){let e=this.tagNumber;e&=31,t|=e,r[0]=t}return r.buffer}if(!this.isHexOnly){const r=f(this.tagNumber,7),i=new Uint8Array(r),n=r.byteLength,s=new Uint8Array(n+1);if(s[0]=31|t,!e){for(let e=0;e<n-1;e++)s[e+1]=128|i[e];s[n]=i[n-1]}return s.buffer}const r=new Uint8Array(this.valueHexView.byteLength+1);if(r[0]=31|t,!e){const e=this.valueHexView;for(let t=0;t<e.length-1;t++)r[t+1]=128|e[t];r[this.valueHexView.byteLength]=e[e.length-1]}return r.buffer}fromBER(e,t,r){const n=i.toUint8Array(e);if(!w(this,n,t,r))return-1;const s=n.subarray(t,t+r);if(0===s.length)return this.error="Zero buffer length",-1;switch(192&s[0]){case 0:this.tagClass=1;break;case 64:this.tagClass=2;break;case 128:this.tagClass=3;break;case 192:this.tagClass=4;break;default:return this.error="Unknown tag class",-1}this.isConstructed=!(32&~s[0]),this.isHexOnly=!1;const o=31&s[0];if(31!==o)this.tagNumber=o,this.blockLength=1;else{let e=1,t=this.valueHexView=new Uint8Array(255),r=255;for(;128&s[e];){if(t[e-1]=127&s[e],e++,e>=s.length)return this.error="End of input reached before message was fully decoded",-1;if(e===r){r+=255;const e=new Uint8Array(r);for(let r=0;r<t.length;r++)e[r]=t[r];t=this.valueHexView=new Uint8Array(r)}}this.blockLength=e+1,t[e-1]=127&s[e];const i=new Uint8Array(e);for(let r=0;r<e;r++)i[r]=t[r];t=this.valueHexView=new Uint8Array(e),t.set(i),this.blockLength<=9?this.tagNumber=h(t,7):(this.isHexOnly=!0,this.warnings.push("Tag too long, represented as hex-coded"))}if(1===this.tagClass&&this.isConstructed)switch(this.tagNumber){case 1:case 2:case 5:case 6:case 9:case 13:case 14:case 23:case 24:case 31:case 32:case 33:case 34:return this.error="Constructed encoding used for primitive type",-1}return t+this.blockLength}toJSON(){return{...super.toJSON(),tagClass:this.tagClass,tagNumber:this.tagNumber,isConstructed:this.isConstructed}}}R.NAME="identificationBlock";class U extends j{constructor({lenBlock:e={}}={}){var t,r,i;super(),this.isIndefiniteForm=null!==(t=e.isIndefiniteForm)&&void 0!==t&&t,this.longFormUsed=null!==(r=e.longFormUsed)&&void 0!==r&&r,this.length=null!==(i=e.length)&&void 0!==i?i:0}fromBER(e,t,r){const n=i.toUint8Array(e);if(!w(this,n,t,r))return-1;const s=n.subarray(t,t+r);if(0===s.length)return this.error="Zero buffer length",-1;if(255===s[0])return this.error="Length block 0xFF is reserved by standard",-1;if(this.isIndefiniteForm=128===s[0],this.isIndefiniteForm)return this.blockLength=1,t+this.blockLength;if(this.longFormUsed=!!(128&s[0]),!1===this.longFormUsed)return this.length=s[0],this.blockLength=1,t+this.blockLength;const o=127&s[0];if(o>8)return this.error="Too big integer",-1;if(o+1>s.length)return this.error="End of input reached before message was fully decoded",-1;const a=t+1,c=n.subarray(a,a+o);return 0===c[o-1]&&this.warnings.push("Needlessly long encoded length"),this.length=h(c,8),this.longFormUsed&&this.length<=127&&this.warnings.push("Unnecessary usage of long length form"),this.blockLength=o+1,t+this.blockLength}toBER(e=!1){let t,r;if(this.length>127&&(this.longFormUsed=!0),this.isIndefiniteForm)return t=new ArrayBuffer(1),!1===e&&(r=new Uint8Array(t),r[0]=128),t;if(this.longFormUsed){const i=f(this.length,8);if(i.byteLength>127)return this.error="Too big length",E;if(t=new ArrayBuffer(i.byteLength+1),e)return t;const n=new Uint8Array(i);r=new Uint8Array(t),r[0]=128|i.byteLength;for(let e=0;e<i.byteLength;e++)r[e+1]=n[e];return t}return t=new ArrayBuffer(1),!1===e&&(r=new Uint8Array(t),r[0]=this.length),t}toJSON(){return{...super.toJSON(),isIndefiniteForm:this.isIndefiniteForm,longFormUsed:this.longFormUsed,length:this.length}}}U.NAME="lengthBlock";const D={};class V extends j{constructor({name:e="",optional:t=!1,primitiveSchema:r,...i}={},n){super(i),this.name=e,this.optional=t,r&&(this.primitiveSchema=r),this.idBlock=new R(i),this.lenBlock=new U(i),this.valueBlock=n?new n(i):new P(i)}fromBER(e,t,r){const i=this.valueBlock.fromBER(e,t,this.lenBlock.isIndefiniteForm?r:this.lenBlock.length);return-1===i?(this.error=this.valueBlock.error,i):(this.idBlock.error.length||(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length||(this.blockLength+=this.lenBlock.blockLength),this.valueBlock.error.length||(this.blockLength+=this.valueBlock.blockLength),i)}toBER(e,t){const r=t||new b;t||L(this);const i=this.idBlock.toBER(e);if(r.write(i),this.lenBlock.isIndefiniteForm)r.write(new Uint8Array([128]).buffer),this.valueBlock.toBER(e,r),r.write(new ArrayBuffer(2));else{const t=this.valueBlock.toBER(e);this.lenBlock.length=t.byteLength;const i=this.lenBlock.toBER(e);r.write(i),r.write(t)}return t?E:r.final()}toJSON(){const e={...super.toJSON(),idBlock:this.idBlock.toJSON(),lenBlock:this.lenBlock.toJSON(),valueBlock:this.valueBlock.toJSON(),name:this.name,optional:this.optional};return this.primitiveSchema&&(e.primitiveSchema=this.primitiveSchema.toJSON()),e}toString(e="ascii"){return"ascii"===e?this.onAsciiEncoding():l.ToHex(this.toBER())}onAsciiEncoding(){return`${this.constructor.NAME} : ${l.ToHex(this.valueBlock.valueBeforeDecodeView)}`}isEqual(e){if(this===e)return!0;if(!(e instanceof this.constructor))return!1;return function(e,t){if(e.byteLength!==t.byteLength)return!1;const r=new Uint8Array(e),i=new Uint8Array(t);for(let e=0;e<r.length;e++)if(r[e]!==i[e])return!1;return!0}(this.toBER(),e.toBER())}}function L(e){if(e instanceof D.Constructed)for(const t of e.valueBlock.value)L(t)&&(e.lenBlock.isIndefiniteForm=!0);return!!e.lenBlock.isIndefiniteForm}V.NAME="BaseBlock";class H extends V{constructor({value:e="",...t}={},r){super(t,r),e&&this.fromString(e)}getValue(){return this.valueBlock.value}setValue(e){this.valueBlock.value=e}fromBER(e,t,r){const i=this.valueBlock.fromBER(e,t,this.lenBlock.isIndefiniteForm?r:this.lenBlock.length);return-1===i?(this.error=this.valueBlock.error,i):(this.fromBuffer(this.valueBlock.valueHexView),this.idBlock.error.length||(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length||(this.blockLength+=this.lenBlock.blockLength),this.valueBlock.error.length||(this.blockLength+=this.valueBlock.blockLength),i)}onAsciiEncoding(){return`${this.constructor.NAME} : '${this.valueBlock.value}'`}}H.NAME="BaseStringBlock";class $ extends(T(P)){constructor({isHexOnly:e=!0,...t}={}){super(t),this.isHexOnly=e}}var M,F,K,z,_,q;$.NAME="PrimitiveValueBlock";class W extends V{constructor(e={}){super(e,$),this.idBlock.isConstructed=!1}}function G(e,t=0,r=e.length){const i=t;let n=new V({},P);const s=new j;if(!w(s,e,t,r))return n.error=s.error,{offset:-1,result:n};if(!e.subarray(t,t+r).length)return n.error="Zero buffer length",{offset:-1,result:n};let o=n.idBlock.fromBER(e,t,r);if(n.idBlock.warnings.length&&n.warnings.concat(n.idBlock.warnings),-1===o)return n.error=n.idBlock.error,{offset:-1,result:n};if(t=o,r-=n.idBlock.blockLength,o=n.lenBlock.fromBER(e,t,r),n.lenBlock.warnings.length&&n.warnings.concat(n.lenBlock.warnings),-1===o)return n.error=n.lenBlock.error,{offset:-1,result:n};if(t=o,r-=n.lenBlock.blockLength,!n.idBlock.isConstructed&&n.lenBlock.isIndefiniteForm)return n.error="Indefinite length form used for primitive encoding form",{offset:-1,result:n};let a=V;if(1===n.idBlock.tagClass){if(n.idBlock.tagNumber>=37&&!1===n.idBlock.isHexOnly)return n.error="UNIVERSAL 37 and upper tags are reserved by ASN.1 standard",{offset:-1,result:n};switch(n.idBlock.tagNumber){case 0:if(n.idBlock.isConstructed&&n.lenBlock.length>0)return n.error="Type [UNIVERSAL 0] is reserved",{offset:-1,result:n};a=D.EndOfContent;break;case 1:a=D.Boolean;break;case 2:a=D.Integer;break;case 3:a=D.BitString;break;case 4:a=D.OctetString;break;case 5:a=D.Null;break;case 6:a=D.ObjectIdentifier;break;case 10:a=D.Enumerated;break;case 12:a=D.Utf8String;break;case 13:a=D.RelativeObjectIdentifier;break;case 14:a=D.TIME;break;case 15:return n.error="[UNIVERSAL 15] is reserved by ASN.1 standard",{offset:-1,result:n};case 16:a=D.Sequence;break;case 17:a=D.Set;break;case 18:a=D.NumericString;break;case 19:a=D.PrintableString;break;case 20:a=D.TeletexString;break;case 21:a=D.VideotexString;break;case 22:a=D.IA5String;break;case 23:a=D.UTCTime;break;case 24:a=D.GeneralizedTime;break;case 25:a=D.GraphicString;break;case 26:a=D.VisibleString;break;case 27:a=D.GeneralString;break;case 28:a=D.UniversalString;break;case 29:a=D.CharacterString;break;case 30:a=D.BmpString;break;case 31:a=D.DATE;break;case 32:a=D.TimeOfDay;break;case 33:a=D.DateTime;break;case 34:a=D.Duration;break;default:{const e=n.idBlock.isConstructed?new D.Constructed:new D.Primitive;e.idBlock=n.idBlock,e.lenBlock=n.lenBlock,e.warnings=n.warnings,n=e}}}else a=n.idBlock.isConstructed?D.Constructed:D.Primitive;return n=function(e,t){if(e instanceof t)return e;const r=new t;return r.idBlock=e.idBlock,r.lenBlock=e.lenBlock,r.warnings=e.warnings,r.valueBeforeDecodeView=e.valueBeforeDecodeView,r}(n,a),o=n.fromBER(e,t,n.lenBlock.isIndefiniteForm?r:n.lenBlock.length),n.valueBeforeDecodeView=e.subarray(i,i+n.blockLength),{offset:o,result:n}}function J(e){if(!e.byteLength){const e=new V({},P);return e.error="Input buffer has zero length",{offset:-1,result:e}}return G(i.toUint8Array(e).slice(),0,e.byteLength)}M=W,D.Primitive=M,W.NAME="PRIMITIVE";class X extends P{constructor({value:e=[],isIndefiniteForm:t=!1,...r}={}){super(r),this.value=e,this.isIndefiniteForm=t}fromBER(e,t,r){const n=i.toUint8Array(e);if(!w(this,n,t,r))return-1;if(this.valueBeforeDecodeView=n.subarray(t,t+r),0===this.valueBeforeDecodeView.length)return this.warnings.push("Zero buffer length"),t;let s=t;for(;o=this.isIndefiniteForm,a=r,(o?1:a)>0;){const e=G(n,s,r);if(-1===e.offset)return this.error=e.result.error,this.warnings.concat(e.result.warnings),-1;if(s=e.offset,this.blockLength+=e.result.blockLength,r-=e.result.blockLength,this.value.push(e.result),this.isIndefiniteForm&&e.result.constructor.NAME===I)break}var o,a;return this.isIndefiniteForm&&(this.value[this.value.length-1].constructor.NAME===I?this.value.pop():this.warnings.push("No EndOfContent block encoded")),s}toBER(e,t){const r=t||new b;for(let t=0;t<this.value.length;t++)this.value[t].toBER(e,r);return t?E:r.final()}toJSON(){const e={...super.toJSON(),isIndefiniteForm:this.isIndefiniteForm,value:[]};for(const t of this.value)e.value.push(t.toJSON());return e}}X.NAME="ConstructedValueBlock";class Z extends V{constructor(e={}){super(e,X),this.idBlock.isConstructed=!0}fromBER(e,t,r){this.valueBlock.isIndefiniteForm=this.lenBlock.isIndefiniteForm;const i=this.valueBlock.fromBER(e,t,this.lenBlock.isIndefiniteForm?r:this.lenBlock.length);return-1===i?(this.error=this.valueBlock.error,i):(this.idBlock.error.length||(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length||(this.blockLength+=this.lenBlock.blockLength),this.valueBlock.error.length||(this.blockLength+=this.valueBlock.blockLength),i)}onAsciiEncoding(){const e=[];for(const t of this.valueBlock.value)e.push(t.toString("ascii").split("\n").map((e=>`  ${e}`)).join("\n"));const t=3===this.idBlock.tagClass?`[${this.idBlock.tagNumber}]`:this.constructor.NAME;return e.length?`${t} :\n${e.join("\n")}`:`${t} :`}}F=Z,D.Constructed=F,Z.NAME="CONSTRUCTED";class Y extends P{fromBER(e,t,r){return t}toBER(e){return E}}Y.override="EndOfContentValueBlock";class Q extends V{constructor(e={}){super(e,Y),this.idBlock.tagClass=1,this.idBlock.tagNumber=0}}K=Q,D.EndOfContent=K,Q.NAME=I;class ee extends V{constructor(e={}){super(e,P),this.idBlock.tagClass=1,this.idBlock.tagNumber=5}fromBER(e,t,r){return this.lenBlock.length>0&&this.warnings.push("Non-zero length of value block for Null type"),this.idBlock.error.length||(this.blockLength+=this.idBlock.blockLength),this.lenBlock.error.length||(this.blockLength+=this.lenBlock.blockLength),this.blockLength+=r,t+r>e.byteLength?(this.error="End of input reached before message was fully decoded (inconsistent offset and length values)",-1):t+r}toBER(e,t){const r=new ArrayBuffer(2);if(!e){const e=new Uint8Array(r);e[0]=5,e[1]=0}return t&&t.write(r),r}onAsciiEncoding(){return`${this.constructor.NAME}`}}z=ee,D.Null=z,ee.NAME="NULL";class te extends(T(P)){constructor({value:e,...t}={}){super(t),t.valueHex?this.valueHexView=i.toUint8Array(t.valueHex):this.valueHexView=new Uint8Array(1),e&&(this.value=e)}get value(){for(const e of this.valueHexView)if(e>0)return!0;return!1}set value(e){this.valueHexView[0]=e?255:0}fromBER(e,t,r){const n=i.toUint8Array(e);return w(this,n,t,r)?(this.valueHexView=n.subarray(t,t+r),r>1&&this.warnings.push("Boolean value encoded in more then 1 octet"),this.isHexOnly=!0,d.call(this),this.blockLength=r,t+r):-1}toBER(){return this.valueHexView.slice()}toJSON(){return{...super.toJSON(),value:this.value}}}te.NAME="BooleanValueBlock";class re extends V{constructor(e={}){super(e,te),this.idBlock.tagClass=1,this.idBlock.tagNumber=1}getValue(){return this.valueBlock.value}setValue(e){this.valueBlock.value=e}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.getValue}`}}_=re,D.Boolean=_,re.NAME="BOOLEAN";class ie extends(T(X)){constructor({isConstructed:e=!1,...t}={}){super(t),this.isConstructed=e}fromBER(e,t,r){let i=0;if(this.isConstructed){if(this.isHexOnly=!1,i=X.prototype.fromBER.call(this,e,t,r),-1===i)return i;for(let e=0;e<this.value.length;e++){const t=this.value[e].constructor.NAME;if(t===I){if(this.isIndefiniteForm)break;return this.error="EndOfContent is unexpected, OCTET STRING may consists of OCTET STRINGs only",-1}if(t!==O)return this.error="OCTET STRING may consists of OCTET STRINGs only",-1}}else this.isHexOnly=!0,i=super.fromBER(e,t,r),this.blockLength=r;return i}toBER(e,t){return this.isConstructed?X.prototype.toBER.call(this,e,t):e?new ArrayBuffer(this.valueHexView.byteLength):this.valueHexView.slice().buffer}toJSON(){return{...super.toJSON(),isConstructed:this.isConstructed}}}ie.NAME="OctetStringValueBlock";let ne=class e extends V{constructor({idBlock:e={},lenBlock:t={},...r}={}){var i,n;null!==(i=r.isConstructed)&&void 0!==i||(r.isConstructed=!!(null===(n=r.value)||void 0===n?void 0:n.length)),super({idBlock:{isConstructed:r.isConstructed,...e},lenBlock:{...t,isIndefiniteForm:!!r.isIndefiniteForm},...r},ie),this.idBlock.tagClass=1,this.idBlock.tagNumber=4}fromBER(e,t,r){if(this.valueBlock.isConstructed=this.idBlock.isConstructed,this.valueBlock.isIndefiniteForm=this.lenBlock.isIndefiniteForm,0===r)return 0===this.idBlock.error.length&&(this.blockLength+=this.idBlock.blockLength),0===this.lenBlock.error.length&&(this.blockLength+=this.lenBlock.blockLength),t;if(!this.valueBlock.isConstructed){const i=(e instanceof ArrayBuffer?new Uint8Array(e):e).subarray(t,t+r);try{if(i.byteLength){const e=G(i,0,i.byteLength);-1!==e.offset&&e.offset===r&&(this.valueBlock.value=[e.result])}}catch(e){}}return super.fromBER(e,t,r)}onAsciiEncoding(){return this.valueBlock.isConstructed||this.valueBlock.value&&this.valueBlock.value.length?Z.prototype.onAsciiEncoding.call(this):`${this.constructor.NAME} : ${l.ToHex(this.valueBlock.valueHexView)}`}getValue(){if(!this.idBlock.isConstructed)return this.valueBlock.valueHexView.slice().buffer;const t=[];for(const r of this.valueBlock.value)r instanceof e&&t.push(r.valueBlock.valueHexView);return i.concat(t)}};q=ne,D.OctetString=q,ne.NAME=O;class se extends(T(X)){constructor({unusedBits:e=0,isConstructed:t=!1,...r}={}){super(r),this.unusedBits=e,this.isConstructed=t,this.blockLength=this.valueHexView.byteLength}fromBER(e,t,r){if(!r)return t;let n=-1;if(this.isConstructed){if(n=X.prototype.fromBER.call(this,e,t,r),-1===n)return n;for(const e of this.value){const t=e.constructor.NAME;if(t===I){if(this.isIndefiniteForm)break;return this.error="EndOfContent is unexpected, BIT STRING may consists of BIT STRINGs only",-1}if(t!==N)return this.error="BIT STRING may consists of BIT STRINGs only",-1;const r=e.valueBlock;if(this.unusedBits>0&&r.unusedBits>0)return this.error='Using of "unused bits" inside constructive BIT STRING allowed for least one only',-1;this.unusedBits=r.unusedBits}return n}const s=i.toUint8Array(e);if(!w(this,s,t,r))return-1;const o=s.subarray(t,t+r);if(this.unusedBits=o[0],this.unusedBits>7)return this.error="Unused bits for BitString must be in range 0-7",-1;if(!this.unusedBits){const e=o.subarray(1);try{if(e.byteLength){const t=G(e,0,e.byteLength);-1!==t.offset&&t.offset===r-1&&(this.value=[t.result])}}catch(e){}}return this.valueHexView=o.subarray(1),this.blockLength=o.length,t+r}toBER(e,t){if(this.isConstructed)return X.prototype.toBER.call(this,e,t);if(e)return new ArrayBuffer(this.valueHexView.byteLength+1);if(!this.valueHexView.byteLength)return E;const r=new Uint8Array(this.valueHexView.length+1);return r[0]=this.unusedBits,r.set(this.valueHexView,1),r.buffer}toJSON(){return{...super.toJSON(),unusedBits:this.unusedBits,isConstructed:this.isConstructed}}}var oe;se.NAME="BitStringValueBlock";let ae=class extends V{constructor({idBlock:e={},lenBlock:t={},...r}={}){var i,n;null!==(i=r.isConstructed)&&void 0!==i||(r.isConstructed=!!(null===(n=r.value)||void 0===n?void 0:n.length)),super({idBlock:{isConstructed:r.isConstructed,...e},lenBlock:{...t,isIndefiniteForm:!!r.isIndefiniteForm},...r},se),this.idBlock.tagClass=1,this.idBlock.tagNumber=3}fromBER(e,t,r){return this.valueBlock.isConstructed=this.idBlock.isConstructed,this.valueBlock.isIndefiniteForm=this.lenBlock.isIndefiniteForm,super.fromBER(e,t,r)}onAsciiEncoding(){if(this.valueBlock.isConstructed||this.valueBlock.value&&this.valueBlock.value.length)return Z.prototype.onAsciiEncoding.call(this);{const e=[],t=this.valueBlock.valueHexView;for(const r of t)e.push(r.toString(2).padStart(8,"0"));const r=e.join("");return`${this.constructor.NAME} : ${r.substring(0,r.length-this.valueBlock.unusedBits)}`}}};var ce,ue,le,pe,he,fe,ye;function de(e,t){const r=new Uint8Array([0]),i=new Uint8Array(e),n=new Uint8Array(t);let s=i.slice(0);const o=s.length-1,a=n.slice(0),c=a.length-1;let u=0;let l=0;for(let e=c<o?o:c;e>=0;e--,l++){if(!0==l<a.length)u=s[o-l]+a[c-l]+r[0];else u=s[o-l]+r[0];if(r[0]=u/10,!0==l>=s.length)s=y(new Uint8Array([u%10]),s);else s[o-l]=u%10}return r[0]>0&&(s=y(r,s)),s}function ge(e){if(e>=A.length)for(let t=A.length;t<=e;t++){const e=new Uint8Array([0]);let r=A[t-1].slice(0);for(let t=r.length-1;t>=0;t--){const i=new Uint8Array([(r[t]<<1)+e[0]]);e[0]=i[0]/10,r[t]=i[0]%10}e[0]>0&&(r=y(e,r)),A.push(r)}return A[e]}function ve(e,t){let r=0;const i=new Uint8Array(e),n=new Uint8Array(t),s=i.slice(0),o=s.length-1,a=n.slice(0),c=a.length-1;let u,l=0;for(let e=c;e>=0;e--,l++)if(u=s[o-l]-a[c-l]-r,!0==u<0)r=1,s[o-l]=u+10;else r=0,s[o-l]=u;if(r>0)for(let e=o-c+1;e>=0;e--,l++){if(u=s[o-l]-r,!(u<0)){r=0,s[o-l]=u;break}r=1,s[o-l]=u+10}return s.slice()}oe=ae,D.BitString=oe,ae.NAME=N;class me extends(T(P)){constructor({value:e,...t}={}){super(t),this._valueDec=0,t.valueHex&&this.setValueHex(),void 0!==e&&(this.valueDec=e)}setValueHex(){this.valueHexView.length>=4?(this.warnings.push("Too big Integer for decoding, hex only"),this.isHexOnly=!0,this._valueDec=0):(this.isHexOnly=!1,this.valueHexView.length>0&&(this._valueDec=d.call(this)))}set valueDec(e){this._valueDec=e,this.isHexOnly=!1,this.valueHexView=new Uint8Array(function(e){const t=e<0?-1*e:e;let r=128;for(let i=1;i<8;i++){if(t<=r){if(e<0){const e=f(r-t,8,i);return new Uint8Array(e)[0]|=128,e}let n=f(t,8,i),s=new Uint8Array(n);if(128&s[0]){const e=n.slice(0),t=new Uint8Array(e);n=new ArrayBuffer(n.byteLength+1),s=new Uint8Array(n);for(let r=0;r<e.byteLength;r++)s[r+1]=t[r];s[0]=0}return n}r*=Math.pow(2,8)}return new ArrayBuffer(0)}(e))}get valueDec(){return this._valueDec}fromDER(e,t,r,i=0){const n=this.fromBER(e,t,r);if(-1===n)return n;const s=this.valueHexView;return 0===s[0]&&128&s[1]?this.valueHexView=s.subarray(1):0!==i&&s.length<i&&(i-s.length>1&&(i=s.length+1),this.valueHexView=s.subarray(i-s.length)),n}toDER(e=!1){const t=this.valueHexView;switch(!0){case!!(128&t[0]):{const e=new Uint8Array(this.valueHexView.length+1);e[0]=0,e.set(t,1),this.valueHexView=e}break;case 0===t[0]&&!(128&t[1]):this.valueHexView=this.valueHexView.subarray(1)}return this.toBER(e)}fromBER(e,t,r){const i=super.fromBER(e,t,r);return-1===i||this.setValueHex(),i}toBER(e){return e?new ArrayBuffer(this.valueHexView.length):this.valueHexView.slice().buffer}toJSON(){return{...super.toJSON(),valueDec:this.valueDec}}toString(){const e=8*this.valueHexView.length-1;let t,r=new Uint8Array(8*this.valueHexView.length/3),i=0;const n=this.valueHexView;let s="",o=!1;for(let o=n.byteLength-1;o>=0;o--){t=n[o];for(let n=0;n<8;n++){if(!(1&~t))if(i===e)r=ve(ge(i),r),s="-";else r=de(r,ge(i));i++,t>>=1}}for(let e=0;e<r.length;e++)r[e]&&(o=!0),o&&(s+=S.charAt(r[e]));return!1===o&&(s+=S.charAt(0)),s}}ce=me,me.NAME="IntegerValueBlock",Object.defineProperty(ce.prototype,"valueHex",{set:function(e){this.valueHexView=new Uint8Array(e),this.setValueHex()},get:function(){return this.valueHexView.slice().buffer}});class we extends V{constructor(e={}){super(e,me),this.idBlock.tagClass=1,this.idBlock.tagNumber=2}toBigInt(){return v(),BigInt(this.valueBlock.toString())}static fromBigInt(e){v();const t=BigInt(e),r=new b,n=t.toString(16).replace(/^-/,""),s=new Uint8Array(l.FromHex(n));if(t<0){const e=new Uint8Array(s.length+(128&s[0]?1:0));e[0]|=128;const n=BigInt(`0x${l.ToHex(e)}`)+t,o=i.toUint8Array(l.FromHex(n.toString(16)));o[0]|=128,r.write(o)}else 128&s[0]&&r.write(new Uint8Array([0])),r.write(s);return new we({valueHex:r.final()})}convertToDER(){const e=new we({valueHex:this.valueBlock.valueHexView});return e.valueBlock.toDER(),e}convertFromDER(){return new we({valueHex:0===this.valueBlock.valueHexView[0]?this.valueBlock.valueHexView.subarray(1):this.valueBlock.valueHexView})}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.valueBlock.toString()}`}}ue=we,D.Integer=ue,we.NAME="INTEGER";class be extends we{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=10}}le=be,D.Enumerated=le,be.NAME="ENUMERATED";class Ae extends(T(P)){constructor({valueDec:e=-1,isFirstSid:t=!1,...r}={}){super(r),this.valueDec=e,this.isFirstSid=t}fromBER(e,t,r){if(!r)return t;const n=i.toUint8Array(e);if(!w(this,n,t,r))return-1;const s=n.subarray(t,t+r);this.valueHexView=new Uint8Array(r);for(let e=0;e<r&&(this.valueHexView[e]=127&s[e],this.blockLength++,128&s[e]);e++);const o=new Uint8Array(this.blockLength);for(let e=0;e<this.blockLength;e++)o[e]=this.valueHexView[e];return this.valueHexView=o,128&s[this.blockLength-1]?(this.error="End of input reached before message was fully decoded",-1):(0===this.valueHexView[0]&&this.warnings.push("Needlessly long format of SID encoding"),this.blockLength<=8?this.valueDec=h(this.valueHexView,7):(this.isHexOnly=!0,this.warnings.push("Too big SID for decoding, hex only")),t+this.blockLength)}set valueBigInt(e){v();let t=BigInt(e).toString(2);for(;t.length%7;)t="0"+t;const r=new Uint8Array(t.length/7);for(let e=0;e<r.length;e++)r[e]=parseInt(t.slice(7*e,7*e+7),2)+(e+1<r.length?128:0);this.fromBER(r.buffer,0,r.length)}toBER(e){if(this.isHexOnly){if(e)return new ArrayBuffer(this.valueHexView.byteLength);const t=this.valueHexView,r=new Uint8Array(this.blockLength);for(let e=0;e<this.blockLength-1;e++)r[e]=128|t[e];return r[this.blockLength-1]=t[this.blockLength-1],r.buffer}const t=f(this.valueDec,7);if(0===t.byteLength)return this.error="Error during encoding SID value",E;const r=new Uint8Array(t.byteLength);if(!e){const e=new Uint8Array(t),i=t.byteLength-1;for(let t=0;t<i;t++)r[t]=128|e[t];r[i]=e[i]}return r}toString(){let e="";if(this.isHexOnly)e=l.ToHex(this.valueHexView);else if(this.isFirstSid){let t=this.valueDec;this.valueDec<=39?e="0.":this.valueDec<=79?(e="1.",t-=40):(e="2.",t-=80),e+=t.toString()}else e=this.valueDec.toString();return e}toJSON(){return{...super.toJSON(),valueDec:this.valueDec,isFirstSid:this.isFirstSid}}}Ae.NAME="sidBlock";class Se extends P{constructor({value:e="",...t}={}){super(t),this.value=[],e&&this.fromString(e)}fromBER(e,t,r){let i=t;for(;r>0;){const t=new Ae;if(i=t.fromBER(e,i,r),-1===i)return this.blockLength=0,this.error=t.error,i;0===this.value.length&&(t.isFirstSid=!0),this.blockLength+=t.blockLength,r-=t.blockLength,this.value.push(t)}return i}toBER(e){const t=[];for(let r=0;r<this.value.length;r++){const i=this.value[r].toBER(e);if(0===i.byteLength)return this.error=this.value[r].error,E;t.push(i)}return m(t)}fromString(e){this.value=[];let t=0,r=0,i="",n=!1;do{if(r=e.indexOf(".",t),i=-1===r?e.substring(t):e.substring(t,r),t=r+1,n){const e=this.value[0];let t=0;switch(e.valueDec){case 0:break;case 1:t=40;break;case 2:t=80;break;default:return void(this.value=[])}const r=parseInt(i,10);if(isNaN(r))return;e.valueDec=r+t,n=!1}else{const e=new Ae;if(i>Number.MAX_SAFE_INTEGER){v();const t=BigInt(i);e.valueBigInt=t}else if(e.valueDec=parseInt(i,10),isNaN(e.valueDec))return;this.value.length||(e.isFirstSid=!0,n=!0),this.value.push(e)}}while(-1!==r)}toString(){let e="",t=!1;for(let r=0;r<this.value.length;r++){t=this.value[r].isHexOnly;let i=this.value[r].toString();0!==r&&(e=`${e}.`),t?(i=`{${i}}`,this.value[r].isFirstSid?e=`2.{${i} - 80}`:e+=i):e+=i}return e}toJSON(){const e={...super.toJSON(),value:this.toString(),sidArray:[]};for(let t=0;t<this.value.length;t++)e.sidArray.push(this.value[t].toJSON());return e}}Se.NAME="ObjectIdentifierValueBlock";class xe extends V{constructor(e={}){super(e,Se),this.idBlock.tagClass=1,this.idBlock.tagNumber=6}getValue(){return this.valueBlock.toString()}setValue(e){this.valueBlock.fromString(e)}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.valueBlock.toString()||"empty"}`}toJSON(){return{...super.toJSON(),value:this.getValue()}}}pe=xe,D.ObjectIdentifier=pe,xe.NAME="OBJECT IDENTIFIER";class Be extends(T(j)){constructor({valueDec:e=0,...t}={}){super(t),this.valueDec=e}fromBER(e,t,r){if(0===r)return t;const n=i.toUint8Array(e);if(!w(this,n,t,r))return-1;const s=n.subarray(t,t+r);this.valueHexView=new Uint8Array(r);for(let e=0;e<r&&(this.valueHexView[e]=127&s[e],this.blockLength++,128&s[e]);e++);const o=new Uint8Array(this.blockLength);for(let e=0;e<this.blockLength;e++)o[e]=this.valueHexView[e];return this.valueHexView=o,128&s[this.blockLength-1]?(this.error="End of input reached before message was fully decoded",-1):(0===this.valueHexView[0]&&this.warnings.push("Needlessly long format of SID encoding"),this.blockLength<=8?this.valueDec=h(this.valueHexView,7):(this.isHexOnly=!0,this.warnings.push("Too big SID for decoding, hex only")),t+this.blockLength)}toBER(e){if(this.isHexOnly){if(e)return new ArrayBuffer(this.valueHexView.byteLength);const t=this.valueHexView,r=new Uint8Array(this.blockLength);for(let e=0;e<this.blockLength-1;e++)r[e]=128|t[e];return r[this.blockLength-1]=t[this.blockLength-1],r.buffer}const t=f(this.valueDec,7);if(0===t.byteLength)return this.error="Error during encoding SID value",E;const r=new Uint8Array(t.byteLength);if(!e){const e=new Uint8Array(t),i=t.byteLength-1;for(let t=0;t<i;t++)r[t]=128|e[t];r[i]=e[i]}return r.buffer}toString(){let e="";return e=this.isHexOnly?l.ToHex(this.valueHexView):this.valueDec.toString(),e}toJSON(){return{...super.toJSON(),valueDec:this.valueDec}}}Be.NAME="relativeSidBlock";class ke extends P{constructor({value:e="",...t}={}){super(t),this.value=[],e&&this.fromString(e)}fromBER(e,t,r){let i=t;for(;r>0;){const t=new Be;if(i=t.fromBER(e,i,r),-1===i)return this.blockLength=0,this.error=t.error,i;this.blockLength+=t.blockLength,r-=t.blockLength,this.value.push(t)}return i}toBER(e,t){const r=[];for(let t=0;t<this.value.length;t++){const i=this.value[t].toBER(e);if(0===i.byteLength)return this.error=this.value[t].error,E;r.push(i)}return m(r)}fromString(e){this.value=[];let t=0,r=0,i="";do{r=e.indexOf(".",t),i=-1===r?e.substring(t):e.substring(t,r),t=r+1;const n=new Be;if(n.valueDec=parseInt(i,10),isNaN(n.valueDec))return!0;this.value.push(n)}while(-1!==r);return!0}toString(){let e="",t=!1;for(let r=0;r<this.value.length;r++){t=this.value[r].isHexOnly;let i=this.value[r].toString();0!==r&&(e=`${e}.`),t?(i=`{${i}}`,e+=i):e+=i}return e}toJSON(){const e={...super.toJSON(),value:this.toString(),sidArray:[]};for(let t=0;t<this.value.length;t++)e.sidArray.push(this.value[t].toJSON());return e}}ke.NAME="RelativeObjectIdentifierValueBlock";class Ee extends V{constructor(e={}){super(e,ke),this.idBlock.tagClass=1,this.idBlock.tagNumber=13}getValue(){return this.valueBlock.toString()}setValue(e){this.valueBlock.fromString(e)}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.valueBlock.toString()||"empty"}`}toJSON(){return{...super.toJSON(),value:this.getValue()}}}he=Ee,D.RelativeObjectIdentifier=he,Ee.NAME="RelativeObjectIdentifier";class Ce extends Z{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=16}}fe=Ce,D.Sequence=fe,Ce.NAME="SEQUENCE";let Ie=class extends Z{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=17}};ye=Ie,D.Set=ye,Ie.NAME="SET";class Oe extends(T(P)){constructor({...e}={}){super(e),this.isHexOnly=!0,this.value=k}toJSON(){return{...super.toJSON(),value:this.value}}}Oe.NAME="StringValueBlock";class Ne extends Oe{}Ne.NAME="SimpleStringValueBlock";class Te extends H{constructor({...e}={}){super(e,Ne)}fromBuffer(e){this.valueBlock.value=String.fromCharCode.apply(null,i.toUint8Array(e))}fromString(e){const t=e.length,r=this.valueBlock.valueHexView=new Uint8Array(t);for(let i=0;i<t;i++)r[i]=e.charCodeAt(i);this.valueBlock.value=e}}Te.NAME="SIMPLE STRING";class je extends Te{fromBuffer(e){this.valueBlock.valueHexView=i.toUint8Array(e);try{this.valueBlock.value=l.ToUtf8String(e)}catch(t){this.warnings.push(`Error during "decodeURIComponent": ${t}, using raw string`),this.valueBlock.value=l.ToBinary(e)}}fromString(e){this.valueBlock.valueHexView=new Uint8Array(l.FromUtf8String(e)),this.valueBlock.value=e}}var Pe,Re,Ue,De,Ve,Le,He,$e,Me,Fe,Ke,ze,_e,qe,We,Ge,Je,Xe,Ze;je.NAME="Utf8StringValueBlock";class Ye extends je{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=12}}Pe=Ye,D.Utf8String=Pe,Ye.NAME="UTF8String";class Qe extends Te{fromBuffer(e){this.valueBlock.value=l.ToUtf16String(e),this.valueBlock.valueHexView=i.toUint8Array(e)}fromString(e){this.valueBlock.value=e,this.valueBlock.valueHexView=new Uint8Array(l.FromUtf16String(e))}}Qe.NAME="BmpStringValueBlock";class et extends Qe{constructor({...e}={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=30}}Re=et,D.BmpString=Re,et.NAME="BMPString";class tt extends Te{fromBuffer(e){const t=ArrayBuffer.isView(e)?e.slice().buffer:e.slice(0),r=new Uint8Array(t);for(let e=0;e<r.length;e+=4)r[e]=r[e+3],r[e+1]=r[e+2],r[e+2]=0,r[e+3]=0;this.valueBlock.value=String.fromCharCode.apply(null,new Uint32Array(t))}fromString(e){const t=e.length,r=this.valueBlock.valueHexView=new Uint8Array(4*t);for(let i=0;i<t;i++){const t=f(e.charCodeAt(i),8),n=new Uint8Array(t);if(n.length>4)continue;const s=4-n.length;for(let e=n.length-1;e>=0;e--)r[4*i+e+s]=n[e]}this.valueBlock.value=e}}tt.NAME="UniversalStringValueBlock";class rt extends tt{constructor({...e}={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=28}}Ue=rt,D.UniversalString=Ue,rt.NAME="UniversalString";class it extends Te{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=18}}De=it,D.NumericString=De,it.NAME="NumericString";class nt extends Te{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=19}}Ve=nt,D.PrintableString=Ve,nt.NAME="PrintableString";class st extends Te{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=20}}Le=st,D.TeletexString=Le,st.NAME="TeletexString";class ot extends Te{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=21}}He=ot,D.VideotexString=He,ot.NAME="VideotexString";class at extends Te{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=22}}$e=at,D.IA5String=$e,at.NAME="IA5String";class ct extends Te{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=25}}Me=ct,D.GraphicString=Me,ct.NAME="GraphicString";class ut extends Te{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=26}}Fe=ut,D.VisibleString=Fe,ut.NAME="VisibleString";class lt extends Te{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=27}}Ke=lt,D.GeneralString=Ke,lt.NAME="GeneralString";class pt extends Te{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=29}}ze=pt,D.CharacterString=ze,pt.NAME="CharacterString";class ht extends ut{constructor({value:e,valueDate:t,...r}={}){if(super(r),this.year=0,this.month=0,this.day=0,this.hour=0,this.minute=0,this.second=0,e){this.fromString(e),this.valueBlock.valueHexView=new Uint8Array(e.length);for(let t=0;t<e.length;t++)this.valueBlock.valueHexView[t]=e.charCodeAt(t)}t&&(this.fromDate(t),this.valueBlock.valueHexView=new Uint8Array(this.toBuffer())),this.idBlock.tagClass=1,this.idBlock.tagNumber=23}fromBuffer(e){this.fromString(String.fromCharCode.apply(null,i.toUint8Array(e)))}toBuffer(){const e=this.toString(),t=new ArrayBuffer(e.length),r=new Uint8Array(t);for(let t=0;t<e.length;t++)r[t]=e.charCodeAt(t);return t}fromDate(e){this.year=e.getUTCFullYear(),this.month=e.getUTCMonth()+1,this.day=e.getUTCDate(),this.hour=e.getUTCHours(),this.minute=e.getUTCMinutes(),this.second=e.getUTCSeconds()}toDate(){return new Date(Date.UTC(this.year,this.month-1,this.day,this.hour,this.minute,this.second))}fromString(e){const t=/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})Z/gi.exec(e);if(null===t)return void(this.error="Wrong input string for conversion");const r=parseInt(t[1],10);this.year=r>=50?1900+r:2e3+r,this.month=parseInt(t[2],10),this.day=parseInt(t[3],10),this.hour=parseInt(t[4],10),this.minute=parseInt(t[5],10),this.second=parseInt(t[6],10)}toString(e="iso"){if("iso"===e){const e=new Array(7);return e[0]=g(this.year<2e3?this.year-1900:this.year-2e3,2),e[1]=g(this.month,2),e[2]=g(this.day,2),e[3]=g(this.hour,2),e[4]=g(this.minute,2),e[5]=g(this.second,2),e[6]="Z",e.join("")}return super.toString(e)}onAsciiEncoding(){return`${this.constructor.NAME} : ${this.toDate().toISOString()}`}toJSON(){return{...super.toJSON(),year:this.year,month:this.month,day:this.day,hour:this.hour,minute:this.minute,second:this.second}}}_e=ht,D.UTCTime=_e,ht.NAME="UTCTime";class ft extends ht{constructor(e={}){var t;super(e),null!==(t=this.millisecond)&&void 0!==t||(this.millisecond=0),this.idBlock.tagClass=1,this.idBlock.tagNumber=24}fromDate(e){super.fromDate(e),this.millisecond=e.getUTCMilliseconds()}toDate(){return new Date(Date.UTC(this.year,this.month-1,this.day,this.hour,this.minute,this.second,this.millisecond))}fromString(e){let t,r=!1,i="",n="",s=0,o=0,a=0;if("Z"===e[e.length-1])i=e.substring(0,e.length-1),r=!0;else{const t=new Number(e[e.length-1]);if(isNaN(t.valueOf()))throw new Error("Wrong input string for conversion");i=e}if(r){if(-1!==i.indexOf("+"))throw new Error("Wrong input string for conversion");if(-1!==i.indexOf("-"))throw new Error("Wrong input string for conversion")}else{let e=1,t=i.indexOf("+"),r="";if(-1===t&&(t=i.indexOf("-"),e=-1),-1!==t){if(r=i.substring(t+1),i=i.substring(0,t),2!==r.length&&4!==r.length)throw new Error("Wrong input string for conversion");let n=parseInt(r.substring(0,2),10);if(isNaN(n.valueOf()))throw new Error("Wrong input string for conversion");if(o=e*n,4===r.length){if(n=parseInt(r.substring(2,4),10),isNaN(n.valueOf()))throw new Error("Wrong input string for conversion");a=e*n}}}let c=i.indexOf(".");if(-1===c&&(c=i.indexOf(",")),-1!==c){const e=new Number(`0${i.substring(c)}`);if(isNaN(e.valueOf()))throw new Error("Wrong input string for conversion");s=e.valueOf(),n=i.substring(0,c)}else n=i;switch(!0){case 8===n.length:if(t=/(\d{4})(\d{2})(\d{2})/gi,-1!==c)throw new Error("Wrong input string for conversion");break;case 10===n.length:if(t=/(\d{4})(\d{2})(\d{2})(\d{2})/gi,-1!==c){let e=60*s;this.minute=Math.floor(e),e=60*(e-this.minute),this.second=Math.floor(e),e=1e3*(e-this.second),this.millisecond=Math.floor(e)}break;case 12===n.length:if(t=/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})/gi,-1!==c){let e=60*s;this.second=Math.floor(e),e=1e3*(e-this.second),this.millisecond=Math.floor(e)}break;case 14===n.length:if(t=/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/gi,-1!==c){const e=1e3*s;this.millisecond=Math.floor(e)}break;default:throw new Error("Wrong input string for conversion")}const u=t.exec(n);if(null===u)throw new Error("Wrong input string for conversion");for(let e=1;e<u.length;e++)switch(e){case 1:this.year=parseInt(u[e],10);break;case 2:this.month=parseInt(u[e],10);break;case 3:this.day=parseInt(u[e],10);break;case 4:this.hour=parseInt(u[e],10)+o;break;case 5:this.minute=parseInt(u[e],10)+a;break;case 6:this.second=parseInt(u[e],10);break;default:throw new Error("Wrong input string for conversion")}if(!1===r){const e=new Date(this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond);this.year=e.getUTCFullYear(),this.month=e.getUTCMonth(),this.day=e.getUTCDay(),this.hour=e.getUTCHours(),this.minute=e.getUTCMinutes(),this.second=e.getUTCSeconds(),this.millisecond=e.getUTCMilliseconds()}}toString(e="iso"){if("iso"===e){const e=[];return e.push(g(this.year,4)),e.push(g(this.month,2)),e.push(g(this.day,2)),e.push(g(this.hour,2)),e.push(g(this.minute,2)),e.push(g(this.second,2)),0!==this.millisecond&&(e.push("."),e.push(g(this.millisecond,3))),e.push("Z"),e.join("")}return super.toString(e)}toJSON(){return{...super.toJSON(),millisecond:this.millisecond}}}qe=ft,D.GeneralizedTime=qe,ft.NAME="GeneralizedTime";class yt extends Ye{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=31}}We=yt,D.DATE=We,yt.NAME="DATE";class dt extends Ye{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=32}}Ge=dt,D.TimeOfDay=Ge,dt.NAME="TimeOfDay";class gt extends Ye{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=33}}Je=gt,D.DateTime=Je,gt.NAME="DateTime";class vt extends Ye{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=34}}Xe=vt,D.Duration=Xe,vt.NAME="Duration";class mt extends Ye{constructor(e={}){super(e),this.idBlock.tagClass=1,this.idBlock.tagNumber=14}}Ze=mt,D.TIME=Ze,mt.NAME="TIME";class wt{constructor({name:e="",optional:t=!1}={}){this.name=e,this.optional=t}}class bt extends wt{constructor({value:e=[],...t}={}){super(t),this.value=e}}class At extends wt{constructor({value:e=new wt,local:t=!1,...r}={}){super(r),this.value=e,this.local=t}}function St(e,t,r){if(r instanceof bt){for(let i=0;i<r.value.length;i++){if(St(e,t,r.value[i]).verified)return{verified:!0,result:e}}{const e={verified:!1,result:{error:"Wrong values for Choice type"}};return r.hasOwnProperty(x)&&(e.name=r.name),e}}if(r instanceof wt)return r.hasOwnProperty(x)&&(e[r.name]=t),{verified:!0,result:e};if(e instanceof Object==!1)return{verified:!1,result:{error:"Wrong root object"}};if(t instanceof Object==!1)return{verified:!1,result:{error:"Wrong ASN.1 data"}};if(r instanceof Object==!1)return{verified:!1,result:{error:"Wrong ASN.1 schema"}};if("idBlock"in r==!1)return{verified:!1,result:{error:"Wrong ASN.1 schema"}};if("fromBER"in r.idBlock==!1)return{verified:!1,result:{error:"Wrong ASN.1 schema"}};if("toBER"in r.idBlock==!1)return{verified:!1,result:{error:"Wrong ASN.1 schema"}};const i=r.idBlock.toBER(!1);if(0===i.byteLength)return{verified:!1,result:{error:"Error encoding idBlock for ASN.1 schema"}};if(-1===r.idBlock.fromBER(i,0,i.byteLength))return{verified:!1,result:{error:"Error decoding idBlock for ASN.1 schema"}};if(!1===r.idBlock.hasOwnProperty("tagClass"))return{verified:!1,result:{error:"Wrong ASN.1 schema"}};if(r.idBlock.tagClass!==t.idBlock.tagClass)return{verified:!1,result:e};if(!1===r.idBlock.hasOwnProperty("tagNumber"))return{verified:!1,result:{error:"Wrong ASN.1 schema"}};if(r.idBlock.tagNumber!==t.idBlock.tagNumber)return{verified:!1,result:e};if(!1===r.idBlock.hasOwnProperty("isConstructed"))return{verified:!1,result:{error:"Wrong ASN.1 schema"}};if(r.idBlock.isConstructed!==t.idBlock.isConstructed)return{verified:!1,result:e};if(!("isHexOnly"in r.idBlock))return{verified:!1,result:{error:"Wrong ASN.1 schema"}};if(r.idBlock.isHexOnly!==t.idBlock.isHexOnly)return{verified:!1,result:e};if(r.idBlock.isHexOnly){if(B in r.idBlock==!1)return{verified:!1,result:{error:"Wrong ASN.1 schema"}};const i=r.idBlock.valueHexView,n=t.idBlock.valueHexView;if(i.length!==n.length)return{verified:!1,result:e};for(let t=0;t<i.length;t++)if(i[t]!==n[1])return{verified:!1,result:e}}if(r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,k),r.name&&(e[r.name]=t)),r instanceof D.Constructed){let i=0,n={verified:!1,result:{error:"Unknown error"}},s=r.valueBlock.value.length;if(s>0&&r.valueBlock.value[0]instanceof At&&(s=t.valueBlock.value.length),0===s)return{verified:!0,result:e};if(0===t.valueBlock.value.length&&0!==r.valueBlock.value.length){let t=!0;for(let e=0;e<r.valueBlock.value.length;e++)t=t&&(r.valueBlock.value[e].optional||!1);return t?{verified:!0,result:e}:(r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,k),r.name&&delete e[r.name]),e.error="Inconsistent object length",{verified:!1,result:e})}for(let o=0;o<s;o++)if(o-i>=t.valueBlock.value.length){if(!1===r.valueBlock.value[o].optional){const t={verified:!1,result:e};return e.error="Inconsistent length between ASN.1 data and schema",r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,k),r.name&&(delete e[r.name],t.name=r.name)),t}}else if(r.valueBlock.value[0]instanceof At){if(n=St(e,t.valueBlock.value[o],r.valueBlock.value[0].value),!1===n.verified){if(!r.valueBlock.value[0].optional)return r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,k),r.name&&delete e[r.name]),n;i++}if(x in r.valueBlock.value[0]&&r.valueBlock.value[0].name.length>0){let i={};i="local"in r.valueBlock.value[0]&&r.valueBlock.value[0].local?t:e,void 0===i[r.valueBlock.value[0].name]&&(i[r.valueBlock.value[0].name]=[]),i[r.valueBlock.value[0].name].push(t.valueBlock.value[o])}}else if(n=St(e,t.valueBlock.value[o-i],r.valueBlock.value[o]),!1===n.verified){if(!r.valueBlock.value[o].optional)return r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,k),r.name&&delete e[r.name]),n;i++}if(!1===n.verified){const t={verified:!1,result:e};return r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,k),r.name&&(delete e[r.name],t.name=r.name)),t}return{verified:!0,result:e}}if(r.primitiveSchema&&B in t.valueBlock){const i=G(t.valueBlock.valueHexView);if(-1===i.offset){const t={verified:!1,result:i.result};return r.name&&(r.name=r.name.replace(/^\s+|\s+$/g,k),r.name&&(delete e[r.name],t.name=r.name)),t}return St(e,i.result,r.primitiveSchema)}return{verified:!0,result:e}}var xt,Bt,kt=Object.freeze({__proto__:null,Any:wt,BaseBlock:V,BaseStringBlock:H,BitString:ae,BmpString:et,Boolean:re,CharacterString:pt,Choice:bt,Constructed:Z,DATE:yt,DateTime:gt,Duration:vt,EndOfContent:Q,Enumerated:be,GeneralString:lt,GeneralizedTime:ft,GraphicString:ct,HexBlock:T,IA5String:at,Integer:we,Null:ee,NumericString:it,ObjectIdentifier:xe,OctetString:ne,Primitive:W,PrintableString:nt,RawData:class{constructor({data:e=C}={}){this.dataView=i.toUint8Array(e)}get data(){return this.dataView.slice().buffer}set data(e){this.dataView=i.toUint8Array(e)}fromBER(e,t,r){const n=t+r;return this.dataView=i.toUint8Array(e).subarray(t,n),n}toBER(e){return this.dataView.slice().buffer}},RelativeObjectIdentifier:Ee,Repeated:At,Sequence:Ce,Set:Ie,TIME:mt,TeletexString:st,TimeOfDay:dt,UTCTime:ht,UniversalString:rt,Utf8String:Ye,ValueBlock:P,VideotexString:ot,ViewWriter:b,VisibleString:ut,compareSchema:St,fromBER:J,verifySchema:function(e,t){if(t instanceof Object==!1)return{verified:!1,result:{error:"Wrong ASN.1 schema type"}};const r=G(i.toUint8Array(e));return-1===r.offset?{verified:!1,result:r.result}:St(r.result,r.result,t)}});!function(e){e[e.Sequence=0]="Sequence",e[e.Set=1]="Set",e[e.Choice=2]="Choice"}(xt||(xt={})),function(e){e[e.Any=1]="Any",e[e.Boolean=2]="Boolean",e[e.OctetString=3]="OctetString",e[e.BitString=4]="BitString",e[e.Integer=5]="Integer",e[e.Enumerated=6]="Enumerated",e[e.ObjectIdentifier=7]="ObjectIdentifier",e[e.Utf8String=8]="Utf8String",e[e.BmpString=9]="BmpString",e[e.UniversalString=10]="UniversalString",e[e.NumericString=11]="NumericString",e[e.PrintableString=12]="PrintableString",e[e.TeletexString=13]="TeletexString",e[e.VideotexString=14]="VideotexString",e[e.IA5String=15]="IA5String",e[e.GraphicString=16]="GraphicString",e[e.VisibleString=17]="VisibleString",e[e.GeneralString=18]="GeneralString",e[e.CharacterString=19]="CharacterString",e[e.UTCTime=20]="UTCTime",e[e.GeneralizedTime=21]="GeneralizedTime",e[e.DATE=22]="DATE",e[e.TimeOfDay=23]="TimeOfDay",e[e.DateTime=24]="DateTime",e[e.Duration=25]="Duration",e[e.TIME=26]="TIME",e[e.Null=27]="Null"}(Bt||(Bt={}));class Et{constructor(e,t=0){if(this.unusedBits=0,this.value=new ArrayBuffer(0),e)if("number"==typeof e)this.fromNumber(e);else{if(!i.isBufferSource(e))throw TypeError("Unsupported type of 'params' argument for BitString");this.unusedBits=t,this.value=i.toArrayBuffer(e)}}fromASN(e){if(!(e instanceof ae))throw new TypeError("Argument 'asn' is not instance of ASN.1 BitString");return this.unusedBits=e.valueBlock.unusedBits,this.value=e.valueBlock.valueHex,this}toASN(){return new ae({unusedBits:this.unusedBits,valueHex:this.value})}toSchema(e){return new ae({name:e})}toNumber(){let e="";const t=new Uint8Array(this.value);for(const r of t)e+=r.toString(2).padStart(8,"0");return e=e.split("").reverse().join(""),this.unusedBits&&(e=e.slice(this.unusedBits).padStart(this.unusedBits,"0")),parseInt(e,2)}fromNumber(e){let t=e.toString(2);const r=t.length+7>>3;this.unusedBits=(r<<3)-t.length;const i=new Uint8Array(r);t=t.padStart(r<<3,"0").split("").reverse().join("");let n=0;for(;n<r;)i[n]=parseInt(t.slice(n<<3,8+(n<<3)),2),n++;this.value=i.buffer}}class Ct{get byteLength(){return this.buffer.byteLength}get byteOffset(){return 0}constructor(e){"number"==typeof e?this.buffer=new ArrayBuffer(e):i.isBufferSource(e)?this.buffer=i.toArrayBuffer(e):Array.isArray(e)?this.buffer=new Uint8Array(e):this.buffer=new ArrayBuffer(0)}fromASN(e){if(!(e instanceof ne))throw new TypeError("Argument 'asn' is not instance of ASN.1 OctetString");return this.buffer=e.valueBlock.valueHex,this}toASN(){return new ne({valueHex:this.buffer})}toSchema(e){return new ne({name:e})}}const It={fromASN:e=>e instanceof ee?null:e.valueBeforeDecodeView,toASN:e=>{if(null===e)return new ee;const t=J(e);if(t.result.error)throw new Error(t.result.error);return t.result}},Ot={fromASN:e=>e.valueBlock.valueHexView.byteLength>=4?e.valueBlock.toString():e.valueBlock.valueDec,toASN:e=>new we({value:+e})},Nt={fromASN:e=>e.valueBlock.valueDec,toASN:e=>new be({value:e})},Tt={fromASN:e=>e.valueBlock.valueHexView,toASN:e=>new we({valueHex:e})},jt={fromASN:e=>e.valueBlock.valueHexView,toASN:e=>new ae({valueHex:e})},Pt={fromASN:e=>e.valueBlock.toString(),toASN:e=>new xe({value:e})},Rt={fromASN:e=>e.valueBlock.value,toASN:e=>new re({value:e})},Ut={fromASN:e=>e.valueBlock.valueHexView,toASN:e=>new ne({valueHex:e})},Dt={fromASN:e=>new Ct(e.getValue()),toASN:e=>e.toASN()};function Vt(e){return{fromASN:e=>e.valueBlock.value,toASN:t=>new e({value:t})}}const Lt=Vt(Ye),Ht=Vt(et),$t=Vt(rt),Mt=Vt(it),Ft=Vt(nt),Kt=Vt(st),zt=Vt(ot),_t=Vt(at),qt=Vt(ct),Wt=Vt(ut),Gt=Vt(lt),Jt=Vt(pt),Xt={fromASN:e=>e.toDate(),toASN:e=>new ht({valueDate:e})},Zt={fromASN:e=>e.toDate(),toASN:e=>new ft({valueDate:e})},Yt={fromASN:()=>null,toASN:()=>new ee};function Qt(e){switch(e){case Bt.Any:return It;case Bt.BitString:return jt;case Bt.BmpString:return Ht;case Bt.Boolean:return Rt;case Bt.CharacterString:return Jt;case Bt.Enumerated:return Nt;case Bt.GeneralString:return Gt;case Bt.GeneralizedTime:return Zt;case Bt.GraphicString:return qt;case Bt.IA5String:return _t;case Bt.Integer:return Ot;case Bt.Null:return Yt;case Bt.NumericString:return Mt;case Bt.ObjectIdentifier:return Pt;case Bt.OctetString:return Ut;case Bt.PrintableString:return Ft;case Bt.TeletexString:return Kt;case Bt.UTCTime:return Xt;case Bt.UniversalString:return $t;case Bt.Utf8String:return Lt;case Bt.VideotexString:return zt;case Bt.VisibleString:return Wt;default:return null}}function er(e){return"function"==typeof e&&e.prototype?!(!e.prototype.toASN||!e.prototype.fromASN)||er(e.prototype):!!(e&&"object"==typeof e&&"toASN"in e&&"fromASN"in e)}function tr(e){var t;if(e){const r=Object.getPrototypeOf(e);return(null===(t=null==r?void 0:r.prototype)||void 0===t?void 0:t.constructor)===Array||tr(r)}return!1}function rr(e,t){if(!e||!t)return!1;if(e.byteLength!==t.byteLength)return!1;const r=new Uint8Array(e),i=new Uint8Array(t);for(let t=0;t<e.byteLength;t++)if(r[t]!==i[t])return!1;return!0}const ir=new class{constructor(){this.items=new WeakMap}has(e){return this.items.has(e)}get(e,t=!1){const r=this.items.get(e);if(!r)throw new Error(`Cannot get schema for '${e.prototype.constructor.name}' target`);if(t&&!r.schema)throw new Error(`Schema '${e.prototype.constructor.name}' doesn't contain ASN.1 schema. Call 'AsnSchemaStorage.cache'.`);return r}cache(e){const t=this.get(e);t.schema||(t.schema=this.create(e,!0))}createDefault(e){const t={type:xt.Sequence,items:{}},r=this.findParentSchema(e);return r&&(Object.assign(t,r),t.items=Object.assign({},t.items,r.items)),t}create(e,t){const r=this.items.get(e)||this.createDefault(e),i=[];for(const e in r.items){const n=r.items[e],s=t?e:"";let o;if("number"==typeof n.type){const e=Bt[n.type],t=kt[e];if(!t)throw new Error(`Cannot get ASN1 class by name '${e}'`);o=new t({name:s})}else if(er(n.type)){o=(new n.type).toSchema(s)}else if(n.optional){this.get(n.type).type===xt.Choice?o=new wt({name:s}):(o=this.create(n.type,!1),o.name=s)}else o=new wt({name:s});const a=!!n.optional||void 0!==n.defaultValue;if(n.repeated){o.name="";o=new("set"===n.repeated?Ie:Ce)({name:"",value:[new At({name:s,value:o})]})}if(null!==n.context&&void 0!==n.context)if(n.implicit)if("number"==typeof n.type||er(n.type)){const e=n.repeated?Z:W;i.push(new e({name:s,optional:a,idBlock:{tagClass:3,tagNumber:n.context}}))}else{this.cache(n.type);const e=!!n.repeated;let t=e?o:this.get(n.type,!0).schema;t="valueBlock"in t?t.valueBlock.value:t.value,i.push(new Z({name:e?"":s,optional:a,idBlock:{tagClass:3,tagNumber:n.context},value:t}))}else i.push(new Z({optional:a,idBlock:{tagClass:3,tagNumber:n.context},value:[o]}));else o.optional=a,i.push(o)}switch(r.type){case xt.Sequence:return new Ce({value:i,name:""});case xt.Set:return new Ie({value:i,name:""});case xt.Choice:return new bt({value:i,name:""});default:throw new Error("Unsupported ASN1 type in use")}}set(e,t){return this.items.set(e,t),this}findParentSchema(e){const t=Object.getPrototypeOf(e);if(t){return this.items.get(t)||this.findParentSchema(t)}return null}},nr=e=>t=>{let r;ir.has(t)?r=ir.get(t):(r=ir.createDefault(t),ir.set(t,r)),Object.assign(r,e)},sr=e=>(t,r)=>{let i;ir.has(t.constructor)?i=ir.get(t.constructor):(i=ir.createDefault(t.constructor),ir.set(t.constructor,i));const n=Object.assign({},e);if("number"==typeof n.type&&!n.converter){const i=Qt(e.type);if(!i)throw new Error(`Cannot get default converter for property '${r}' of ${t.constructor.name}`);n.converter=i}i.items[r]=n};class or extends Error{constructor(){super(...arguments),this.schemas=[]}}class ar{static parse(e,t){const r=J(e);if(r.result.error)throw new Error(r.result.error);return this.fromASN(r.result,t)}static fromASN(e,t){var r;try{if(er(t)){return(new t).fromASN(e)}const i=ir.get(t);ir.cache(t);let n=i.schema;if(e.constructor===Z&&i.type!==xt.Choice){n=new Z({idBlock:{tagClass:3,tagNumber:e.idBlock.tagNumber},value:i.schema.valueBlock.value});for(const t in i.items)delete e[t]}const s=St({},e,n);if(!s.verified)throw new or(`Data does not match to ${t.name} ASN1 schema. ${s.result.error}`);const o=new t;if(tr(t)){if(!("value"in e.valueBlock)||!Array.isArray(e.valueBlock.value))throw new Error("Cannot get items from the ASN.1 parsed value. ASN.1 object is not constructed.");const r=i.itemType;if("number"==typeof r){const i=Qt(r);if(!i)throw new Error(`Cannot get default converter for array item of ${t.name} ASN1 schema`);return t.from(e.valueBlock.value,(e=>i.fromASN(e)))}return t.from(e.valueBlock.value,(e=>this.fromASN(e,r)))}for(const e in i.items){const t=s.result[e];if(!t)continue;const n=i.items[e],a=n.type;if("number"==typeof a||er(a)){const i=null!==(r=n.converter)&&void 0!==r?r:er(a)?new a:null;if(!i)throw new Error("Converter is empty");if(n.repeated)if(n.implicit){const r=new("sequence"===n.repeated?Ce:Ie);r.valueBlock=t.valueBlock;const s=J(r.toBER(!1));if(-1===s.offset)throw new Error(`Cannot parse the child item. ${s.result.error}`);if(!("value"in s.result.valueBlock)||!Array.isArray(s.result.valueBlock.value))throw new Error("Cannot get items from the ASN.1 parsed value. ASN.1 object is not constructed.");const a=s.result.valueBlock.value;o[e]=Array.from(a,(e=>i.fromASN(e)))}else o[e]=Array.from(t,(e=>i.fromASN(e)));else{let r=t;if(n.implicit){let e;if(er(a))e=(new a).toSchema("");else{const t=Bt[a],r=kt[t];if(!r)throw new Error(`Cannot get '${t}' class from asn1js module`);e=new r}e.valueBlock=r.valueBlock,r=J(e.toBER(!1)).result}o[e]=i.fromASN(r)}}else if(n.repeated){if(!Array.isArray(t))throw new Error("Cannot get list of items from the ASN.1 parsed value. ASN.1 value should be iterable.");o[e]=Array.from(t,(e=>this.fromASN(e,a)))}else o[e]=this.fromASN(t,a)}return o}catch(e){throw e instanceof or&&e.schemas.push(t.name),e}}}class cr{static serialize(e){return e instanceof V?e.toBER(!1):this.toASN(e).toBER(!1)}static toASN(e){if(e&&"object"==typeof e&&er(e))return e.toASN();if(!e||"object"!=typeof e)throw new TypeError("Parameter 1 should be type of Object.");const t=e.constructor,r=ir.get(t);ir.cache(t);let i,n=[];if(r.itemType){if(!Array.isArray(e))throw new TypeError("Parameter 1 should be type of Array.");if("number"==typeof r.itemType){const i=Qt(r.itemType);if(!i)throw new Error(`Cannot get default converter for array item of ${t.name} ASN1 schema`);n=e.map((e=>i.toASN(e)))}else n=e.map((e=>this.toAsnItem({type:r.itemType},"[]",t,e)))}else for(const i in r.items){const s=r.items[i],o=e[i];if(void 0===o||s.defaultValue===o||"object"==typeof s.defaultValue&&"object"==typeof o&&rr(this.serialize(s.defaultValue),this.serialize(o)))continue;const a=cr.toAsnItem(s,i,t,o);if("number"==typeof s.context)if(s.implicit)if(s.repeated||"number"!=typeof s.type&&!er(s.type))n.push(new Z({optional:s.optional,idBlock:{tagClass:3,tagNumber:s.context},value:a.valueBlock.value}));else{const e={};e.valueHex=a instanceof ee?a.valueBeforeDecodeView:a.valueBlock.toBER(),n.push(new W({optional:s.optional,idBlock:{tagClass:3,tagNumber:s.context},...e}))}else n.push(new Z({optional:s.optional,idBlock:{tagClass:3,tagNumber:s.context},value:[a]}));else s.repeated?n=n.concat(a):n.push(a)}switch(r.type){case xt.Sequence:i=new Ce({value:n});break;case xt.Set:i=new Ie({value:n});break;case xt.Choice:if(!n[0])throw new Error(`Schema '${t.name}' has wrong data. Choice cannot be empty.`);i=n[0]}return i}static toAsnItem(e,t,r,i){let n;if("number"==typeof e.type){const s=e.converter;if(!s)throw new Error(`Property '${t}' doesn't have converter for type ${Bt[e.type]} in schema '${r.name}'`);if(e.repeated){if(!Array.isArray(i))throw new TypeError("Parameter 'objProp' should be type of Array.");const t=Array.from(i,(e=>s.toASN(e)));n=new("sequence"===e.repeated?Ce:Ie)({value:t})}else n=s.toASN(i)}else if(e.repeated){if(!Array.isArray(i))throw new TypeError("Parameter 'objProp' should be type of Array.");const t=Array.from(i,(e=>this.toASN(e)));n=new("sequence"===e.repeated?Ce:Ie)({value:t})}else n=this.toASN(i);return n}}class ur extends Array{constructor(e=[]){if("number"==typeof e)super(e);else{super();for(const t of e)this.push(t)}}}class lr{static serialize(e){return cr.serialize(e)}static parse(e,t){return ar.parse(e,t)}static toString(e){const t=J(i.isBufferSource(e)?i.toArrayBuffer(e):lr.serialize(e));if(-1===t.offset)throw new Error(`Cannot decode ASN.1 data. ${t.result.error}`);return t.result.toString()}}var pr=function(e,t){return pr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},pr(e,t)};function hr(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}pr(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}function fr(e,t,r,i){var n,s=arguments.length,o=s<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,r):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,r,i);else for(var a=e.length-1;a>=0;a--)(n=e[a])&&(o=(s<3?n(o):s>3?n(t,r,o):n(t,r))||o);return s>3&&o&&Object.defineProperty(t,r,o),o}function yr(e,t,r,i){return new(r||(r=Promise))((function(n,s){function o(e){try{c(i.next(e))}catch(e){s(e)}}function a(e){try{c(i.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((i=i.apply(e,t||[])).next())}))}function dr(e,t){var r,i,n,s={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=a(0),o.throw=a(1),o.return=a(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(c){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(s=0)),s;)try{if(r=1,i&&(n=2&a[0]?i.return:a[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,a[1])).done)return n;switch(i=0,n&&(a=[2&a[0],n.value]),a[0]){case 0:case 1:n=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,i=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(n=s.trys,(n=n.length>0&&n[n.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!n||a[1]>n[0]&&a[1]<n[3])){s.label=a[1];break}if(6===a[0]&&s.label<n[1]){s.label=n[1],n=a;break}if(n&&s.label<n[2]){s.label=n[2],s.ops.push(a);break}n[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],i=0}finally{r=n=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}function gr(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],i=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function vr(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var i,n,s=r.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(i=s.next()).done;)o.push(i.value)}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}return o}function mr(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(vr(arguments[t]));return e}"function"==typeof SuppressedError&&SuppressedError;var wr,br={exports:{}};wr=br,function(e){const t="(0?\\d+|0x[a-f0-9]+)",r={fourOctet:new RegExp(`^${t}\\.${t}\\.${t}\\.${t}$`,"i"),threeOctet:new RegExp(`^${t}\\.${t}\\.${t}$`,"i"),twoOctet:new RegExp(`^${t}\\.${t}$`,"i"),longValue:new RegExp(`^${t}$`,"i")},i=new RegExp("^0[0-7]+$","i"),n=new RegExp("^0x[a-f0-9]+$","i"),s="%[0-9a-z]{1,}",o="(?:[0-9a-f]+::?)+",a={zoneIndex:new RegExp(s,"i"),native:new RegExp(`^(::)?(${o})?([0-9a-f]+)?(::)?(${s})?$`,"i"),deprecatedTransitional:new RegExp(`^(?:::)(${t}\\.${t}\\.${t}\\.${t}(${s})?)$`,"i"),transitional:new RegExp(`^((?:${o})|(?:::)(?:${o})?)${t}\\.${t}\\.${t}\\.${t}(${s})?$`,"i")};function c(e,t){if(e.indexOf("::")!==e.lastIndexOf("::"))return null;let r,i,n=0,s=-1,o=(e.match(a.zoneIndex)||[])[0];for(o&&(o=o.substring(1),e=e.replace(/%.+$/,""));(s=e.indexOf(":",s+1))>=0;)n++;if("::"===e.substr(0,2)&&n--,"::"===e.substr(-2,2)&&n--,n>t)return null;for(i=t-n,r=":";i--;)r+="0:";return":"===(e=e.replace("::",r))[0]&&(e=e.slice(1)),":"===e[e.length-1]&&(e=e.slice(0,-1)),{parts:t=function(){const t=e.split(":"),r=[];for(let e=0;e<t.length;e++)r.push(parseInt(t[e],16));return r}(),zoneId:o}}function u(e,t,r,i){if(e.length!==t.length)throw new Error("ipaddr: cannot match CIDR for objects with different lengths");let n,s=0;for(;i>0;){if(n=r-i,n<0&&(n=0),e[s]>>n!=t[s]>>n)return!1;i-=r,s+=1}return!0}function l(e){if(n.test(e))return parseInt(e,16);if("0"===e[0]&&!isNaN(parseInt(e[1],10))){if(i.test(e))return parseInt(e,8);throw new Error(`ipaddr: cannot parse ${e} as octal`)}return parseInt(e,10)}function p(e,t){for(;e.length<t;)e=`0${e}`;return e}const h={};h.IPv4=function(){function e(e){if(4!==e.length)throw new Error("ipaddr: ipv4 octet count should be 4");let t,r;for(t=0;t<e.length;t++)if(r=e[t],!(0<=r&&r<=255))throw new Error("ipaddr: ipv4 octet should fit in 8 bits");this.octets=e}return e.prototype.SpecialRanges={unspecified:[[new e([0,0,0,0]),8]],broadcast:[[new e([255,255,255,255]),32]],multicast:[[new e([224,0,0,0]),4]],linkLocal:[[new e([169,254,0,0]),16]],loopback:[[new e([127,0,0,0]),8]],carrierGradeNat:[[new e([100,64,0,0]),10]],private:[[new e([10,0,0,0]),8],[new e([172,16,0,0]),12],[new e([192,168,0,0]),16]],reserved:[[new e([192,0,0,0]),24],[new e([192,0,2,0]),24],[new e([192,88,99,0]),24],[new e([198,18,0,0]),15],[new e([198,51,100,0]),24],[new e([203,0,113,0]),24],[new e([240,0,0,0]),4]],as112:[[new e([192,175,48,0]),24],[new e([192,31,196,0]),24]],amt:[[new e([192,52,193,0]),24]]},e.prototype.kind=function(){return"ipv4"},e.prototype.match=function(e,t){let r;if(void 0===t&&(r=e,e=r[0],t=r[1]),"ipv4"!==e.kind())throw new Error("ipaddr: cannot match ipv4 address with non-ipv4 one");return u(this.octets,e.octets,8,t)},e.prototype.prefixLengthFromSubnetMask=function(){let e=0,t=!1;const r={0:8,128:7,192:6,224:5,240:4,248:3,252:2,254:1,255:0};let i,n,s;for(i=3;i>=0;i-=1){if(n=this.octets[i],!(n in r))return null;if(s=r[n],t&&0!==s)return null;8!==s&&(t=!0),e+=s}return 32-e},e.prototype.range=function(){return h.subnetMatch(this,this.SpecialRanges)},e.prototype.toByteArray=function(){return this.octets.slice(0)},e.prototype.toIPv4MappedAddress=function(){return h.IPv6.parse(`::ffff:${this.toString()}`)},e.prototype.toNormalizedString=function(){return this.toString()},e.prototype.toString=function(){return this.octets.join(".")},e}(),h.IPv4.broadcastAddressFromCIDR=function(e){try{const t=this.parseCIDR(e),r=t[0].toByteArray(),i=this.subnetMaskFromPrefixLength(t[1]).toByteArray(),n=[];let s=0;for(;s<4;)n.push(parseInt(r[s],10)|255^parseInt(i[s],10)),s++;return new this(n)}catch(e){throw new Error("ipaddr: the address does not have IPv4 CIDR format")}},h.IPv4.isIPv4=function(e){return null!==this.parser(e)},h.IPv4.isValid=function(e){try{return new this(this.parser(e)),!0}catch(e){return!1}},h.IPv4.isValidCIDR=function(e){try{return this.parseCIDR(e),!0}catch(e){return!1}},h.IPv4.isValidFourPartDecimal=function(e){return!(!h.IPv4.isValid(e)||!e.match(/^(0|[1-9]\d*)(\.(0|[1-9]\d*)){3}$/))},h.IPv4.networkAddressFromCIDR=function(e){let t,r,i,n,s;try{for(t=this.parseCIDR(e),i=t[0].toByteArray(),s=this.subnetMaskFromPrefixLength(t[1]).toByteArray(),n=[],r=0;r<4;)n.push(parseInt(i[r],10)&parseInt(s[r],10)),r++;return new this(n)}catch(e){throw new Error("ipaddr: the address does not have IPv4 CIDR format")}},h.IPv4.parse=function(e){const t=this.parser(e);if(null===t)throw new Error("ipaddr: string is not formatted like an IPv4 Address");return new this(t)},h.IPv4.parseCIDR=function(e){let t;if(t=e.match(/^(.+)\/(\d+)$/)){const e=parseInt(t[2]);if(e>=0&&e<=32){const r=[this.parse(t[1]),e];return Object.defineProperty(r,"toString",{value:function(){return this.join("/")}}),r}}throw new Error("ipaddr: string is not formatted like an IPv4 CIDR range")},h.IPv4.parser=function(e){let t,i,n;if(t=e.match(r.fourOctet))return function(){const e=t.slice(1,6),r=[];for(let t=0;t<e.length;t++)i=e[t],r.push(l(i));return r}();if(t=e.match(r.longValue)){if(n=l(t[1]),n>4294967295||n<0)throw new Error("ipaddr: address outside defined range");return function(){const e=[];let t;for(t=0;t<=24;t+=8)e.push(n>>t&255);return e}().reverse()}return(t=e.match(r.twoOctet))?function(){const e=t.slice(1,4),r=[];if(n=l(e[1]),n>16777215||n<0)throw new Error("ipaddr: address outside defined range");return r.push(l(e[0])),r.push(n>>16&255),r.push(n>>8&255),r.push(255&n),r}():(t=e.match(r.threeOctet))?function(){const e=t.slice(1,5),r=[];if(n=l(e[2]),n>65535||n<0)throw new Error("ipaddr: address outside defined range");return r.push(l(e[0])),r.push(l(e[1])),r.push(n>>8&255),r.push(255&n),r}():null},h.IPv4.subnetMaskFromPrefixLength=function(e){if((e=parseInt(e))<0||e>32)throw new Error("ipaddr: invalid IPv4 prefix length");const t=[0,0,0,0];let r=0;const i=Math.floor(e/8);for(;r<i;)t[r]=255,r++;return i<4&&(t[i]=Math.pow(2,e%8)-1<<8-e%8),new this(t)},h.IPv6=function(){function e(e,t){let r,i;if(16===e.length)for(this.parts=[],r=0;r<=14;r+=2)this.parts.push(e[r]<<8|e[r+1]);else{if(8!==e.length)throw new Error("ipaddr: ipv6 part count should be 8 or 16");this.parts=e}for(r=0;r<this.parts.length;r++)if(i=this.parts[r],!(0<=i&&i<=65535))throw new Error("ipaddr: ipv6 part should fit in 16 bits");t&&(this.zoneId=t)}return e.prototype.SpecialRanges={unspecified:[new e([0,0,0,0,0,0,0,0]),128],linkLocal:[new e([65152,0,0,0,0,0,0,0]),10],multicast:[new e([65280,0,0,0,0,0,0,0]),8],loopback:[new e([0,0,0,0,0,0,0,1]),128],uniqueLocal:[new e([64512,0,0,0,0,0,0,0]),7],ipv4Mapped:[new e([0,0,0,0,0,65535,0,0]),96],discard:[new e([256,0,0,0,0,0,0,0]),64],rfc6145:[new e([0,0,0,0,65535,0,0,0]),96],rfc6052:[new e([100,65435,0,0,0,0,0,0]),96],"6to4":[new e([8194,0,0,0,0,0,0,0]),16],teredo:[new e([8193,0,0,0,0,0,0,0]),32],benchmarking:[new e([8193,2,0,0,0,0,0,0]),48],amt:[new e([8193,3,0,0,0,0,0,0]),32],as112v6:[[new e([8193,4,274,0,0,0,0,0]),48],[new e([9760,79,32768,0,0,0,0,0]),48]],deprecated:[new e([8193,16,0,0,0,0,0,0]),28],orchid2:[new e([8193,32,0,0,0,0,0,0]),28],droneRemoteIdProtocolEntityTags:[new e([8193,48,0,0,0,0,0,0]),28],reserved:[[new e([8193,0,0,0,0,0,0,0]),23],[new e([8193,3512,0,0,0,0,0,0]),32]]},e.prototype.isIPv4MappedAddress=function(){return"ipv4Mapped"===this.range()},e.prototype.kind=function(){return"ipv6"},e.prototype.match=function(e,t){let r;if(void 0===t&&(r=e,e=r[0],t=r[1]),"ipv6"!==e.kind())throw new Error("ipaddr: cannot match ipv6 address with non-ipv6 one");return u(this.parts,e.parts,16,t)},e.prototype.prefixLengthFromSubnetMask=function(){let e=0,t=!1;const r={0:16,32768:15,49152:14,57344:13,61440:12,63488:11,64512:10,65024:9,65280:8,65408:7,65472:6,65504:5,65520:4,65528:3,65532:2,65534:1,65535:0};let i,n;for(let s=7;s>=0;s-=1){if(i=this.parts[s],!(i in r))return null;if(n=r[i],t&&0!==n)return null;16!==n&&(t=!0),e+=n}return 128-e},e.prototype.range=function(){return h.subnetMatch(this,this.SpecialRanges)},e.prototype.toByteArray=function(){let e;const t=[],r=this.parts;for(let i=0;i<r.length;i++)e=r[i],t.push(e>>8),t.push(255&e);return t},e.prototype.toFixedLengthString=function(){const e=function(){const e=[];for(let t=0;t<this.parts.length;t++)e.push(p(this.parts[t].toString(16),4));return e}.call(this).join(":");let t="";return this.zoneId&&(t=`%${this.zoneId}`),e+t},e.prototype.toIPv4Address=function(){if(!this.isIPv4MappedAddress())throw new Error("ipaddr: trying to convert a generic ipv6 address to ipv4");const e=this.parts.slice(-2),t=e[0],r=e[1];return new h.IPv4([t>>8,255&t,r>>8,255&r])},e.prototype.toNormalizedString=function(){const e=function(){const e=[];for(let t=0;t<this.parts.length;t++)e.push(this.parts[t].toString(16));return e}.call(this).join(":");let t="";return this.zoneId&&(t=`%${this.zoneId}`),e+t},e.prototype.toRFC5952String=function(){const e=/((^|:)(0(:|$)){2,})/g,t=this.toNormalizedString();let r,i=0,n=-1;for(;r=e.exec(t);)r[0].length>n&&(i=r.index,n=r[0].length);return n<0?t:`${t.substring(0,i)}::${t.substring(i+n)}`},e.prototype.toString=function(){return this.toRFC5952String()},e}(),h.IPv6.broadcastAddressFromCIDR=function(e){try{const t=this.parseCIDR(e),r=t[0].toByteArray(),i=this.subnetMaskFromPrefixLength(t[1]).toByteArray(),n=[];let s=0;for(;s<16;)n.push(parseInt(r[s],10)|255^parseInt(i[s],10)),s++;return new this(n)}catch(e){throw new Error(`ipaddr: the address does not have IPv6 CIDR format (${e})`)}},h.IPv6.isIPv6=function(e){return null!==this.parser(e)},h.IPv6.isValid=function(e){if("string"==typeof e&&-1===e.indexOf(":"))return!1;try{const t=this.parser(e);return new this(t.parts,t.zoneId),!0}catch(e){return!1}},h.IPv6.isValidCIDR=function(e){if("string"==typeof e&&-1===e.indexOf(":"))return!1;try{return this.parseCIDR(e),!0}catch(e){return!1}},h.IPv6.networkAddressFromCIDR=function(e){let t,r,i,n,s;try{for(t=this.parseCIDR(e),i=t[0].toByteArray(),s=this.subnetMaskFromPrefixLength(t[1]).toByteArray(),n=[],r=0;r<16;)n.push(parseInt(i[r],10)&parseInt(s[r],10)),r++;return new this(n)}catch(e){throw new Error(`ipaddr: the address does not have IPv6 CIDR format (${e})`)}},h.IPv6.parse=function(e){const t=this.parser(e);if(null===t.parts)throw new Error("ipaddr: string is not formatted like an IPv6 Address");return new this(t.parts,t.zoneId)},h.IPv6.parseCIDR=function(e){let t,r,i;if((r=e.match(/^(.+)\/(\d+)$/))&&(t=parseInt(r[2]),t>=0&&t<=128))return i=[this.parse(r[1]),t],Object.defineProperty(i,"toString",{value:function(){return this.join("/")}}),i;throw new Error("ipaddr: string is not formatted like an IPv6 CIDR range")},h.IPv6.parser=function(e){let t,r,i,n,s,o;if(i=e.match(a.deprecatedTransitional))return this.parser(`::ffff:${i[1]}`);if(a.native.test(e))return c(e,8);if((i=e.match(a.transitional))&&(o=i[6]||"",t=i[1],i[1].endsWith("::")||(t=t.slice(0,-1)),t=c(t+o,6),t.parts)){for(s=[parseInt(i[2]),parseInt(i[3]),parseInt(i[4]),parseInt(i[5])],r=0;r<s.length;r++)if(n=s[r],!(0<=n&&n<=255))return null;return t.parts.push(s[0]<<8|s[1]),t.parts.push(s[2]<<8|s[3]),{parts:t.parts,zoneId:t.zoneId}}return null},h.IPv6.subnetMaskFromPrefixLength=function(e){if((e=parseInt(e))<0||e>128)throw new Error("ipaddr: invalid IPv6 prefix length");const t=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];let r=0;const i=Math.floor(e/8);for(;r<i;)t[r]=255,r++;return i<16&&(t[i]=Math.pow(2,e%8)-1<<8-e%8),new this(t)},h.fromByteArray=function(e){const t=e.length;if(4===t)return new h.IPv4(e);if(16===t)return new h.IPv6(e);throw new Error("ipaddr: the binary input is neither an IPv6 nor IPv4 address")},h.isValid=function(e){return h.IPv6.isValid(e)||h.IPv4.isValid(e)},h.isValidCIDR=function(e){return h.IPv6.isValidCIDR(e)||h.IPv4.isValidCIDR(e)},h.parse=function(e){if(h.IPv6.isValid(e))return h.IPv6.parse(e);if(h.IPv4.isValid(e))return h.IPv4.parse(e);throw new Error("ipaddr: the address has neither IPv6 nor IPv4 format")},h.parseCIDR=function(e){try{return h.IPv6.parseCIDR(e)}catch(t){try{return h.IPv4.parseCIDR(e)}catch(e){throw new Error("ipaddr: the address has neither IPv6 nor IPv4 CIDR format")}}},h.process=function(e){const t=this.parse(e);return"ipv6"===t.kind()&&t.isIPv4MappedAddress()?t.toIPv4Address():t},h.subnetMatch=function(e,t,r){let i,n,s,o;for(n in null==r&&(r="unicast"),t)if(Object.prototype.hasOwnProperty.call(t,n))for(s=t[n],!s[0]||s[0]instanceof Array||(s=[s]),i=0;i<s.length;i++)if(o=s[i],e.kind()===o[0].kind()&&e.match.apply(e,o))return n;return r},wr.exports?wr.exports=h:e.ipaddr=h}(r);var Ar,Sr,xr,Br=br.exports;class kr{static decodeIP(e){if(64===e.length&&0===parseInt(e,16))return"::/0";if(16!==e.length)return e;const t=parseInt(e.slice(8),16).toString(2).split("").reduce(((e,t)=>e+ +t),0);let r=e.slice(0,8).replace(/(.{2})/g,(e=>`${parseInt(e,16)}.`));return r=r.slice(0,-1),`${r}/${t}`}static toString(e){if(4===e.byteLength||16===e.byteLength){const t=new Uint8Array(e);return Br.fromByteArray(Array.from(t)).toString()}return this.decodeIP(l.ToHex(e))}static fromString(e){const t=Br.parse(e);return new Uint8Array(t.toByteArray()).buffer}}let Er=class{constructor(e={}){Object.assign(this,e)}toString(){return this.bmpString||this.printableString||this.teletexString||this.universalString||this.utf8String||""}};fr([sr({type:Bt.TeletexString})],Er.prototype,"teletexString",void 0),fr([sr({type:Bt.PrintableString})],Er.prototype,"printableString",void 0),fr([sr({type:Bt.UniversalString})],Er.prototype,"universalString",void 0),fr([sr({type:Bt.Utf8String})],Er.prototype,"utf8String",void 0),fr([sr({type:Bt.BmpString})],Er.prototype,"bmpString",void 0),Er=fr([nr({type:xt.Choice})],Er);let Cr=class extends Er{constructor(e={}){super(e),Object.assign(this,e)}toString(){return this.ia5String||(this.anyValue?l.ToHex(this.anyValue):super.toString())}};fr([sr({type:Bt.IA5String})],Cr.prototype,"ia5String",void 0),fr([sr({type:Bt.Any})],Cr.prototype,"anyValue",void 0),Cr=fr([nr({type:xt.Choice})],Cr);class Ir{constructor(e={}){this.type="",this.value=new Cr,Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],Ir.prototype,"type",void 0),fr([sr({type:Cr})],Ir.prototype,"value",void 0);let Or=Ar=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Ar.prototype)}};Or=Ar=fr([nr({type:xt.Set,itemType:Ir})],Or);let Nr=Sr=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Sr.prototype)}};Nr=Sr=fr([nr({type:xt.Sequence,itemType:Or})],Nr);let Tr=xr=class extends Nr{constructor(e){super(e),Object.setPrototypeOf(this,xr.prototype)}};Tr=xr=fr([nr({type:xt.Sequence})],Tr);const jr={fromASN:e=>kr.toString(Ut.fromASN(e)),toASN:e=>Ut.toASN(kr.fromString(e))};class Pr{constructor(e={}){this.typeId="",this.value=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],Pr.prototype,"typeId",void 0),fr([sr({type:Bt.Any,context:0})],Pr.prototype,"value",void 0);class Rr{constructor(e={}){this.partyName=new Er,Object.assign(this,e)}}fr([sr({type:Er,optional:!0,context:0,implicit:!0})],Rr.prototype,"nameAssigner",void 0),fr([sr({type:Er,context:1,implicit:!0})],Rr.prototype,"partyName",void 0);let Ur=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:Pr,context:0,implicit:!0})],Ur.prototype,"otherName",void 0),fr([sr({type:Bt.IA5String,context:1,implicit:!0})],Ur.prototype,"rfc822Name",void 0),fr([sr({type:Bt.IA5String,context:2,implicit:!0})],Ur.prototype,"dNSName",void 0),fr([sr({type:Bt.Any,context:3,implicit:!0})],Ur.prototype,"x400Address",void 0),fr([sr({type:Tr,context:4,implicit:!1})],Ur.prototype,"directoryName",void 0),fr([sr({type:Rr,context:5})],Ur.prototype,"ediPartyName",void 0),fr([sr({type:Bt.IA5String,context:6,implicit:!0})],Ur.prototype,"uniformResourceIdentifier",void 0),fr([sr({type:Bt.OctetString,context:7,implicit:!0,converter:jr})],Ur.prototype,"iPAddress",void 0),fr([sr({type:Bt.ObjectIdentifier,context:8,implicit:!0})],Ur.prototype,"registeredID",void 0),Ur=fr([nr({type:xt.Choice})],Ur);const Dr="*******.5.5.7",Vr=`${Dr}.3`,Lr=`${Dr}.48`,Hr=`${Lr}.1`,$r=`${Lr}.2`,Mr=`${Lr}.3`,Fr=`${Lr}.5`,Kr="2.5.29";var zr;const _r=`${`${Dr}.1`}.1`;class qr{constructor(e={}){this.accessMethod="",this.accessLocation=new Ur,Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],qr.prototype,"accessMethod",void 0),fr([sr({type:Ur})],qr.prototype,"accessLocation",void 0);let Wr=zr=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,zr.prototype)}};Wr=zr=fr([nr({type:xt.Sequence,itemType:qr})],Wr);const Gr=`${Kr}.35`;class Jr extends Ct{}class Xr{constructor(e={}){e&&Object.assign(this,e)}}fr([sr({type:Jr,context:0,optional:!0,implicit:!0})],Xr.prototype,"keyIdentifier",void 0),fr([sr({type:Ur,context:1,optional:!0,implicit:!0,repeated:"sequence"})],Xr.prototype,"authorityCertIssuer",void 0),fr([sr({type:Bt.Integer,context:2,optional:!0,implicit:!0,converter:Tt})],Xr.prototype,"authorityCertSerialNumber",void 0);const Zr=`${Kr}.19`;class Yr{constructor(e={}){this.cA=!1,Object.assign(this,e)}}var Qr;fr([sr({type:Bt.Boolean,defaultValue:!1})],Yr.prototype,"cA",void 0),fr([sr({type:Bt.Integer,optional:!0})],Yr.prototype,"pathLenConstraint",void 0);let ei=Qr=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Qr.prototype)}};var ti;ei=Qr=fr([nr({type:xt.Sequence,itemType:Ur})],ei);const ri=`${Kr}.29`;let ii=ti=class extends ei{constructor(e){super(e),Object.setPrototypeOf(this,ti.prototype)}};var ni;ii=ti=fr([nr({type:xt.Sequence})],ii);const si=`${Kr}.32`;let oi=class{constructor(e={}){Object.assign(this,e)}toString(){return this.ia5String||this.visibleString||this.bmpString||this.utf8String||""}};fr([sr({type:Bt.IA5String})],oi.prototype,"ia5String",void 0),fr([sr({type:Bt.VisibleString})],oi.prototype,"visibleString",void 0),fr([sr({type:Bt.BmpString})],oi.prototype,"bmpString",void 0),fr([sr({type:Bt.Utf8String})],oi.prototype,"utf8String",void 0),oi=fr([nr({type:xt.Choice})],oi);class ai{constructor(e={}){this.organization=new oi,this.noticeNumbers=[],Object.assign(this,e)}}fr([sr({type:oi})],ai.prototype,"organization",void 0),fr([sr({type:Bt.Integer,repeated:"sequence"})],ai.prototype,"noticeNumbers",void 0);class ci{constructor(e={}){Object.assign(this,e)}}fr([sr({type:ai,optional:!0})],ci.prototype,"noticeRef",void 0),fr([sr({type:oi,optional:!0})],ci.prototype,"explicitText",void 0);let ui=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:Bt.IA5String})],ui.prototype,"cPSuri",void 0),fr([sr({type:ci})],ui.prototype,"userNotice",void 0),ui=fr([nr({type:xt.Choice})],ui);class li{constructor(e={}){this.policyQualifierId="",this.qualifier=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],li.prototype,"policyQualifierId",void 0),fr([sr({type:Bt.Any})],li.prototype,"qualifier",void 0);class pi{constructor(e={}){this.policyIdentifier="",Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],pi.prototype,"policyIdentifier",void 0),fr([sr({type:li,repeated:"sequence",optional:!0})],pi.prototype,"policyQualifiers",void 0);let hi=ni=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,ni.prototype)}};hi=ni=fr([nr({type:xt.Sequence,itemType:pi})],hi);let fi=class{constructor(e=0){this.value=e}};fr([sr({type:Bt.Integer})],fi.prototype,"value",void 0),fi=fr([nr({type:xt.Choice})],fi);let yi=class extends fi{};var di;yi=fr([nr({type:xt.Choice})],yi);const gi=`${Kr}.31`;var vi;!function(e){e[e.unused=1]="unused",e[e.keyCompromise=2]="keyCompromise",e[e.cACompromise=4]="cACompromise",e[e.affiliationChanged=8]="affiliationChanged",e[e.superseded=16]="superseded",e[e.cessationOfOperation=32]="cessationOfOperation",e[e.certificateHold=64]="certificateHold",e[e.privilegeWithdrawn=128]="privilegeWithdrawn",e[e.aACompromise=256]="aACompromise"}(vi||(vi={}));class mi extends Et{toJSON(){const e=[],t=this.toNumber();return t&vi.aACompromise&&e.push("aACompromise"),t&vi.affiliationChanged&&e.push("affiliationChanged"),t&vi.cACompromise&&e.push("cACompromise"),t&vi.certificateHold&&e.push("certificateHold"),t&vi.cessationOfOperation&&e.push("cessationOfOperation"),t&vi.keyCompromise&&e.push("keyCompromise"),t&vi.privilegeWithdrawn&&e.push("privilegeWithdrawn"),t&vi.superseded&&e.push("superseded"),t&vi.unused&&e.push("unused"),e}toString(){return`[${this.toJSON().join(", ")}]`}}let wi=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:Ur,context:0,repeated:"sequence",implicit:!0})],wi.prototype,"fullName",void 0),fr([sr({type:Or,context:1,implicit:!0})],wi.prototype,"nameRelativeToCRLIssuer",void 0),wi=fr([nr({type:xt.Choice})],wi);class bi{constructor(e={}){Object.assign(this,e)}}fr([sr({type:wi,context:0,optional:!0})],bi.prototype,"distributionPoint",void 0),fr([sr({type:mi,context:1,optional:!0,implicit:!0})],bi.prototype,"reasons",void 0),fr([sr({type:Ur,context:2,optional:!0,repeated:"sequence",implicit:!0})],bi.prototype,"cRLIssuer",void 0);let Ai=di=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,di.prototype)}};var Si;Ai=di=fr([nr({type:xt.Sequence,itemType:bi})],Ai);let xi=Si=class extends Ai{constructor(e){super(e),Object.setPrototypeOf(this,Si.prototype)}};xi=Si=fr([nr({type:xt.Sequence,itemType:bi})],xi);class Bi{constructor(e={}){this.onlyContainsUserCerts=Bi.ONLY,this.onlyContainsCACerts=Bi.ONLY,this.indirectCRL=Bi.ONLY,this.onlyContainsAttributeCerts=Bi.ONLY,Object.assign(this,e)}}Bi.ONLY=!1,fr([sr({type:wi,context:0,optional:!0})],Bi.prototype,"distributionPoint",void 0),fr([sr({type:Bt.Boolean,context:1,defaultValue:Bi.ONLY,implicit:!0})],Bi.prototype,"onlyContainsUserCerts",void 0),fr([sr({type:Bt.Boolean,context:2,defaultValue:Bi.ONLY,implicit:!0})],Bi.prototype,"onlyContainsCACerts",void 0),fr([sr({type:mi,context:3,optional:!0,implicit:!0})],Bi.prototype,"onlySomeReasons",void 0),fr([sr({type:Bt.Boolean,context:4,defaultValue:Bi.ONLY,implicit:!0})],Bi.prototype,"indirectCRL",void 0),fr([sr({type:Bt.Boolean,context:5,defaultValue:Bi.ONLY,implicit:!0})],Bi.prototype,"onlyContainsAttributeCerts",void 0);const ki=`${Kr}.21`;var Ei;!function(e){e[e.unspecified=0]="unspecified",e[e.keyCompromise=1]="keyCompromise",e[e.cACompromise=2]="cACompromise",e[e.affiliationChanged=3]="affiliationChanged",e[e.superseded=4]="superseded",e[e.cessationOfOperation=5]="cessationOfOperation",e[e.certificateHold=6]="certificateHold",e[e.removeFromCRL=8]="removeFromCRL",e[e.privilegeWithdrawn=9]="privilegeWithdrawn",e[e.aACompromise=10]="aACompromise"}(Ei||(Ei={}));let Ci=class{constructor(e=Ei.unspecified){this.reason=Ei.unspecified,this.reason=e}toJSON(){return Ei[this.reason]}toString(){return this.toJSON()}};var Ii;fr([sr({type:Bt.Enumerated})],Ci.prototype,"reason",void 0),Ci=fr([nr({type:xt.Choice})],Ci);const Oi=`${Kr}.37`;let Ni=Ii=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Ii.prototype)}};Ni=Ii=fr([nr({type:xt.Sequence,itemType:Bt.ObjectIdentifier})],Ni);const Ti=`${Vr}.1`,ji=`${Vr}.2`,Pi=`${Vr}.3`,Ri=`${Vr}.4`,Ui=`${Vr}.8`,Di=`${Vr}.9`;let Vi=class{constructor(e=new ArrayBuffer(0)){this.value=e}};fr([sr({type:Bt.Integer,converter:Tt})],Vi.prototype,"value",void 0),Vi=fr([nr({type:xt.Choice})],Vi);const Li=`${Kr}.24`;let Hi=class{constructor(e){this.value=new Date,e&&(this.value=e)}};var $i;fr([sr({type:Bt.GeneralizedTime})],Hi.prototype,"value",void 0),Hi=fr([nr({type:xt.Choice})],Hi);let Mi=$i=class extends ei{constructor(e){super(e),Object.setPrototypeOf(this,$i.prototype)}};Mi=$i=fr([nr({type:xt.Sequence})],Mi);const Fi=`${Kr}.15`;var Ki,zi;!function(e){e[e.digitalSignature=1]="digitalSignature",e[e.nonRepudiation=2]="nonRepudiation",e[e.keyEncipherment=4]="keyEncipherment",e[e.dataEncipherment=8]="dataEncipherment",e[e.keyAgreement=16]="keyAgreement",e[e.keyCertSign=32]="keyCertSign",e[e.cRLSign=64]="cRLSign",e[e.encipherOnly=128]="encipherOnly",e[e.decipherOnly=256]="decipherOnly"}(Ki||(Ki={}));class _i extends Et{toJSON(){const e=this.toNumber(),t=[];return e&Ki.cRLSign&&t.push("crlSign"),e&Ki.dataEncipherment&&t.push("dataEncipherment"),e&Ki.decipherOnly&&t.push("decipherOnly"),e&Ki.digitalSignature&&t.push("digitalSignature"),e&Ki.encipherOnly&&t.push("encipherOnly"),e&Ki.keyAgreement&&t.push("keyAgreement"),e&Ki.keyCertSign&&t.push("keyCertSign"),e&Ki.keyEncipherment&&t.push("keyEncipherment"),e&Ki.nonRepudiation&&t.push("nonRepudiation"),t}toString(){return`[${this.toJSON().join(", ")}]`}}class qi{constructor(e={}){this.base=new Ur,this.minimum=0,Object.assign(this,e)}}fr([sr({type:Ur})],qi.prototype,"base",void 0),fr([sr({type:Bt.Integer,context:0,defaultValue:0,implicit:!0})],qi.prototype,"minimum",void 0),fr([sr({type:Bt.Integer,context:1,optional:!0,implicit:!0})],qi.prototype,"maximum",void 0);let Wi=zi=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,zi.prototype)}};Wi=zi=fr([nr({type:xt.Sequence,itemType:qi})],Wi);class Gi{constructor(e={}){Object.assign(this,e)}}fr([sr({type:Wi,context:0,optional:!0,implicit:!0})],Gi.prototype,"permittedSubtrees",void 0),fr([sr({type:Wi,context:1,optional:!0,implicit:!0})],Gi.prototype,"excludedSubtrees",void 0);class Ji{constructor(e={}){Object.assign(this,e)}}var Xi;fr([sr({type:Bt.Integer,context:0,implicit:!0,optional:!0,converter:Tt})],Ji.prototype,"requireExplicitPolicy",void 0),fr([sr({type:Bt.Integer,context:1,implicit:!0,optional:!0,converter:Tt})],Ji.prototype,"inhibitPolicyMapping",void 0);class Zi{constructor(e={}){this.issuerDomainPolicy="",this.subjectDomainPolicy="",Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],Zi.prototype,"issuerDomainPolicy",void 0),fr([sr({type:Bt.ObjectIdentifier})],Zi.prototype,"subjectDomainPolicy",void 0);let Yi=Xi=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Xi.prototype)}};var Qi;Yi=Xi=fr([nr({type:xt.Sequence,itemType:Zi})],Yi);const en=`${Kr}.17`;let tn=Qi=class extends ei{constructor(e){super(e),Object.setPrototypeOf(this,Qi.prototype)}};tn=Qi=fr([nr({type:xt.Sequence})],tn);let rn=class{constructor(e={}){this.type="",this.values=[],Object.assign(this,e)}};var nn;fr([sr({type:Bt.ObjectIdentifier})],rn.prototype,"type",void 0),fr([sr({type:Bt.Any,repeated:"set"})],rn.prototype,"values",void 0);let sn=nn=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,nn.prototype)}};sn=nn=fr([nr({type:xt.Sequence,itemType:rn})],sn);const on=`${Kr}.14`;class an extends Jr{}class cn{constructor(e={}){Object.assign(this,e)}}var un,ln;fr([sr({type:Bt.GeneralizedTime,context:0,implicit:!0,optional:!0})],cn.prototype,"notBefore",void 0),fr([sr({type:Bt.GeneralizedTime,context:1,implicit:!0,optional:!0})],cn.prototype,"notAfter",void 0),function(e){e[e.keyUpdateAllowed=1]="keyUpdateAllowed",e[e.newExtensions=2]="newExtensions",e[e.pKIXCertificate=4]="pKIXCertificate"}(un||(un={}));class pn extends Et{toJSON(){const e=[],t=this.toNumber();return t&un.pKIXCertificate&&e.push("pKIXCertificate"),t&un.newExtensions&&e.push("newExtensions"),t&un.keyUpdateAllowed&&e.push("keyUpdateAllowed"),e}toString(){return`[${this.toJSON().join(", ")}]`}}class hn{constructor(e={}){this.entrustVers="",this.entrustInfoFlags=new pn,Object.assign(this,e)}}fr([sr({type:Bt.GeneralString})],hn.prototype,"entrustVers",void 0),fr([sr({type:pn})],hn.prototype,"entrustInfoFlags",void 0);let fn=ln=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,ln.prototype)}};fn=ln=fr([nr({type:xt.Sequence,itemType:qr})],fn);class yn{constructor(e={}){this.algorithm="",Object.assign(this,e)}isEqual(e){return e instanceof yn&&e.algorithm==this.algorithm&&(e.parameters&&this.parameters&&p(e.parameters,this.parameters)||e.parameters===this.parameters)}}fr([sr({type:Bt.ObjectIdentifier})],yn.prototype,"algorithm",void 0),fr([sr({type:Bt.Any,optional:!0})],yn.prototype,"parameters",void 0);class dn{constructor(e={}){this.algorithm=new yn,this.subjectPublicKey=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:yn})],dn.prototype,"algorithm",void 0),fr([sr({type:Bt.BitString})],dn.prototype,"subjectPublicKey",void 0);let gn=class{constructor(e){if(e)if("string"==typeof e||"number"==typeof e||e instanceof Date){const t=new Date(e);t.getUTCFullYear()>2049?this.generalTime=t:this.utcTime=t}else Object.assign(this,e)}getTime(){const e=this.utcTime||this.generalTime;if(!e)throw new Error("Cannot get time from CHOICE object");return e}};fr([sr({type:Bt.UTCTime})],gn.prototype,"utcTime",void 0),fr([sr({type:Bt.GeneralizedTime})],gn.prototype,"generalTime",void 0),gn=fr([nr({type:xt.Choice})],gn);class vn{constructor(e){this.notBefore=new gn(new Date),this.notAfter=new gn(new Date),e&&(this.notBefore=new gn(e.notBefore),this.notAfter=new gn(e.notAfter))}}var mn;fr([sr({type:gn})],vn.prototype,"notBefore",void 0),fr([sr({type:gn})],vn.prototype,"notAfter",void 0);let wn=class e{constructor(t={}){this.extnID="",this.critical=e.CRITICAL,this.extnValue=new Ct,Object.assign(this,t)}};wn.CRITICAL=!1,fr([sr({type:Bt.ObjectIdentifier})],wn.prototype,"extnID",void 0),fr([sr({type:Bt.Boolean,defaultValue:wn.CRITICAL})],wn.prototype,"critical",void 0),fr([sr({type:Ct})],wn.prototype,"extnValue",void 0);let bn=mn=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,mn.prototype)}};var An;bn=mn=fr([nr({type:xt.Sequence,itemType:wn})],bn),function(e){e[e.v1=0]="v1",e[e.v2=1]="v2",e[e.v3=2]="v3"}(An||(An={}));class Sn{constructor(e={}){this.version=An.v1,this.serialNumber=new ArrayBuffer(0),this.signature=new yn,this.issuer=new Tr,this.validity=new vn,this.subject=new Tr,this.subjectPublicKeyInfo=new dn,Object.assign(this,e)}}fr([sr({type:Bt.Integer,context:0,defaultValue:An.v1})],Sn.prototype,"version",void 0),fr([sr({type:Bt.Integer,converter:Tt})],Sn.prototype,"serialNumber",void 0),fr([sr({type:yn})],Sn.prototype,"signature",void 0),fr([sr({type:Tr})],Sn.prototype,"issuer",void 0),fr([sr({type:vn})],Sn.prototype,"validity",void 0),fr([sr({type:Tr})],Sn.prototype,"subject",void 0),fr([sr({type:dn})],Sn.prototype,"subjectPublicKeyInfo",void 0),fr([sr({type:Bt.BitString,context:1,implicit:!0,optional:!0})],Sn.prototype,"issuerUniqueID",void 0),fr([sr({type:Bt.BitString,context:2,implicit:!0,optional:!0})],Sn.prototype,"subjectUniqueID",void 0),fr([sr({type:bn,context:3,optional:!0})],Sn.prototype,"extensions",void 0);class xn{constructor(e={}){this.tbsCertificate=new Sn,this.signatureAlgorithm=new yn,this.signatureValue=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Sn})],xn.prototype,"tbsCertificate",void 0),fr([sr({type:yn})],xn.prototype,"signatureAlgorithm",void 0),fr([sr({type:Bt.BitString})],xn.prototype,"signatureValue",void 0);class Bn{constructor(e={}){this.userCertificate=new ArrayBuffer(0),this.revocationDate=new gn,Object.assign(this,e)}}fr([sr({type:Bt.Integer,converter:Tt})],Bn.prototype,"userCertificate",void 0),fr([sr({type:gn})],Bn.prototype,"revocationDate",void 0),fr([sr({type:wn,optional:!0,repeated:"sequence"})],Bn.prototype,"crlEntryExtensions",void 0);class kn{constructor(e={}){this.signature=new yn,this.issuer=new Tr,this.thisUpdate=new gn,Object.assign(this,e)}}fr([sr({type:Bt.Integer,optional:!0})],kn.prototype,"version",void 0),fr([sr({type:yn})],kn.prototype,"signature",void 0),fr([sr({type:Tr})],kn.prototype,"issuer",void 0),fr([sr({type:gn})],kn.prototype,"thisUpdate",void 0),fr([sr({type:gn,optional:!0})],kn.prototype,"nextUpdate",void 0),fr([sr({type:Bn,repeated:"sequence",optional:!0})],kn.prototype,"revokedCertificates",void 0),fr([sr({type:wn,optional:!0,context:0,repeated:"sequence"})],kn.prototype,"crlExtensions",void 0);class En{constructor(e={}){this.tbsCertList=new kn,this.signatureAlgorithm=new yn,this.signature=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:kn})],En.prototype,"tbsCertList",void 0),fr([sr({type:yn})],En.prototype,"signatureAlgorithm",void 0),fr([sr({type:Bt.BitString})],En.prototype,"signature",void 0);class Cn{constructor(e={}){this.issuer=new Tr,this.serialNumber=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Tr})],Cn.prototype,"issuer",void 0),fr([sr({type:Bt.Integer,converter:Tt})],Cn.prototype,"serialNumber",void 0);let In=class{constructor(e={}){Object.assign(this,e)}};var On;fr([sr({type:an,context:0,implicit:!0})],In.prototype,"subjectKeyIdentifier",void 0),fr([sr({type:Cn})],In.prototype,"issuerAndSerialNumber",void 0),In=fr([nr({type:xt.Choice})],In),function(e){e[e.v0=0]="v0",e[e.v1=1]="v1",e[e.v2=2]="v2",e[e.v3=3]="v3",e[e.v4=4]="v4",e[e.v5=5]="v5"}(On||(On={}));let Nn=class extends yn{};Nn=fr([nr({type:xt.Sequence})],Nn);let Tn=class extends yn{};Tn=fr([nr({type:xt.Sequence})],Tn);let jn=class extends yn{};jn=fr([nr({type:xt.Sequence})],jn);let Pn=class extends yn{};Pn=fr([nr({type:xt.Sequence})],Pn);let Rn=class extends yn{};Rn=fr([nr({type:xt.Sequence})],Rn);let Un=class extends yn{};Un=fr([nr({type:xt.Sequence})],Un);let Dn=class{constructor(e={}){this.attrType="",this.attrValues=[],Object.assign(this,e)}};var Vn;fr([sr({type:Bt.ObjectIdentifier})],Dn.prototype,"attrType",void 0),fr([sr({type:Bt.Any,repeated:"set"})],Dn.prototype,"attrValues",void 0);class Ln{constructor(e={}){this.version=On.v0,this.sid=new In,this.digestAlgorithm=new Nn,this.signatureAlgorithm=new Tn,this.signature=new Ct,Object.assign(this,e)}}fr([sr({type:Bt.Integer})],Ln.prototype,"version",void 0),fr([sr({type:In})],Ln.prototype,"sid",void 0),fr([sr({type:Nn})],Ln.prototype,"digestAlgorithm",void 0),fr([sr({type:Dn,repeated:"set",context:0,implicit:!0,optional:!0})],Ln.prototype,"signedAttrs",void 0),fr([sr({type:Tn})],Ln.prototype,"signatureAlgorithm",void 0),fr([sr({type:Ct})],Ln.prototype,"signature",void 0),fr([sr({type:Dn,repeated:"set",context:1,implicit:!0,optional:!0})],Ln.prototype,"unsignedAttrs",void 0);let Hn=Vn=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Vn.prototype)}};Hn=Vn=fr([nr({type:xt.Set,itemType:Ln})],Hn);let $n=class extends gn{};$n=fr([nr({type:xt.Choice})],$n);let Mn=class extends Ln{};Mn=fr([nr({type:xt.Sequence})],Mn);class Fn{constructor(e={}){this.acIssuer=new Ur,this.acSerial=0,this.attrs=[],Object.assign(this,e)}}var Kn;fr([sr({type:Ur})],Fn.prototype,"acIssuer",void 0),fr([sr({type:Bt.Integer})],Fn.prototype,"acSerial",void 0),fr([sr({type:rn,repeated:"sequence"})],Fn.prototype,"attrs",void 0);let zn=Kn=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Kn.prototype)}};zn=Kn=fr([nr({type:xt.Sequence,itemType:Bt.ObjectIdentifier})],zn);class _n{constructor(e={}){this.permitUnSpecified=!0,Object.assign(this,e)}}fr([sr({type:Bt.Integer,optional:!0})],_n.prototype,"pathLenConstraint",void 0),fr([sr({type:zn,implicit:!0,context:0,optional:!0})],_n.prototype,"permittedAttrs",void 0),fr([sr({type:zn,implicit:!0,context:1,optional:!0})],_n.prototype,"excludedAttrs",void 0),fr([sr({type:Bt.Boolean,defaultValue:!0})],_n.prototype,"permitUnSpecified",void 0);class qn{constructor(e={}){this.issuer=new ei,this.serial=new ArrayBuffer(0),this.issuerUID=new ArrayBuffer(0),Object.assign(this,e)}}var Wn;fr([sr({type:ei})],qn.prototype,"issuer",void 0),fr([sr({type:Bt.Integer,converter:Tt})],qn.prototype,"serial",void 0),fr([sr({type:Bt.BitString,optional:!0})],qn.prototype,"issuerUID",void 0),function(e){e[e.publicKey=0]="publicKey",e[e.publicKeyCert=1]="publicKeyCert",e[e.otherObjectTypes=2]="otherObjectTypes"}(Wn||(Wn={}));class Gn{constructor(e={}){this.digestedObjectType=Wn.publicKey,this.digestAlgorithm=new yn,this.objectDigest=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.Enumerated})],Gn.prototype,"digestedObjectType",void 0),fr([sr({type:Bt.ObjectIdentifier,optional:!0})],Gn.prototype,"otherObjectTypeID",void 0),fr([sr({type:yn})],Gn.prototype,"digestAlgorithm",void 0),fr([sr({type:Bt.BitString})],Gn.prototype,"objectDigest",void 0);class Jn{constructor(e={}){Object.assign(this,e)}}fr([sr({type:ei,optional:!0})],Jn.prototype,"issuerName",void 0),fr([sr({type:qn,context:0,implicit:!0,optional:!0})],Jn.prototype,"baseCertificateID",void 0),fr([sr({type:Gn,context:1,implicit:!0,optional:!0})],Jn.prototype,"objectDigestInfo",void 0);let Xn=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:Ur,repeated:"sequence"})],Xn.prototype,"v1Form",void 0),fr([sr({type:Jn,context:0,implicit:!0})],Xn.prototype,"v2Form",void 0),Xn=fr([nr({type:xt.Choice})],Xn);class Zn{constructor(e={}){this.notBeforeTime=new Date,this.notAfterTime=new Date,Object.assign(this,e)}}fr([sr({type:Bt.GeneralizedTime})],Zn.prototype,"notBeforeTime",void 0),fr([sr({type:Bt.GeneralizedTime})],Zn.prototype,"notAfterTime",void 0);class Yn{constructor(e={}){Object.assign(this,e)}}var Qn,es,ts;fr([sr({type:qn,implicit:!0,context:0,optional:!0})],Yn.prototype,"baseCertificateID",void 0),fr([sr({type:ei,implicit:!0,context:1,optional:!0})],Yn.prototype,"entityName",void 0),fr([sr({type:Gn,implicit:!0,context:2,optional:!0})],Yn.prototype,"objectDigestInfo",void 0),function(e){e[e.v2=1]="v2"}(Qn||(Qn={}));class rs{constructor(e={}){this.version=Qn.v2,this.holder=new Yn,this.issuer=new Xn,this.signature=new yn,this.serialNumber=new ArrayBuffer(0),this.attrCertValidityPeriod=new Zn,this.attributes=[],Object.assign(this,e)}}fr([sr({type:Bt.Integer})],rs.prototype,"version",void 0),fr([sr({type:Yn})],rs.prototype,"holder",void 0),fr([sr({type:Xn})],rs.prototype,"issuer",void 0),fr([sr({type:yn})],rs.prototype,"signature",void 0),fr([sr({type:Bt.Integer,converter:Tt})],rs.prototype,"serialNumber",void 0),fr([sr({type:Zn})],rs.prototype,"attrCertValidityPeriod",void 0),fr([sr({type:rn,repeated:"sequence"})],rs.prototype,"attributes",void 0),fr([sr({type:Bt.BitString,optional:!0})],rs.prototype,"issuerUniqueID",void 0),fr([sr({type:bn,optional:!0})],rs.prototype,"extensions",void 0);class is{constructor(e={}){this.acinfo=new rs,this.signatureAlgorithm=new yn,this.signatureValue=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:rs})],is.prototype,"acinfo",void 0),fr([sr({type:yn})],is.prototype,"signatureAlgorithm",void 0),fr([sr({type:Bt.BitString})],is.prototype,"signatureValue",void 0),function(e){e[e.unmarked=1]="unmarked",e[e.unclassified=2]="unclassified",e[e.restricted=4]="restricted",e[e.confidential=8]="confidential",e[e.secret=16]="secret",e[e.topSecret=32]="topSecret"}(es||(es={}));class ns extends Et{}class ss{constructor(e={}){this.type="",this.value=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier,implicit:!0,context:0})],ss.prototype,"type",void 0),fr([sr({type:Bt.Any,implicit:!0,context:1})],ss.prototype,"value",void 0);class os{constructor(e={}){this.policyId="",this.classList=new ns(es.unclassified),Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],os.prototype,"policyId",void 0),fr([sr({type:ns,defaultValue:new ns(es.unclassified)})],os.prototype,"classList",void 0),fr([sr({type:ss,repeated:"set"})],os.prototype,"securityCategories",void 0);class as{constructor(e={}){Object.assign(this,e)}}fr([sr({type:Ct})],as.prototype,"cotets",void 0),fr([sr({type:Bt.ObjectIdentifier})],as.prototype,"oid",void 0),fr([sr({type:Bt.Utf8String})],as.prototype,"string",void 0);class cs{constructor(e={}){this.values=[],Object.assign(this,e)}}fr([sr({type:ei,implicit:!0,context:0,optional:!0})],cs.prototype,"policyAuthority",void 0),fr([sr({type:as,repeated:"sequence"})],cs.prototype,"values",void 0);class us{constructor(e={}){this.targetCertificate=new qn,Object.assign(this,e)}}fr([sr({type:qn})],us.prototype,"targetCertificate",void 0),fr([sr({type:Ur,optional:!0})],us.prototype,"targetName",void 0),fr([sr({type:Gn,optional:!0})],us.prototype,"certDigestInfo",void 0);let ls=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:Ur,context:0,implicit:!0})],ls.prototype,"targetName",void 0),fr([sr({type:Ur,context:1,implicit:!0})],ls.prototype,"targetGroup",void 0),fr([sr({type:us,context:2,implicit:!0})],ls.prototype,"targetCert",void 0),ls=fr([nr({type:xt.Choice})],ls);let ps=ts=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,ts.prototype)}};var hs;ps=ts=fr([nr({type:xt.Sequence,itemType:ls})],ps);let fs=hs=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,hs.prototype)}};fs=hs=fr([nr({type:xt.Sequence,itemType:ps})],fs);class ys{constructor(e={}){Object.assign(this,e)}}fr([sr({type:ei,implicit:!0,context:0,optional:!0})],ys.prototype,"roleAuthority",void 0),fr([sr({type:Ur,implicit:!0,context:1})],ys.prototype,"roleName",void 0);class ds{constructor(e={}){this.service=new Ur,this.ident=new Ur,Object.assign(this,e)}}var gs;fr([sr({type:Ur})],ds.prototype,"service",void 0),fr([sr({type:Ur})],ds.prototype,"ident",void 0),fr([sr({type:Ct,optional:!0})],ds.prototype,"authInfo",void 0);class vs{constructor(e={}){this.otherCertFormat="",this.otherCert=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],vs.prototype,"otherCertFormat",void 0),fr([sr({type:Bt.Any})],vs.prototype,"otherCert",void 0);let ms=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:xn})],ms.prototype,"certificate",void 0),fr([sr({type:is,context:2,implicit:!0})],ms.prototype,"v2AttrCert",void 0),fr([sr({type:vs,context:3,implicit:!0})],ms.prototype,"other",void 0),ms=fr([nr({type:xt.Choice})],ms);let ws=gs=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,gs.prototype)}};ws=gs=fr([nr({type:xt.Set,itemType:ms})],ws);class bs{constructor(e={}){this.contentType="",this.content=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],bs.prototype,"contentType",void 0),fr([sr({type:Bt.Any,context:0})],bs.prototype,"content",void 0);let As=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:Ct})],As.prototype,"single",void 0),fr([sr({type:Bt.Any})],As.prototype,"any",void 0),As=fr([nr({type:xt.Choice})],As);class Ss{constructor(e={}){this.eContentType="",Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],Ss.prototype,"eContentType",void 0),fr([sr({type:As,context:0,optional:!0})],Ss.prototype,"eContent",void 0);let xs=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:Ct,context:0,implicit:!0,optional:!0})],xs.prototype,"value",void 0),fr([sr({type:Ct,converter:Dt,context:0,implicit:!0,optional:!0,repeated:"sequence"})],xs.prototype,"constructedValue",void 0),xs=fr([nr({type:xt.Choice})],xs);class Bs{constructor(e={}){this.contentType="",this.contentEncryptionAlgorithm=new Pn,Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],Bs.prototype,"contentType",void 0),fr([sr({type:Pn})],Bs.prototype,"contentEncryptionAlgorithm",void 0),fr([sr({type:xs,optional:!0})],Bs.prototype,"encryptedContent",void 0);class ks{constructor(e={}){this.keyAttrId="",Object.assign(this,e)}}var Es;fr([sr({type:Bt.ObjectIdentifier})],ks.prototype,"keyAttrId",void 0),fr([sr({type:Bt.Any,optional:!0})],ks.prototype,"keyAttr",void 0);class Cs{constructor(e={}){this.subjectKeyIdentifier=new an,Object.assign(this,e)}}fr([sr({type:an})],Cs.prototype,"subjectKeyIdentifier",void 0),fr([sr({type:Bt.GeneralizedTime,optional:!0})],Cs.prototype,"date",void 0),fr([sr({type:ks,optional:!0})],Cs.prototype,"other",void 0);let Is=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:Cs,context:0,implicit:!0,optional:!0})],Is.prototype,"rKeyId",void 0),fr([sr({type:Cn,optional:!0})],Is.prototype,"issuerAndSerialNumber",void 0),Is=fr([nr({type:xt.Choice})],Is);class Os{constructor(e={}){this.rid=new Is,this.encryptedKey=new Ct,Object.assign(this,e)}}fr([sr({type:Is})],Os.prototype,"rid",void 0),fr([sr({type:Ct})],Os.prototype,"encryptedKey",void 0);let Ns=Es=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Es.prototype)}};Ns=Es=fr([nr({type:xt.Sequence,itemType:Os})],Ns);class Ts{constructor(e={}){this.algorithm=new yn,this.publicKey=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:yn})],Ts.prototype,"algorithm",void 0),fr([sr({type:Bt.BitString})],Ts.prototype,"publicKey",void 0);let js=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:an,context:0,implicit:!0,optional:!0})],js.prototype,"subjectKeyIdentifier",void 0),fr([sr({type:Ts,context:1,implicit:!0,optional:!0})],js.prototype,"originatorKey",void 0),fr([sr({type:Cn,optional:!0})],js.prototype,"issuerAndSerialNumber",void 0),js=fr([nr({type:xt.Choice})],js);class Ps{constructor(e={}){this.version=On.v3,this.originator=new js,this.keyEncryptionAlgorithm=new jn,this.recipientEncryptedKeys=new Ns,Object.assign(this,e)}}fr([sr({type:Bt.Integer})],Ps.prototype,"version",void 0),fr([sr({type:js,context:0})],Ps.prototype,"originator",void 0),fr([sr({type:Ct,context:1,optional:!0})],Ps.prototype,"ukm",void 0),fr([sr({type:jn})],Ps.prototype,"keyEncryptionAlgorithm",void 0),fr([sr({type:Ns})],Ps.prototype,"recipientEncryptedKeys",void 0);let Rs=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:an,context:0,implicit:!0})],Rs.prototype,"subjectKeyIdentifier",void 0),fr([sr({type:Cn})],Rs.prototype,"issuerAndSerialNumber",void 0),Rs=fr([nr({type:xt.Choice})],Rs);class Us{constructor(e={}){this.version=On.v0,this.rid=new Rs,this.keyEncryptionAlgorithm=new jn,this.encryptedKey=new Ct,Object.assign(this,e)}}fr([sr({type:Bt.Integer})],Us.prototype,"version",void 0),fr([sr({type:Rs})],Us.prototype,"rid",void 0),fr([sr({type:jn})],Us.prototype,"keyEncryptionAlgorithm",void 0),fr([sr({type:Ct})],Us.prototype,"encryptedKey",void 0);class Ds{constructor(e={}){this.keyIdentifier=new Ct,Object.assign(this,e)}}fr([sr({type:Ct})],Ds.prototype,"keyIdentifier",void 0),fr([sr({type:Bt.GeneralizedTime,optional:!0})],Ds.prototype,"date",void 0),fr([sr({type:ks,optional:!0})],Ds.prototype,"other",void 0);class Vs{constructor(e={}){this.version=On.v4,this.kekid=new Ds,this.keyEncryptionAlgorithm=new jn,this.encryptedKey=new Ct,Object.assign(this,e)}}fr([sr({type:Bt.Integer})],Vs.prototype,"version",void 0),fr([sr({type:Ds})],Vs.prototype,"kekid",void 0),fr([sr({type:jn})],Vs.prototype,"keyEncryptionAlgorithm",void 0),fr([sr({type:Ct})],Vs.prototype,"encryptedKey",void 0);class Ls{constructor(e={}){this.version=On.v0,this.keyEncryptionAlgorithm=new jn,this.encryptedKey=new Ct,Object.assign(this,e)}}fr([sr({type:Bt.Integer})],Ls.prototype,"version",void 0),fr([sr({type:Un,context:0,optional:!0})],Ls.prototype,"keyDerivationAlgorithm",void 0),fr([sr({type:jn})],Ls.prototype,"keyEncryptionAlgorithm",void 0),fr([sr({type:Ct})],Ls.prototype,"encryptedKey",void 0);class Hs{constructor(e={}){this.oriType="",this.oriValue=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],Hs.prototype,"oriType",void 0),fr([sr({type:Bt.Any})],Hs.prototype,"oriValue",void 0);let $s=class{constructor(e={}){Object.assign(this,e)}};var Ms;fr([sr({type:Us,optional:!0})],$s.prototype,"ktri",void 0),fr([sr({type:Ps,context:1,implicit:!0,optional:!0})],$s.prototype,"kari",void 0),fr([sr({type:Vs,context:2,implicit:!0,optional:!0})],$s.prototype,"kekri",void 0),fr([sr({type:Ls,context:3,implicit:!0,optional:!0})],$s.prototype,"pwri",void 0),fr([sr({type:Hs,context:4,implicit:!0,optional:!0})],$s.prototype,"ori",void 0),$s=fr([nr({type:xt.Choice})],$s);let Fs=Ms=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Ms.prototype)}};var Ks;Fs=Ms=fr([nr({type:xt.Set,itemType:$s})],Fs);class zs{constructor(e={}){this.otherRevInfoFormat="",this.otherRevInfo=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],zs.prototype,"otherRevInfoFormat",void 0),fr([sr({type:Bt.Any})],zs.prototype,"otherRevInfo",void 0);let _s=class{constructor(e={}){this.other=new zs,Object.assign(this,e)}};fr([sr({type:zs,context:1,implicit:!0})],_s.prototype,"other",void 0),_s=fr([nr({type:xt.Choice})],_s);let qs=Ks=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Ks.prototype)}};qs=Ks=fr([nr({type:xt.Set,itemType:_s})],qs);class Ws{constructor(e={}){Object.assign(this,e)}}var Gs;fr([sr({type:ws,context:0,implicit:!0,optional:!0})],Ws.prototype,"certs",void 0),fr([sr({type:qs,context:1,implicit:!0,optional:!0})],Ws.prototype,"crls",void 0);let Js=Gs=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Gs.prototype)}};Js=Gs=fr([nr({type:xt.Set,itemType:Dn})],Js);class Xs{constructor(e={}){this.version=On.v0,this.recipientInfos=new Fs,this.encryptedContentInfo=new Bs,Object.assign(this,e)}}fr([sr({type:Bt.Integer})],Xs.prototype,"version",void 0),fr([sr({type:Ws,context:0,implicit:!0,optional:!0})],Xs.prototype,"originatorInfo",void 0),fr([sr({type:Fs})],Xs.prototype,"recipientInfos",void 0),fr([sr({type:Bs})],Xs.prototype,"encryptedContentInfo",void 0),fr([sr({type:Js,context:1,implicit:!0,optional:!0})],Xs.prototype,"unprotectedAttrs",void 0);const Zs="1.2.840.113549.1.7.2";var Ys;let Qs=Ys=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Ys.prototype)}};Qs=Ys=fr([nr({type:xt.Set,itemType:Nn})],Qs);class eo{constructor(e={}){this.version=On.v0,this.digestAlgorithms=new Qs,this.encapContentInfo=new Ss,this.signerInfos=new Hn,Object.assign(this,e)}}fr([sr({type:Bt.Integer})],eo.prototype,"version",void 0),fr([sr({type:Qs})],eo.prototype,"digestAlgorithms",void 0),fr([sr({type:Ss})],eo.prototype,"encapContentInfo",void 0),fr([sr({type:ws,context:0,implicit:!0,optional:!0})],eo.prototype,"certificates",void 0),fr([sr({type:qs,context:1,implicit:!0,optional:!0})],eo.prototype,"crls",void 0),fr([sr({type:Hn})],eo.prototype,"signerInfos",void 0);const to="1.2.840.10045.2.1",ro="1.2.840.10045.4.1",io="1.2.840.10045.4.3.1",no="1.2.840.10045.4.3.2",so="1.2.840.10045.4.3.3",oo="1.2.840.10045.4.3.4",ao="1.2.840.10045.3.1.7",co="1.3.132.0.34",uo="1.3.132.0.35";function lo(e){return new yn({algorithm:e})}const po=lo(ro);lo(io);const ho=lo(no),fo=lo(so),yo=lo(oo);let go=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:Bt.ObjectIdentifier})],go.prototype,"fieldType",void 0),fr([sr({type:Bt.Any})],go.prototype,"parameters",void 0),go=fr([nr({type:xt.Sequence})],go);let vo=class{constructor(e={}){Object.assign(this,e)}};var mo;fr([sr({type:Bt.OctetString})],vo.prototype,"a",void 0),fr([sr({type:Bt.OctetString})],vo.prototype,"b",void 0),fr([sr({type:Bt.BitString,optional:!0})],vo.prototype,"seed",void 0),vo=fr([nr({type:xt.Sequence})],vo),function(e){e[e.ecpVer1=1]="ecpVer1"}(mo||(mo={}));let wo=class{constructor(e={}){this.version=mo.ecpVer1,Object.assign(this,e)}};fr([sr({type:Bt.Integer})],wo.prototype,"version",void 0),fr([sr({type:go})],wo.prototype,"fieldID",void 0),fr([sr({type:vo})],wo.prototype,"curve",void 0),fr([sr({type:class extends Ct{}})],wo.prototype,"base",void 0),fr([sr({type:Bt.Integer,converter:Tt})],wo.prototype,"order",void 0),fr([sr({type:Bt.Integer,optional:!0})],wo.prototype,"cofactor",void 0),wo=fr([nr({type:xt.Sequence})],wo);let bo=class{constructor(e={}){Object.assign(this,e)}};fr([sr({type:Bt.ObjectIdentifier})],bo.prototype,"namedCurve",void 0),fr([sr({type:Bt.Null})],bo.prototype,"implicitCurve",void 0),fr([sr({type:wo})],bo.prototype,"specifiedCurve",void 0),bo=fr([nr({type:xt.Choice})],bo);class Ao{constructor(e={}){this.version=1,this.privateKey=new Ct,Object.assign(this,e)}}fr([sr({type:Bt.Integer})],Ao.prototype,"version",void 0),fr([sr({type:Ct})],Ao.prototype,"privateKey",void 0),fr([sr({type:bo,context:0,optional:!0})],Ao.prototype,"parameters",void 0),fr([sr({type:Bt.BitString,context:1,optional:!0})],Ao.prototype,"publicKey",void 0);class So{constructor(e={}){this.r=new ArrayBuffer(0),this.s=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.Integer,converter:Tt})],So.prototype,"r",void 0),fr([sr({type:Bt.Integer,converter:Tt})],So.prototype,"s",void 0);const xo="1.2.840.113549.1.1",Bo=`${xo}.1`,ko=`${xo}.7`,Eo=`${xo}.9`,Co=`${xo}.10`,Io=`${xo}.2`,Oo=`${xo}.4`,No=`${xo}.5`,To=`${xo}.14`,jo=`${xo}.11`,Po=`${xo}.12`,Ro=`${xo}.13`,Uo=`${xo}.15`,Do=`${xo}.16`,Vo="1.3.14.3.2.26",Lo="2.16.840.1.101.3.4.2.4",Ho="2.16.840.1.101.3.4.2.1",$o="2.16.840.1.101.3.4.2.2",Mo="2.16.840.1.101.3.4.2.3",Fo=`${xo}.8`;function Ko(e){return new yn({algorithm:e,parameters:null})}Ko("1.2.840.113549.2.2"),Ko("1.2.840.113549.2.5");const zo=Ko(Vo);Ko(Lo),Ko(Ho),Ko($o),Ko(Mo),Ko("2.16.840.1.101.3.4.2.5"),Ko("2.16.840.1.101.3.4.2.6");const _o=new yn({algorithm:Fo,parameters:lr.serialize(zo)}),qo=new yn({algorithm:Eo,parameters:lr.serialize(Ut.toASN(new Uint8Array([218,57,163,238,94,107,75,13,50,85,191,239,149,96,24,144,175,216,7,9]).buffer))});Ko(Bo),Ko(Io),Ko(Oo),Ko(No),Ko(Uo),Ko(Do),Ko(Po),Ko(Ro),Ko(Uo),Ko(Do);class Wo{constructor(e={}){this.hashAlgorithm=new yn(zo),this.maskGenAlgorithm=new yn({algorithm:Fo,parameters:lr.serialize(zo)}),this.pSourceAlgorithm=new yn(qo),Object.assign(this,e)}}fr([sr({type:yn,context:0,defaultValue:zo})],Wo.prototype,"hashAlgorithm",void 0),fr([sr({type:yn,context:1,defaultValue:_o})],Wo.prototype,"maskGenAlgorithm",void 0),fr([sr({type:yn,context:2,defaultValue:qo})],Wo.prototype,"pSourceAlgorithm",void 0),new yn({algorithm:ko,parameters:lr.serialize(new Wo)});class Go{constructor(e={}){this.hashAlgorithm=new yn(zo),this.maskGenAlgorithm=new yn({algorithm:Fo,parameters:lr.serialize(zo)}),this.saltLength=20,this.trailerField=1,Object.assign(this,e)}}fr([sr({type:yn,context:0,defaultValue:zo})],Go.prototype,"hashAlgorithm",void 0),fr([sr({type:yn,context:1,defaultValue:_o})],Go.prototype,"maskGenAlgorithm",void 0),fr([sr({type:Bt.Integer,context:2,defaultValue:20})],Go.prototype,"saltLength",void 0),fr([sr({type:Bt.Integer,context:3,defaultValue:1})],Go.prototype,"trailerField",void 0),new yn({algorithm:Co,parameters:lr.serialize(new Go)});class Jo{constructor(e={}){this.digestAlgorithm=new yn,this.digest=new Ct,Object.assign(this,e)}}var Xo;fr([sr({type:yn})],Jo.prototype,"digestAlgorithm",void 0),fr([sr({type:Ct})],Jo.prototype,"digest",void 0);class Zo{constructor(e={}){this.prime=new ArrayBuffer(0),this.exponent=new ArrayBuffer(0),this.coefficient=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.Integer,converter:Tt})],Zo.prototype,"prime",void 0),fr([sr({type:Bt.Integer,converter:Tt})],Zo.prototype,"exponent",void 0),fr([sr({type:Bt.Integer,converter:Tt})],Zo.prototype,"coefficient",void 0);let Yo=Xo=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Xo.prototype)}};Yo=Xo=fr([nr({type:xt.Sequence,itemType:Zo})],Yo);class Qo{constructor(e={}){this.version=0,this.modulus=new ArrayBuffer(0),this.publicExponent=new ArrayBuffer(0),this.privateExponent=new ArrayBuffer(0),this.prime1=new ArrayBuffer(0),this.prime2=new ArrayBuffer(0),this.exponent1=new ArrayBuffer(0),this.exponent2=new ArrayBuffer(0),this.coefficient=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.Integer})],Qo.prototype,"version",void 0),fr([sr({type:Bt.Integer,converter:Tt})],Qo.prototype,"modulus",void 0),fr([sr({type:Bt.Integer,converter:Tt})],Qo.prototype,"publicExponent",void 0),fr([sr({type:Bt.Integer,converter:Tt})],Qo.prototype,"privateExponent",void 0),fr([sr({type:Bt.Integer,converter:Tt})],Qo.prototype,"prime1",void 0),fr([sr({type:Bt.Integer,converter:Tt})],Qo.prototype,"prime2",void 0),fr([sr({type:Bt.Integer,converter:Tt})],Qo.prototype,"exponent1",void 0),fr([sr({type:Bt.Integer,converter:Tt})],Qo.prototype,"exponent2",void 0),fr([sr({type:Bt.Integer,converter:Tt})],Qo.prototype,"coefficient",void 0),fr([sr({type:Yo,optional:!0})],Qo.prototype,"otherPrimeInfos",void 0);class ea{constructor(e={}){this.modulus=new ArrayBuffer(0),this.publicExponent=new ArrayBuffer(0),Object.assign(this,e)}}var ta;fr([sr({type:Bt.Integer,converter:Tt})],ea.prototype,"modulus",void 0),fr([sr({type:Bt.Integer,converter:Tt})],ea.prototype,"publicExponent",void 0),function(e){e[e.Transient=0]="Transient",e[e.Singleton=1]="Singleton",e[e.ResolutionScoped=2]="ResolutionScoped",e[e.ContainerScoped=3]="ContainerScoped"}(ta||(ta={}));var ra=ta;function ia(e){return!!e.useClass}function na(e){return!!e.useFactory}var sa=function(){function e(e){this.wrap=e,this.reflectMethods=["get","getPrototypeOf","setPrototypeOf","getOwnPropertyDescriptor","defineProperty","has","set","deleteProperty","apply","construct","ownKeys"]}return e.prototype.createProxy=function(e){var t,r=this,i=!1;return new Proxy({},this.createHandler((function(){return i||(t=e(r.wrap()),i=!0),t})))},e.prototype.createHandler=function(e){var t={};return this.reflectMethods.forEach((function(r){t[r]=function(){for(var t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];return t[0]=e(),Reflect[r].apply(void 0,mr(t))}})),t},e}();function oa(e){return"string"==typeof e||"symbol"==typeof e}function aa(e){return"object"==typeof e&&"token"in e&&"transform"in e}function ca(e){return!!e.useToken}function ua(e){return null!=e.useValue}var la=function(){function e(){this._registryMap=new Map}return e.prototype.entries=function(){return this._registryMap.entries()},e.prototype.getAll=function(e){return this.ensure(e),this._registryMap.get(e)},e.prototype.get=function(e){this.ensure(e);var t=this._registryMap.get(e);return t[t.length-1]||null},e.prototype.set=function(e,t){this.ensure(e),this._registryMap.get(e).push(t)},e.prototype.setAll=function(e,t){this._registryMap.set(e,t)},e.prototype.has=function(e){return this.ensure(e),this._registryMap.get(e).length>0},e.prototype.clear=function(){this._registryMap.clear()},e.prototype.ensure=function(e){this._registryMap.has(e)||this._registryMap.set(e,[])},e}(),pa=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return hr(t,e),t}(la),ha=function(){this.scopedResolutions=new Map};function fa(e,t,r){var i,n,s=vr(e.toString().match(/constructor\(([\w, ]+)\)/)||[],2)[1],o=function(e,t){return null===e?"at position #"+t:'"'+e.split(",")[t].trim()+'" at position #'+t}(void 0===s?null:s,t);return i="Cannot inject the dependency "+o+' of "'+e.name+'" constructor. Reason:',void 0===n&&(n="    "),mr([i],r.message.split("\n").map((function(e){return n+e}))).join("\n")}var ya=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return hr(t,e),t}(la),da=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return hr(t,e),t}(la),ga=function(){this.preResolution=new ya,this.postResolution=new da},va=new Map,ma=function(){function e(e){this.parent=e,this._registry=new pa,this.interceptors=new ga,this.disposed=!1,this.disposables=new Set}return e.prototype.register=function(e,t,r){var i;if(void 0===r&&(r={lifecycle:ra.Transient}),this.ensureNotDisposed(),i=function(e){return ia(e)||ua(e)||ca(e)||na(e)}(t)?t:{useClass:t},ca(i))for(var n=[e],s=i;null!=s;){var o=s.useToken;if(n.includes(o))throw new Error("Token registration cycle detected! "+mr(n,[o]).join(" -> "));n.push(o);var a=this._registry.get(o);s=a&&ca(a.provider)?a.provider:null}if((r.lifecycle===ra.Singleton||r.lifecycle==ra.ContainerScoped||r.lifecycle==ra.ResolutionScoped)&&(ua(i)||na(i)))throw new Error('Cannot use lifecycle "'+ra[r.lifecycle]+'" with ValueProviders or FactoryProviders');return this._registry.set(e,{provider:i,options:r}),this},e.prototype.registerType=function(e,t){return this.ensureNotDisposed(),oa(t)?this.register(e,{useToken:t}):this.register(e,{useClass:t})},e.prototype.registerInstance=function(e,t){return this.ensureNotDisposed(),this.register(e,{useValue:t})},e.prototype.registerSingleton=function(e,t){if(this.ensureNotDisposed(),oa(e)){if(oa(t))return this.register(e,{useToken:t},{lifecycle:ra.Singleton});if(t)return this.register(e,{useClass:t},{lifecycle:ra.Singleton});throw new Error('Cannot register a type name as a singleton without a "to" token')}var r=e;return t&&!oa(t)&&(r=t),this.register(e,{useClass:r},{lifecycle:ra.Singleton})},e.prototype.resolve=function(e,t){void 0===t&&(t=new ha),this.ensureNotDisposed();var r=this.getRegistration(e);if(!r&&oa(e))throw new Error('Attempted to resolve unregistered dependency token: "'+e.toString()+'"');if(this.executePreResolutionInterceptor(e,"Single"),r){var i=this.resolveRegistration(r,t);return this.executePostResolutionInterceptor(e,i,"Single"),i}if(function(e){return"function"==typeof e||e instanceof sa}(e)){i=this.construct(e,t);return this.executePostResolutionInterceptor(e,i,"Single"),i}throw new Error("Attempted to construct an undefined constructor. Could mean a circular dependency problem. Try using `delay` function.")},e.prototype.executePreResolutionInterceptor=function(e,t){var r,i;if(this.interceptors.preResolution.has(e)){var n=[];try{for(var s=gr(this.interceptors.preResolution.getAll(e)),o=s.next();!o.done;o=s.next()){var a=o.value;"Once"!=a.options.frequency&&n.push(a),a.callback(e,t)}}catch(e){r={error:e}}finally{try{o&&!o.done&&(i=s.return)&&i.call(s)}finally{if(r)throw r.error}}this.interceptors.preResolution.setAll(e,n)}},e.prototype.executePostResolutionInterceptor=function(e,t,r){var i,n;if(this.interceptors.postResolution.has(e)){var s=[];try{for(var o=gr(this.interceptors.postResolution.getAll(e)),a=o.next();!a.done;a=o.next()){var c=a.value;"Once"!=c.options.frequency&&s.push(c),c.callback(e,t,r)}}catch(e){i={error:e}}finally{try{a&&!a.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}this.interceptors.postResolution.setAll(e,s)}},e.prototype.resolveRegistration=function(e,t){if(this.ensureNotDisposed(),e.options.lifecycle===ra.ResolutionScoped&&t.scopedResolutions.has(e))return t.scopedResolutions.get(e);var r,i=e.options.lifecycle===ra.Singleton,n=e.options.lifecycle===ra.ContainerScoped,s=i||n;return r=ua(e.provider)?e.provider.useValue:ca(e.provider)?s?e.instance||(e.instance=this.resolve(e.provider.useToken,t)):this.resolve(e.provider.useToken,t):ia(e.provider)?s?e.instance||(e.instance=this.construct(e.provider.useClass,t)):this.construct(e.provider.useClass,t):na(e.provider)?e.provider.useFactory(this):this.construct(e.provider,t),e.options.lifecycle===ra.ResolutionScoped&&t.scopedResolutions.set(e,r),r},e.prototype.resolveAll=function(e,t){var r=this;void 0===t&&(t=new ha),this.ensureNotDisposed();var i=this.getAllRegistrations(e);if(!i&&oa(e))throw new Error('Attempted to resolve unregistered dependency token: "'+e.toString()+'"');if(this.executePreResolutionInterceptor(e,"All"),i){var n=i.map((function(e){return r.resolveRegistration(e,t)}));return this.executePostResolutionInterceptor(e,n,"All"),n}var s=[this.construct(e,t)];return this.executePostResolutionInterceptor(e,s,"All"),s},e.prototype.isRegistered=function(e,t){return void 0===t&&(t=!1),this.ensureNotDisposed(),this._registry.has(e)||t&&(this.parent||!1)&&this.parent.isRegistered(e,!0)},e.prototype.reset=function(){this.ensureNotDisposed(),this._registry.clear(),this.interceptors.preResolution.clear(),this.interceptors.postResolution.clear()},e.prototype.clearInstances=function(){var e,t;this.ensureNotDisposed();try{for(var r=gr(this._registry.entries()),i=r.next();!i.done;i=r.next()){var n=vr(i.value,2),s=n[0],o=n[1];this._registry.setAll(s,o.filter((function(e){return!ua(e.provider)})).map((function(e){return e.instance=void 0,e})))}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}},e.prototype.createChildContainer=function(){var t,r;this.ensureNotDisposed();var i=new e(this);try{for(var n=gr(this._registry.entries()),s=n.next();!s.done;s=n.next()){var o=vr(s.value,2),a=o[0],c=o[1];c.some((function(e){return e.options.lifecycle===ra.ContainerScoped}))&&i._registry.setAll(a,c.map((function(e){return e.options.lifecycle===ra.ContainerScoped?{provider:e.provider,options:e.options}:e})))}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return i},e.prototype.beforeResolution=function(e,t,r){void 0===r&&(r={frequency:"Always"}),this.interceptors.preResolution.set(e,{callback:t,options:r})},e.prototype.afterResolution=function(e,t,r){void 0===r&&(r={frequency:"Always"}),this.interceptors.postResolution.set(e,{callback:t,options:r})},e.prototype.dispose=function(){return yr(this,void 0,void 0,(function(){var e;return dr(this,(function(t){switch(t.label){case 0:return this.disposed=!0,e=[],this.disposables.forEach((function(t){var r=t.dispose();r&&e.push(r)})),[4,Promise.all(e)];case 1:return t.sent(),[2]}}))}))},e.prototype.getRegistration=function(e){return this.isRegistered(e)?this._registry.get(e):this.parent?this.parent.getRegistration(e):null},e.prototype.getAllRegistrations=function(e){return this.isRegistered(e)?this._registry.getAll(e):this.parent?this.parent.getAllRegistrations(e):null},e.prototype.construct=function(e,t){var r=this;if(e instanceof sa)return e.createProxy((function(e){return r.resolve(e,t)}));var i,n=function(){var i=va.get(e);if(!i||0===i.length){if(0===e.length)return new e;throw new Error('TypeInfo not known for "'+e.name+'"')}var n=i.map(r.resolveParams(t,e));return new(e.bind.apply(e,mr([void 0],n)))}();return"function"!=typeof(i=n).dispose||i.dispose.length>0||this.disposables.add(n),n},e.prototype.resolveParams=function(e,t){var r=this;return function(i,n){var s,o,a,c;try{return"object"==typeof(c=i)&&"token"in c&&"multiple"in c?aa(i)?i.multiple?(s=r.resolve(i.transform)).transform.apply(s,mr([r.resolveAll(i.token)],i.transformArgs)):(o=r.resolve(i.transform)).transform.apply(o,mr([r.resolve(i.token,e)],i.transformArgs)):i.multiple?r.resolveAll(i.token):r.resolve(i.token,e):aa(i)?(a=r.resolve(i.transform,e)).transform.apply(a,mr([r.resolve(i.token,e)],i.transformArgs)):r.resolve(i,e)}catch(e){throw new Error(fa(t,n,e))}}},e.prototype.ensureNotDisposed=function(){if(this.disposed)throw new Error("This container has been disposed, you cannot interact with a disposed container")},e}(),wa=new ma;function ba(){return function(e){va.set(e,function(e){var t=Reflect.getMetadata("design:paramtypes",e)||[],r=Reflect.getOwnMetadata("injectionTokens",e)||{};return Object.keys(r).forEach((function(e){t[+e]=r[e]})),t}(e))}}if("undefined"==typeof Reflect||!Reflect.getMetadata)throw new Error("tsyringe requires a reflect polyfill. Please add 'import \"reflect-metadata\"' to the top of your entry point.");const Aa="crypto.algorithm";class Sa{getAlgorithms(){return wa.resolveAll(Aa)}toAsnAlgorithm(e){for(const t of this.getAlgorithms()){const r=t.toAsnAlgorithm(e);if(r)return r}if(/^[0-9.]+$/.test(e.name)){const t=new yn({algorithm:e.name});if("parameters"in e){const r=e;t.parameters=r.parameters}return t}throw new Error("Cannot convert WebCrypto algorithm to ASN.1 algorithm")}toWebAlgorithm(e){for(const t of this.getAlgorithms()){const r=t.toWebAlgorithm(e);if(r)return r}return{name:e.algorithm,parameters:e.parameters}}}const xa="crypto.algorithmProvider";var Ba;wa.registerSingleton(xa,Sa);const ka="1.3.36.3.3.2.8.1.1",Ea=`${ka}.1`,Ca=`${ka}.2`,Ia=`${ka}.3`,Oa=`${ka}.4`,Na=`${ka}.5`,Ta=`${ka}.6`,ja=`${ka}.7`,Pa=`${ka}.8`,Ra=`${ka}.9`,Ua=`${ka}.10`,Da=`${ka}.11`,Va=`${ka}.12`,La=`${ka}.13`,Ha=`${ka}.14`,$a="brainpoolP160r1",Ma="brainpoolP160t1",Fa="brainpoolP192r1",Ka="brainpoolP192t1",za="brainpoolP224r1",_a="brainpoolP224t1",qa="brainpoolP256r1",Wa="brainpoolP256t1",Ga="brainpoolP320r1",Ja="brainpoolP320t1",Xa="brainpoolP384r1",Za="brainpoolP384t1",Ya="brainpoolP512r1",Qa="brainpoolP512t1",ec="ECDSA";e.EcAlgorithm=Ba=class{toAsnAlgorithm(e){if(e.name.toLowerCase()===ec.toLowerCase())if("hash"in e){switch(("string"==typeof e.hash?e.hash:e.hash.name).toLowerCase()){case"sha-1":return po;case"sha-256":return ho;case"sha-384":return fo;case"sha-512":return yo}}else if("namedCurve"in e){let t="";switch(e.namedCurve){case"P-256":t=ao;break;case"K-256":t=Ba.SECP256K1;break;case"P-384":t=co;break;case"P-521":t=uo;break;case $a:t=Ea;break;case Ma:t=Ca;break;case Fa:t=Ia;break;case Ka:t=Oa;break;case za:t=Na;break;case _a:t=Ta;break;case qa:t=ja;break;case Wa:t=Pa;break;case Ga:t=Ra;break;case Ja:t=Ua;break;case Xa:t=Da;break;case Za:t=Va;break;case Ya:t=La;break;case Qa:t=Ha}if(t)return new yn({algorithm:to,parameters:lr.serialize(new bo({namedCurve:t}))})}return null}toWebAlgorithm(e){switch(e.algorithm){case ro:return{name:ec,hash:{name:"SHA-1"}};case no:return{name:ec,hash:{name:"SHA-256"}};case so:return{name:ec,hash:{name:"SHA-384"}};case oo:return{name:ec,hash:{name:"SHA-512"}};case to:if(!e.parameters)throw new TypeError("Cannot get required parameters from EC algorithm");switch(lr.parse(e.parameters,bo).namedCurve){case ao:return{name:ec,namedCurve:"P-256"};case Ba.SECP256K1:return{name:ec,namedCurve:"K-256"};case co:return{name:ec,namedCurve:"P-384"};case uo:return{name:ec,namedCurve:"P-521"};case Ea:return{name:ec,namedCurve:$a};case Ca:return{name:ec,namedCurve:Ma};case Ia:return{name:ec,namedCurve:Fa};case Oa:return{name:ec,namedCurve:Ka};case Na:return{name:ec,namedCurve:za};case Ta:return{name:ec,namedCurve:_a};case ja:return{name:ec,namedCurve:qa};case Pa:return{name:ec,namedCurve:Wa};case Ra:return{name:ec,namedCurve:Ga};case Ua:return{name:ec,namedCurve:Ja};case Da:return{name:ec,namedCurve:Xa};case Va:return{name:ec,namedCurve:Za};case La:return{name:ec,namedCurve:Ya};case Ha:return{name:ec,namedCurve:Qa}}}return null}},e.EcAlgorithm.SECP256K1="1.3.132.0.10",e.EcAlgorithm=Ba=fr([ba()],e.EcAlgorithm),wa.registerSingleton(Aa,e.EcAlgorithm);const tc=Symbol("name"),rc=Symbol("value");class ic{constructor(e,t={},r=""){this[tc]=e,this[rc]=r;for(const e in t)this[e]=t[e]}}ic.NAME=tc,ic.VALUE=rc;class nc{static toTextObject(t){const r=new ic("Algorithm Identifier",{},sc.toString(t.algorithm));if(t.parameters)switch(t.algorithm){case to:{const i=(new e.EcAlgorithm).toWebAlgorithm(t);i&&"namedCurve"in i?r["Named Curve"]=i.namedCurve:r.Parameters=t.parameters;break}default:r.Parameters=t.parameters}return r}}class sc{static toString(e){const t=this.items[e];return t||e}}sc.items={[Vo]:"sha1",[Lo]:"sha224",[Ho]:"sha256",[$o]:"sha384",[Mo]:"sha512",[Bo]:"rsaEncryption",[No]:"sha1WithRSAEncryption",[To]:"sha224WithRSAEncryption",[jo]:"sha256WithRSAEncryption",[Po]:"sha384WithRSAEncryption",[Ro]:"sha512WithRSAEncryption",[to]:"ecPublicKey",[ro]:"ecdsaWithSHA1",[io]:"ecdsaWithSHA224",[no]:"ecdsaWithSHA256",[so]:"ecdsaWithSHA384",[oo]:"ecdsaWithSHA512",[Ti]:"TLS WWW server authentication",[ji]:"TLS WWW client authentication",[Pi]:"Code Signing",[Ri]:"E-mail Protection",[Ui]:"Time Stamping",[Di]:"OCSP Signing",[Zs]:"Signed Data"};class oc{static serialize(e){return this.serializeObj(e).join("\n")}static pad(e=0){return"".padStart(2*e," ")}static serializeObj(e,t=0){const r=[];let n=this.pad(t++),s="";const o=e[ic.VALUE];o&&(s=` ${o}`),r.push(`${n}${e[ic.NAME]}:${s}`),n=this.pad(t);for(const s in e){if("symbol"==typeof s)continue;const o=e[s],a=s?`${s}: `:"";if("string"==typeof o||"number"==typeof o||"boolean"==typeof o)r.push(`${n}${a}${o}`);else if(o instanceof Date)r.push(`${n}${a}${o.toUTCString()}`);else if(Array.isArray(o))for(const e of o)e[ic.NAME]=s,r.push(...this.serializeObj(e,t));else if(o instanceof ic)o[ic.NAME]=s,r.push(...this.serializeObj(o,t));else if(i.isBufferSource(o))s?(r.push(`${n}${a}`),r.push(...this.serializeBufferSource(o,t+1))):r.push(...this.serializeBufferSource(o,t));else{if(!("toTextObject"in o))throw new TypeError("Cannot serialize data in text format. Unsupported type.");{const e=o.toTextObject();e[ic.NAME]=s,r.push(...this.serializeObj(e,t))}}}return r}static serializeBufferSource(e,t=0){const r=this.pad(t),n=i.toUint8Array(e),s=[];for(let e=0;e<n.length;){const t=[];for(let r=0;r<16&&e<n.length;r++){8===r&&t.push("");const i=n[e++].toString(16).padStart(2,"0");t.push(i)}s.push(`${r}${t.join(" ")}`)}return s}static serializeAlgorithm(e){return this.algorithmSerializer.toTextObject(e)}}oc.oidSerializer=sc,oc.algorithmSerializer=nc;class ac{constructor(...e){if(1===e.length){const t=e[0];this.rawData=lr.serialize(t),this.onInit(t)}else{const t=lr.parse(e[0],e[1]);this.rawData=i.toArrayBuffer(e[0]),this.onInit(t)}}equal(e){return e instanceof ac&&p(e.rawData,this.rawData)}toString(e="text"){switch(e){case"asn":return lr.toString(this.rawData);case"text":return oc.serialize(this.toTextObject());case"hex":return l.ToHex(this.rawData);case"base64":return l.ToBase64(this.rawData);case"base64url":return l.ToBase64Url(this.rawData);default:throw TypeError("Argument 'format' is unsupported value")}}getTextName(){return this.constructor.NAME}toTextObject(){const e=this.toTextObjectEmpty();return e[""]=this.rawData,e}toTextObjectEmpty(e){return new ic(this.getTextName(),{},e)}}ac.NAME="ASN";class cc extends ac{constructor(...e){let t;t=i.isBufferSource(e[0])?i.toArrayBuffer(e[0]):lr.serialize(new wn({extnID:e[0],critical:e[1],extnValue:new Ct(i.toArrayBuffer(e[2]))})),super(t,wn)}onInit(e){this.type=e.extnID,this.critical=e.critical,this.value=e.extnValue.buffer}toTextObject(){const e=this.toTextObjectWithoutValue();return e[""]=this.value,e}toTextObjectWithoutValue(){const e=this.toTextObjectEmpty(this.critical?"critical":void 0);return e[ic.NAME]===cc.NAME&&(e[ic.NAME]=sc.toString(this.type)),e}}var uc;class lc{static isCryptoKeyPair(e){return e&&e.privateKey&&e.publicKey}static isCryptoKey(e){return e&&e.usages&&e.type&&e.algorithm&&void 0!==e.extractable}constructor(){this.items=new Map,this[uc]="CryptoProvider","undefined"!=typeof self&&"undefined"!=typeof crypto?this.set(lc.DEFAULT,crypto):"undefined"!=typeof global&&global.crypto&&global.crypto.subtle&&this.set(lc.DEFAULT,global.crypto)}clear(){this.items.clear()}delete(e){return this.items.delete(e)}forEach(e,t){return this.items.forEach(e,t)}has(e){return this.items.has(e)}get size(){return this.items.size}entries(){return this.items.entries()}keys(){return this.items.keys()}values(){return this.items.values()}[Symbol.iterator](){return this.items[Symbol.iterator]()}get(e=lc.DEFAULT){const t=this.items.get(e.toLowerCase());if(!t)throw new Error(`Cannot get Crypto by name '${e}'`);return t}set(e,t){if("string"==typeof e){if(!t)throw new TypeError("Argument 'value' is required");this.items.set(e.toLowerCase(),t)}else this.items.set(lc.DEFAULT,e);return this}}uc=Symbol.toStringTag,lc.DEFAULT="default";const pc=new lc,hc=/^[0-2](?:\.[1-9][0-9]*)+$/;class fc{constructor(e={}){this.items={};for(const t in e)this.register(t,e[t])}get(e){return this.items[e]||null}findId(e){return t=e,new RegExp(hc).test(t)?e:this.get(e);var t}register(e,t){this.items[e]=t,this.items[t]=e}}const yc=new fc;function dc(e,t){return`\\${l.ToHex(l.FromUtf8String(t)).toUpperCase()}`}yc.register("CN","2.5.4.3"),yc.register("L","2.5.4.7"),yc.register("ST","2.5.4.8"),yc.register("O","2.5.4.10"),yc.register("OU","2.5.4.11"),yc.register("C","2.5.4.6"),yc.register("DC","0.9.2342.19200300.100.1.25"),yc.register("E","1.2.840.113549.1.9.1"),yc.register("G","2.5.4.42"),yc.register("I","2.5.4.43"),yc.register("SN","2.5.4.4"),yc.register("T","2.5.4.12");class gc{static isASCII(e){for(let t=0;t<e.length;t++){if(e.charCodeAt(t)>255)return!1}return!0}static isPrintableString(e){return/^[A-Za-z0-9 '()+,-./:=?]*$/g.test(e)}constructor(e,t={}){this.extraNames=new fc,this.asn=new Tr;for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const r=t[e];this.extraNames.register(e,r)}"string"==typeof e?this.asn=this.fromString(e):e instanceof Tr?this.asn=e:i.isBufferSource(e)?this.asn=lr.parse(e,Tr):this.asn=this.fromJSON(e)}getField(e){const t=this.extraNames.findId(e)||yc.findId(e),r=[];for(const e of this.asn)for(const i of e)i.type===t&&r.push(i.value.toString());return r}getName(e){return this.extraNames.get(e)||yc.get(e)}toString(){return this.asn.map((e=>e.map((e=>`${this.getName(e.type)||e.type}=${e.value.anyValue?`#${l.ToHex(e.value.anyValue)}`:e.value.toString().replace(/([,+"\\<>;])/g,"\\$1").replace(/^([ #])/,"\\$1").replace(/([ ]$)/,"\\$1").replace(/([\r\n\t])/,dc)}`)).join("+"))).join(", ")}toJSON(){var e;const t=[];for(const r of this.asn){const i={};for(const t of r){const r=this.getName(t.type)||t.type;null!==(e=i[r])&&void 0!==e||(i[r]=[]),i[r].push(t.value.anyValue?`#${l.ToHex(t.value.anyValue)}`:t.value.toString())}t.push(i)}return t}fromString(e){const t=new Tr,r=/(\d\.[\d.]*\d|[A-Za-z]+)=((?:"")|(?:".*?[^\\]")|(?:[^,+].*?(?:[^\\][,+]))|(?:))([,+])?/g;let i=null,n=",";for(;i=r.exec(`${e},`);){let[,e,r]=i;const s=r[r.length-1];","!==s&&"+"!==s||(r=r.slice(0,r.length-1),i[3]=s);const o=i[3];e=this.getTypeOid(e);const a=this.createAttribute(e,r);"+"===n?t[t.length-1].push(a):t.push(new Or([a])),n=o}return t}fromJSON(e){const t=new Tr;for(const r of e){const e=new Or;for(const t in r){const i=this.getTypeOid(t),n=r[t];for(const t of n){const r=this.createAttribute(i,t);e.push(r)}}t.push(e)}return t}getTypeOid(e){if(/[\d.]+/.test(e)||(e=this.getName(e)||""),!e)throw new Error(`Cannot get OID for name type '${e}'`);return e}createAttribute(e,t){const r=new Ir({type:e});if("object"==typeof t)for(const e in t)switch(e){case"ia5String":r.value.ia5String=t[e];break;case"utf8String":r.value.utf8String=t[e];break;case"universalString":r.value.universalString=t[e];break;case"bmpString":r.value.bmpString=t[e];break;case"printableString":r.value.printableString=t[e]}else if("#"===t[0])r.value.anyValue=l.FromHex(t.slice(1));else{const i=this.processStringValue(t);e===this.getName("E")||e===this.getName("DC")?r.value.ia5String=i:gc.isPrintableString(i)?r.value.printableString=i:r.value.utf8String=i}return r}processStringValue(e){const t=/"(.*?[^\\])?"/.exec(e);return t&&(e=t[1]),e.replace(/\\0a/gi,"\n").replace(/\\0d/gi,"\r").replace(/\\0g/gi,"\t").replace(/\\(.)/g,"$1")}toArrayBuffer(){return lr.serialize(this.asn)}async getThumbprint(...e){var t;let r,i="SHA-1";return e.length>=1&&!(null===(t=e[0])||void 0===t?void 0:t.subtle)?(i=e[0]||i,r=e[1]||pc.get()):r=e[0]||pc.get(),await r.subtle.digest(i,this.toArrayBuffer())}}const vc="Cannot initialize GeneralName from ASN.1 data.",mc=`${vc} Unsupported string format in use.`,wc=`${vc} Value doesn't match to GUID regular expression.`,bc=/^([0-9a-f]{8})-?([0-9a-f]{4})-?([0-9a-f]{4})-?([0-9a-f]{4})-?([0-9a-f]{12})$/i,Ac="*******.4.1.311.25.1",Sc="*******.4.1.311.20.2.3",xc="dns",Bc="dn",kc="email",Ec="ip",Cc="url",Ic="guid",Oc="upn",Nc="id";class Tc extends ac{constructor(...e){let t;if(2===e.length)switch(e[0]){case Bc:{const r=new gc(e[1]).toArrayBuffer(),i=lr.parse(r,Tr);t=new Ur({directoryName:i});break}case xc:t=new Ur({dNSName:e[1]});break;case kc:t=new Ur({rfc822Name:e[1]});break;case Ic:{const r=new RegExp(bc,"i").exec(e[1]);if(!r)throw new Error("Cannot parse GUID value. Value doesn't match to regular expression");const i=r.slice(1).map(((e,t)=>t<3?l.ToHex(new Uint8Array(l.FromHex(e)).reverse()):e)).join("");t=new Ur({otherName:new Pr({typeId:Ac,value:lr.serialize(new Ct(l.FromHex(i)))})});break}case Ec:t=new Ur({iPAddress:e[1]});break;case Nc:t=new Ur({registeredID:e[1]});break;case Oc:t=new Ur({otherName:new Pr({typeId:Sc,value:lr.serialize(Lt.toASN(e[1]))})});break;case Cc:t=new Ur({uniformResourceIdentifier:e[1]});break;default:throw new Error("Cannot create GeneralName. Unsupported type of the name")}else t=i.isBufferSource(e[0])?lr.parse(e[0],Ur):e[0];super(t)}onInit(e){if(null!=e.dNSName)this.type=xc,this.value=e.dNSName;else if(null!=e.rfc822Name)this.type=kc,this.value=e.rfc822Name;else if(null!=e.iPAddress)this.type=Ec,this.value=e.iPAddress;else if(null!=e.uniformResourceIdentifier)this.type=Cc,this.value=e.uniformResourceIdentifier;else if(null!=e.registeredID)this.type=Nc,this.value=e.registeredID;else if(null!=e.directoryName)this.type=Bc,this.value=new gc(e.directoryName).toString();else{if(null==e.otherName)throw new Error(mc);if(e.otherName.typeId===Ac){this.type=Ic;const t=lr.parse(e.otherName.value,Ct),r=new RegExp(bc,"i").exec(l.ToHex(t));if(!r)throw new Error(wc);this.value=r.slice(1).map(((e,t)=>t<3?l.ToHex(new Uint8Array(l.FromHex(e)).reverse()):e)).join("-")}else{if(e.otherName.typeId!==Sc)throw new Error(mc);this.type=Oc,this.value=lr.parse(e.otherName.value,Er).toString()}}}toJSON(){return{type:this.type,value:this.value}}toTextObject(){let e;switch(this.type){case Bc:case xc:case Ic:case Ec:case Nc:case Oc:case Cc:e=this.type.toUpperCase();break;case kc:e="Email";break;default:throw new Error("Unsupported GeneralName type")}let t=this.value;return this.type===Nc&&(t=sc.toString(t)),new ic(e,void 0,t)}}class jc extends ac{constructor(e){let t;if(e instanceof ei)t=e;else if(Array.isArray(e)){const r=[];for(const t of e)if(t instanceof Ur)r.push(t);else{const e=lr.parse(new Tc(t.type,t.value).rawData,Ur);r.push(e)}t=new ei(r)}else{if(!i.isBufferSource(e))throw new Error("Cannot initialize GeneralNames. Incorrect incoming arguments");t=lr.parse(e,ei)}super(t)}onInit(e){const t=[];for(const r of e){let e=null;try{e=new Tc(r)}catch(e){continue}t.push(e)}this.items=t}toJSON(){return this.items.map((e=>e.toJSON()))}toTextObject(){const e=super.toTextObjectEmpty();for(const t of this.items){const r=t.toTextObject();let i=e[r[ic.NAME]];Array.isArray(i)||(i=[],e[r[ic.NAME]]=i),i.push(r)}return e}}jc.NAME="GeneralNames";const Pc="-{5}",Rc="\\n",Uc="\\n",Dc=`${`${Pc}BEGIN (${`[^${Rc}]+`}(?=${Pc}))${Pc}`}${Uc}(?:((?:${`[^:${Rc}]+`}: ${`(?:[^${Rc}]+${Uc}(?: +[^${Rc}]+${Uc})*)`})+))?${Uc}?(${`(?:[a-zA-Z0-9=+/]+${Uc})+`})${`${Pc}END \\1${Pc}`}`;class Vc{static isPem(e){return"string"==typeof e&&new RegExp(Dc,"g").test(e)}static decodeWithHeaders(e){e=e.replace(/\r/g,"");const t=new RegExp(Dc,"g"),r=[];let i=null;for(;i=t.exec(e);){const e=i[3].replace(new RegExp(`[${Rc}]+`,"g"),""),t={type:i[1],headers:[],rawData:l.FromBase64(e)},n=i[2];if(n){const e=n.split(new RegExp(Uc,"g"));let r=null;for(const i of e){const[e,n]=i.split(/:(.*)/);if(void 0===n){if(!r)throw new Error("Cannot parse PEM string. Incorrect header value");r.value+=e.trim()}else r&&t.headers.push(r),r={key:e,value:n.trim()}}r&&t.headers.push(r)}r.push(t)}return r}static decode(e){return this.decodeWithHeaders(e).map((e=>e.rawData))}static decodeFirst(e){const t=this.decode(e);if(!t.length)throw new RangeError("PEM string doesn't contain any objects");return t[0]}static encode(e,t){if(Array.isArray(e)){const r=new Array;return t?e.forEach((e=>{if(!i.isBufferSource(e))throw new TypeError("Cannot encode array of BufferSource in PEM format. Not all items of the array are BufferSource");r.push(this.encodeStruct({type:t,rawData:i.toArrayBuffer(e)}))})):e.forEach((e=>{if(!("type"in e))throw new TypeError("Cannot encode array of PemStruct in PEM format. Not all items of the array are PemStrut");r.push(this.encodeStruct(e))})),r.join("\n")}if(!t)throw new Error("Required argument 'tag' is missed");return this.encodeStruct({type:t,rawData:i.toArrayBuffer(e)})}static encodeStruct(e){var t;const r=e.type.toLocaleUpperCase(),i=[];if(i.push(`-----BEGIN ${r}-----`),null===(t=e.headers)||void 0===t?void 0:t.length){for(const t of e.headers)i.push(`${t.key}: ${t.value}`);i.push("")}const n=l.ToBase64(e.rawData);let s,o=0;const a=Array();for(;o<n.length&&(n.length-o<64?s=n.substring(o):(s=n.substring(o,o+64),o+=64),0!==s.length)&&(a.push(s),!(s.length<64)););return i.push(...a),i.push(`-----END ${r}-----`),i.join("\n")}}Vc.CertificateTag="CERTIFICATE",Vc.CrlTag="CRL",Vc.CertificateRequestTag="CERTIFICATE REQUEST",Vc.PublicKeyTag="PUBLIC KEY",Vc.PrivateKeyTag="PRIVATE KEY";class Lc extends ac{static isAsnEncoded(e){return i.isBufferSource(e)||"string"==typeof e}static toArrayBuffer(e){if("string"==typeof e){if(Vc.isPem(e))return Vc.decode(e)[0];if(l.isHex(e))return l.FromHex(e);if(l.isBase64(e))return l.FromBase64(e);if(l.isBase64Url(e))return l.FromBase64Url(e);throw new TypeError("Unsupported format of 'raw' argument. Must be one of DER, PEM, HEX, Base64, or Base4Url")}{const t=l.ToBinary(e);return Vc.isPem(t)?Vc.decode(t)[0]:l.isHex(t)?l.FromHex(t):l.isBase64(t)?l.FromBase64(t):l.isBase64Url(t)?l.FromBase64Url(t):i.toArrayBuffer(e)}}constructor(...e){Lc.isAsnEncoded(e[0])?super(Lc.toArrayBuffer(e[0]),e[1]):super(e[0])}toString(e="pem"){return"pem"===e?Vc.encode(this.rawData,this.tag):super.toString(e)}}class Hc extends Lc{static async create(e,t=pc.get()){if(e instanceof Hc)return e;if(lc.isCryptoKey(e)){if("public"!==e.type)throw new TypeError("Public key is required");const r=await t.subtle.exportKey("spki",e);return new Hc(r)}if(e.publicKey)return e.publicKey;if(i.isBufferSource(e))return new Hc(e);throw new TypeError("Unsupported PublicKeyType")}constructor(e){Lc.isAsnEncoded(e)?super(e,dn):super(e),this.tag=Vc.PublicKeyTag}async export(...e){let t,r=["verify"],i={hash:"SHA-256",...this.algorithm};e.length>1?(i=e[0]||i,r=e[1]||r,t=e[2]||pc.get()):t=e[0]||pc.get();let n=this.rawData;const s=lr.parse(this.rawData,dn);return s.algorithm.algorithm===Co&&(n=function(e,t){return e.algorithm=new yn({algorithm:Bo,parameters:null}),t=lr.serialize(e),t}(s,n)),t.subtle.importKey("spki",n,i,!0,r)}onInit(e){const t=wa.resolve(xa),r=this.algorithm=t.toWebAlgorithm(e.algorithm);switch(e.algorithm.algorithm){case Bo:{const t=lr.parse(e.subjectPublicKey,ea),n=i.toUint8Array(t.modulus);r.publicExponent=i.toUint8Array(t.publicExponent),r.modulusLength=(n[0]?n:n.slice(1)).byteLength<<3;break}}}async getThumbprint(...e){var t;let r,i="SHA-1";return e.length>=1&&!(null===(t=e[0])||void 0===t?void 0:t.subtle)?(i=e[0]||i,r=e[1]||pc.get()):r=e[0]||pc.get(),await r.subtle.digest(i,this.rawData)}async getKeyIdentifier(...e){let t,r="SHA-1";1===e.length?"string"==typeof e[0]?(r=e[0],t=pc.get()):t=e[0]:2===e.length?(r=e[0],t=e[1]):t=pc.get();const i=lr.parse(this.rawData,dn);return await t.subtle.digest(r,i.subjectPublicKey)}toTextObject(){const e=this.toTextObjectEmpty(),t=lr.parse(this.rawData,dn);if(e.Algorithm=oc.serializeAlgorithm(t.algorithm),t.algorithm.algorithm===to)e["EC Point"]=t.subjectPublicKey;else e["Raw Data"]=t.subjectPublicKey;return e}}class $c extends cc{static async create(e,t=!1,r=pc.get()){if("name"in e&&"serialNumber"in e)return new $c(e,t);const i=await Hc.create(e,r),n=await i.getKeyIdentifier(r);return new $c(l.ToHex(n),t)}constructor(...e){if(i.isBufferSource(e[0]))super(e[0]);else if("string"==typeof e[0]){const t=new Xr({keyIdentifier:new Jr(l.FromHex(e[0]))});super(Gr,e[1],lr.serialize(t))}else{const t=e[0],r=t.name instanceof jc?lr.parse(t.name.rawData,ei):t.name,i=new Xr({authorityCertIssuer:r,authorityCertSerialNumber:l.FromHex(t.serialNumber)});super(Gr,e[1],lr.serialize(i))}}onInit(e){super.onInit(e);const t=lr.parse(e.extnValue,Xr);t.keyIdentifier&&(this.keyId=l.ToHex(t.keyIdentifier)),(t.authorityCertIssuer||t.authorityCertSerialNumber)&&(this.certId={name:t.authorityCertIssuer||[],serialNumber:t.authorityCertSerialNumber?l.ToHex(t.authorityCertSerialNumber):""})}toTextObject(){const e=this.toTextObjectWithoutValue(),t=lr.parse(this.value,Xr);return t.authorityCertIssuer&&(e["Authority Issuer"]=new jc(t.authorityCertIssuer).toTextObject()),t.authorityCertSerialNumber&&(e["Authority Serial Number"]=t.authorityCertSerialNumber),t.keyIdentifier&&(e[""]=t.keyIdentifier),e}}$c.NAME="Authority Key Identifier";class Mc extends cc{constructor(...e){if(i.isBufferSource(e[0])){super(e[0]);const t=lr.parse(this.value,Yr);this.ca=t.cA,this.pathLength=t.pathLenConstraint}else{const t=new Yr({cA:e[0],pathLenConstraint:e[1]});super(Zr,e[2],lr.serialize(t)),this.ca=e[0],this.pathLength=e[1]}}toTextObject(){const e=this.toTextObjectWithoutValue();return this.ca&&(e.CA=this.ca),void 0!==this.pathLength&&(e["Path Length"]=this.pathLength),e}}Mc.NAME="Basic Constraints",e.ExtendedKeyUsage=void 0,function(e){e.serverAuth="*******.5.5.7.3.1",e.clientAuth="*******.5.5.7.3.2",e.codeSigning="*******.5.5.7.3.3",e.emailProtection="*******.5.5.7.3.4",e.timeStamping="*******.5.5.7.3.8",e.ocspSigning="*******.5.5.7.3.9"}(e.ExtendedKeyUsage||(e.ExtendedKeyUsage={}));class Fc extends cc{constructor(...e){if(i.isBufferSource(e[0])){super(e[0]);const t=lr.parse(this.value,Ni);this.usages=t.map((e=>e))}else{const t=new Ni(e[0]);super(Oi,e[1],lr.serialize(t)),this.usages=e[0]}}toTextObject(){const e=this.toTextObjectWithoutValue();return e[""]=this.usages.map((e=>sc.toString(e))).join(", "),e}}Fc.NAME="Extended Key Usages",e.KeyUsageFlags=void 0,function(e){e[e.digitalSignature=1]="digitalSignature",e[e.nonRepudiation=2]="nonRepudiation",e[e.keyEncipherment=4]="keyEncipherment",e[e.dataEncipherment=8]="dataEncipherment",e[e.keyAgreement=16]="keyAgreement",e[e.keyCertSign=32]="keyCertSign",e[e.cRLSign=64]="cRLSign",e[e.encipherOnly=128]="encipherOnly",e[e.decipherOnly=256]="decipherOnly"}(e.KeyUsageFlags||(e.KeyUsageFlags={}));class Kc extends cc{constructor(...e){if(i.isBufferSource(e[0])){super(e[0]);const t=lr.parse(this.value,_i);this.usages=t.toNumber()}else{const t=new _i(e[0]);super(Fi,e[1],lr.serialize(t)),this.usages=e[0]}}toTextObject(){const e=this.toTextObjectWithoutValue(),t=lr.parse(this.value,_i);return e[""]=t.toJSON().join(", "),e}}Kc.NAME="Key Usages";class zc extends cc{static async create(e,t=!1,r=pc.get()){const i=await Hc.create(e,r),n=await i.getKeyIdentifier(r);return new zc(l.ToHex(n),t)}constructor(...e){if(i.isBufferSource(e[0])){super(e[0]);const t=lr.parse(this.value,an);this.keyId=l.ToHex(t)}else{const t="string"==typeof e[0]?l.FromHex(e[0]):e[0],r=new an(t);super(on,e[1],lr.serialize(r)),this.keyId=l.ToHex(t)}}toTextObject(){const e=this.toTextObjectWithoutValue(),t=lr.parse(this.value,an);return e[""]=t,e}}zc.NAME="Subject Key Identifier";class _c extends cc{constructor(...e){i.isBufferSource(e[0])?super(e[0]):super(en,e[1],new jc(e[0]||[]).rawData)}onInit(e){super.onInit(e);const t=lr.parse(e.extnValue,tn);this.names=new jc(t)}toTextObject(){const e=this.toTextObjectWithoutValue(),t=this.names.toTextObject();for(const r in t)e[r]=t[r];return e}}_c.NAME="Subject Alternative Name";class qc{static register(e,t){this.items.set(e,t)}static create(e){const t=new cc(e),r=this.items.get(t.type);return r?new r(e):t}}qc.items=new Map;class Wc extends cc{constructor(...e){var t;if(i.isBufferSource(e[0])){super(e[0]);const t=lr.parse(this.value,hi);this.policies=t.map((e=>e.policyIdentifier))}else{const r=e[0],i=null!==(t=e[1])&&void 0!==t&&t,n=new hi(r.map((e=>new pi({policyIdentifier:e}))));super(si,i,lr.serialize(n)),this.policies=r}}toTextObject(){const e=this.toTextObjectWithoutValue();return e.Policy=this.policies.map((e=>new ic("",{},sc.toString(e)))),e}}Wc.NAME="Certificate Policies",qc.register(si,Wc);class Gc extends cc{constructor(...e){var t;if(i.isBufferSource(e[0]))super(e[0]);else if(Array.isArray(e[0])&&"string"==typeof e[0][0]){const t=e[0].map((e=>new bi({distributionPoint:new wi({fullName:[new Ur({uniformResourceIdentifier:e})]})}))),r=new Ai(t);super(gi,e[1],lr.serialize(r))}else{const t=new Ai(e[0]);super(gi,e[1],lr.serialize(t))}null!==(t=this.distributionPoints)&&void 0!==t||(this.distributionPoints=[])}onInit(e){super.onInit(e);const t=lr.parse(e.extnValue,Ai);this.distributionPoints=t}toTextObject(){const e=this.toTextObjectWithoutValue();return e["Distribution Point"]=this.distributionPoints.map((e=>{var t;const r={};return e.distributionPoint&&(r[""]=null===(t=e.distributionPoint.fullName)||void 0===t?void 0:t.map((e=>new Tc(e).toString())).join(", ")),e.reasons&&(r.Reasons=e.reasons.toString()),e.cRLIssuer&&(r["CRL Issuer"]=e.cRLIssuer.map((e=>e.toString())).join(", ")),r})),e}}Gc.NAME="CRL Distribution Points";class Jc extends cc{constructor(...e){var t,r,n,s;if(i.isBufferSource(e[0]))super(e[0]);else if(e[0]instanceof Wr){const t=new Wr(e[0]);super(_r,e[1],lr.serialize(t))}else{const t=e[0],r=new Wr;Zc(r,t,Hr,"ocsp"),Zc(r,t,$r,"caIssuers"),Zc(r,t,Mr,"timeStamping"),Zc(r,t,Fr,"caRepository"),super(_r,e[1],lr.serialize(r))}null!==(t=this.ocsp)&&void 0!==t||(this.ocsp=[]),null!==(r=this.caIssuers)&&void 0!==r||(this.caIssuers=[]),null!==(n=this.timeStamping)&&void 0!==n||(this.timeStamping=[]),null!==(s=this.caRepository)&&void 0!==s||(this.caRepository=[])}onInit(e){super.onInit(e),this.ocsp=[],this.caIssuers=[],this.timeStamping=[],this.caRepository=[];lr.parse(e.extnValue,Wr).forEach((e=>{switch(e.accessMethod){case Hr:this.ocsp.push(new Tc(e.accessLocation));break;case $r:this.caIssuers.push(new Tc(e.accessLocation));break;case Mr:this.timeStamping.push(new Tc(e.accessLocation));break;case Fr:this.caRepository.push(new Tc(e.accessLocation))}}))}toTextObject(){const e=this.toTextObjectWithoutValue();return this.ocsp.length&&Xc(e,"OCSP",this.ocsp),this.caIssuers.length&&Xc(e,"CA Issuers",this.caIssuers),this.timeStamping.length&&Xc(e,"Time Stamping",this.timeStamping),this.caRepository.length&&Xc(e,"CA Repository",this.caRepository),e}}function Xc(e,t,r){if(1===r.length)e[t]=r[0].toTextObject();else{const i=new ic("");r.forEach(((e,t)=>{const r=e.toTextObject(),n=`${r[ic.NAME]} ${t+1}`;let s=i[n];Array.isArray(s)||(s=[],i[n]=s),s.push(r)})),e[t]=i}}function Zc(e,t,r,i){const n=t[i];if(n){(Array.isArray(n)?n:[n]).forEach((t=>{"string"==typeof t&&(t=new Tc("url",t)),e.push(new qr({accessMethod:r,accessLocation:lr.parse(t.rawData,Ur)}))}))}}var Yc;Jc.NAME="Authority Info Access";class Qc{constructor(e={}){this.attrId="",this.attrValues=[],Object.assign(e)}}fr([sr({type:Bt.ObjectIdentifier})],Qc.prototype,"attrId",void 0),fr([sr({type:Bt.Any,repeated:"set"})],Qc.prototype,"attrValues",void 0);let eu=Yc=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Yc.prototype)}};var tu;eu=Yc=fr([nr({type:xt.Sequence,itemType:Qc})],eu);let ru=tu=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,tu.prototype)}};ru=tu=fr([nr({type:xt.Sequence,itemType:bs})],ru);class iu{constructor(e={}){this.certId="",this.certValue=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],iu.prototype,"certId",void 0),fr([sr({type:Bt.Any,context:0})],iu.prototype,"certValue",void 0);class nu{constructor(e={}){this.crlId="",this.crltValue=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],nu.prototype,"crlId",void 0),fr([sr({type:Bt.Any,context:0})],nu.prototype,"crltValue",void 0);class su extends Ct{}let ou=class{constructor(e={}){this.encryptionAlgorithm=new yn,this.encryptedData=new su,Object.assign(this,e)}};var au,cu;fr([sr({type:yn})],ou.prototype,"encryptionAlgorithm",void 0),fr([sr({type:su})],ou.prototype,"encryptedData",void 0),function(e){e[e.v1=0]="v1"}(cu||(cu={}));class uu extends Ct{}let lu=au=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,au.prototype)}};lu=au=fr([nr({type:xt.Sequence,itemType:rn})],lu);class pu{constructor(e={}){this.version=cu.v1,this.privateKeyAlgorithm=new yn,this.privateKey=new uu,Object.assign(this,e)}}fr([sr({type:Bt.Integer})],pu.prototype,"version",void 0),fr([sr({type:yn})],pu.prototype,"privateKeyAlgorithm",void 0),fr([sr({type:uu})],pu.prototype,"privateKey",void 0),fr([sr({type:lu,implicit:!0,context:0,optional:!0})],pu.prototype,"attributes",void 0);let hu=class extends pu{};hu=fr([nr({type:xt.Sequence})],hu);let fu=class extends ou{};fu=fr([nr({type:xt.Sequence})],fu);class yu{constructor(e={}){this.secretTypeId="",this.secretValue=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],yu.prototype,"secretTypeId",void 0),fr([sr({type:Bt.Any,context:0})],yu.prototype,"secretValue",void 0);class du{constructor(e={}){this.mac=new Jo,this.macSalt=new Ct,this.iterations=1,Object.assign(this,e)}}fr([sr({type:Jo})],du.prototype,"mac",void 0),fr([sr({type:Ct})],du.prototype,"macSalt",void 0),fr([sr({type:Bt.Integer,defaultValue:1})],du.prototype,"iterations",void 0);class gu{constructor(e={}){this.version=3,this.authSafe=new bs,this.macData=new du,Object.assign(this,e)}}var vu;fr([sr({type:Bt.Integer})],gu.prototype,"version",void 0),fr([sr({type:bs})],gu.prototype,"authSafe",void 0),fr([sr({type:du,optional:!0})],gu.prototype,"macData",void 0);class mu{constructor(e={}){this.bagId="",this.bagValue=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:Bt.ObjectIdentifier})],mu.prototype,"bagId",void 0),fr([sr({type:Bt.Any,context:0})],mu.prototype,"bagValue",void 0),fr([sr({type:Qc,repeated:"set",optional:!0})],mu.prototype,"bagAttributes",void 0);let wu=vu=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,vu.prototype)}};var bu,Au,Su;wu=vu=fr([nr({type:xt.Sequence,itemType:mu})],wu);const xu="1.2.840.113549.1.9",Bu=`${xu}.7`,ku=`${xu}.14`;let Eu=class extends Er{constructor(e={}){super(e)}toString(){return this.ia5String||super.toString()}};fr([sr({type:Bt.IA5String})],Eu.prototype,"ia5String",void 0),Eu=fr([nr({type:xt.Choice})],Eu);let Cu=class extends bs{};Cu=fr([nr({type:xt.Sequence})],Cu);let Iu=class extends gu{};Iu=fr([nr({type:xt.Sequence})],Iu);let Ou=class extends ou{};Ou=fr([nr({type:xt.Sequence})],Ou);let Nu=class{constructor(e=""){this.value=e}toString(){return this.value}};fr([sr({type:Bt.IA5String})],Nu.prototype,"value",void 0),Nu=fr([nr({type:xt.Choice})],Nu);let Tu=class extends Eu{};Tu=fr([nr({type:xt.Choice})],Tu);let ju=class extends Er{};ju=fr([nr({type:xt.Choice})],ju);let Pu=class{constructor(e=new Date){this.value=e}};fr([sr({type:Bt.GeneralizedTime})],Pu.prototype,"value",void 0),Pu=fr([nr({type:xt.Choice})],Pu);let Ru=class extends Er{};Ru=fr([nr({type:xt.Choice})],Ru);let Uu=class{constructor(e="M"){this.value=e}toString(){return this.value}};fr([sr({type:Bt.PrintableString})],Uu.prototype,"value",void 0),Uu=fr([nr({type:xt.Choice})],Uu);let Du=class{constructor(e=""){this.value=e}toString(){return this.value}};fr([sr({type:Bt.PrintableString})],Du.prototype,"value",void 0),Du=fr([nr({type:xt.Choice})],Du);let Vu=class extends Du{};Vu=fr([nr({type:xt.Choice})],Vu);let Lu=class extends Er{};Lu=fr([nr({type:xt.Choice})],Lu);let Hu=class{constructor(e=""){this.value=e}toString(){return this.value}};fr([sr({type:Bt.ObjectIdentifier})],Hu.prototype,"value",void 0),Hu=fr([nr({type:xt.Choice})],Hu);let $u=class extends gn{};$u=fr([nr({type:xt.Choice})],$u);let Mu=class{constructor(e=0){this.value=e}toString(){return this.value.toString()}};fr([sr({type:Bt.Integer})],Mu.prototype,"value",void 0),Mu=fr([nr({type:xt.Choice})],Mu);let Fu=class extends Ln{};Fu=fr([nr({type:xt.Sequence})],Fu);let Ku=class extends Er{};Ku=fr([nr({type:xt.Choice})],Ku);let zu=bu=class extends bn{constructor(e){super(e),Object.setPrototypeOf(this,bu.prototype)}};zu=bu=fr([nr({type:xt.Sequence})],zu);let _u=Au=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Au.prototype)}};_u=Au=fr([nr({type:xt.Set,itemType:Dn})],_u);let qu=class{constructor(e=""){this.value=e}toString(){return this.value}};fr([sr({type:Bt.BmpString})],qu.prototype,"value",void 0),qu=fr([nr({type:xt.Choice})],qu);let Wu=class extends yn{};Wu=fr([nr({type:xt.Sequence})],Wu);let Gu=Su=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,Su.prototype)}};Gu=Su=fr([nr({type:xt.Sequence,itemType:Wu})],Gu);class Ju extends ac{constructor(...e){let t;if(i.isBufferSource(e[0]))t=i.toArrayBuffer(e[0]);else{const r=e[0],n=Array.isArray(e[1])?e[1].map((e=>i.toArrayBuffer(e))):[];t=lr.serialize(new rn({type:r,values:n}))}super(t,rn)}onInit(e){this.type=e.type,this.values=e.values}toTextObject(){const e=this.toTextObjectWithoutValue();return e.Value=this.values.map((e=>new ic("",{"":e}))),e}toTextObjectWithoutValue(){const e=this.toTextObjectEmpty();return e[ic.NAME]===Ju.NAME&&(e[ic.NAME]=sc.toString(this.type)),e}}Ju.NAME="Attribute";class Xu extends Ju{constructor(...e){var t;if(i.isBufferSource(e[0]))super(e[0]);else{const t=new Ku({printableString:e[0]});super(Bu,[lr.serialize(t)])}null!==(t=this.password)&&void 0!==t||(this.password="")}onInit(e){if(super.onInit(e),this.values[0]){const e=lr.parse(this.values[0],Ku);this.password=e.toString()}}toTextObject(){const e=this.toTextObjectWithoutValue();return e[ic.VALUE]=this.password,e}}Xu.NAME="Challenge Password";class Zu extends Ju{constructor(...e){var t;if(i.isBufferSource(e[0]))super(e[0]);else{const t=e[0],r=new bn;for(const e of t)r.push(lr.parse(e.rawData,wn));super(ku,[lr.serialize(r)])}null!==(t=this.items)&&void 0!==t||(this.items=[])}onInit(e){if(super.onInit(e),this.values[0]){const e=lr.parse(this.values[0],bn);this.items=e.map((e=>qc.create(lr.serialize(e))))}}toTextObject(){const e=this.toTextObjectWithoutValue(),t=this.items.map((e=>e.toTextObject()));for(const r of t)e[r[ic.NAME]]=r;return e}}Zu.NAME="Extensions";class Yu{static register(e,t){this.items.set(e,t)}static create(e){const t=new Ju(e),r=this.items.get(t.type);return r?new r(e):t}}Yu.items=new Map;const Qu="crypto.signatureFormatter";class el{toAsnSignature(e,t){return i.toArrayBuffer(t)}toWebSignature(e,t){return i.toArrayBuffer(t)}}var tl;e.RsaAlgorithm=tl=class{static createPssParams(e,t){const r=tl.getHashAlgorithm(e);return r?new Go({hashAlgorithm:r,maskGenAlgorithm:new yn({algorithm:Fo,parameters:lr.serialize(r)}),saltLength:t}):null}static getHashAlgorithm(e){const t=wa.resolve(xa);return"string"==typeof e?t.toAsnAlgorithm({name:e}):"object"==typeof e&&e&&"name"in e?t.toAsnAlgorithm(e):null}toAsnAlgorithm(e){switch(e.name.toLowerCase()){case"rsassa-pkcs1-v1_5":if(!("hash"in e))return new yn({algorithm:Bo,parameters:null});{let t;if("string"==typeof e.hash)t=e.hash;else{if(!e.hash||"object"!=typeof e.hash||!("name"in e.hash)||"string"!=typeof e.hash.name)throw new Error("Cannot get hash algorithm name");t=e.hash.name.toUpperCase()}switch(t.toLowerCase()){case"sha-1":return new yn({algorithm:No,parameters:null});case"sha-256":return new yn({algorithm:jo,parameters:null});case"sha-384":return new yn({algorithm:Po,parameters:null});case"sha-512":return new yn({algorithm:Ro,parameters:null})}}break;case"rsa-pss":if("hash"in e){if(!("saltLength"in e)||"number"!=typeof e.saltLength)throw new Error("Cannot get 'saltLength' from 'alg' argument");const t=tl.createPssParams(e.hash,e.saltLength);if(!t)throw new Error("Cannot create PSS parameters");return new yn({algorithm:Co,parameters:lr.serialize(t)})}return new yn({algorithm:Co,parameters:null})}return null}toWebAlgorithm(e){switch(e.algorithm){case Bo:return{name:"RSASSA-PKCS1-v1_5"};case No:return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-1"}};case jo:return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case Po:return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-384"}};case Ro:return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-512"}};case Co:if(e.parameters){const t=lr.parse(e.parameters,Go);return{name:"RSA-PSS",hash:wa.resolve(xa).toWebAlgorithm(t.hashAlgorithm),saltLength:t.saltLength}}return{name:"RSA-PSS"}}return null}},e.RsaAlgorithm=tl=fr([ba()],e.RsaAlgorithm),wa.registerSingleton(Aa,e.RsaAlgorithm),e.ShaAlgorithm=class{toAsnAlgorithm(e){switch(e.name.toLowerCase()){case"sha-1":return new yn({algorithm:Vo});case"sha-256":return new yn({algorithm:Ho});case"sha-384":return new yn({algorithm:$o});case"sha-512":return new yn({algorithm:Mo})}return null}toWebAlgorithm(e){switch(e.algorithm){case Vo:return{name:"SHA-1"};case Ho:return{name:"SHA-256"};case $o:return{name:"SHA-384"};case Mo:return{name:"SHA-512"}}return null}},e.ShaAlgorithm=fr([ba()],e.ShaAlgorithm),wa.registerSingleton(Aa,e.ShaAlgorithm);class rl{addPadding(e,t){const r=i.toUint8Array(t),n=new Uint8Array(e);return n.set(r,e-r.length),n}removePadding(e,t=!1){let r=i.toUint8Array(e);for(let e=0;e<r.length;e++)if(r[e]){r=r.slice(e);break}if(t&&r[0]>127){const e=new Uint8Array(r.length+1);return e.set(r,1),e.buffer}return r.buffer}toAsnSignature(e,t){if("ECDSA"===e.name){const r=e.namedCurve,n=rl.namedCurveSize.get(r)||rl.defaultNamedCurveSize,s=new So,o=i.toUint8Array(t);return s.r=this.removePadding(o.slice(0,n),!0),s.s=this.removePadding(o.slice(n,n+n),!0),lr.serialize(s)}return null}toWebSignature(e,t){if("ECDSA"===e.name){const r=lr.parse(t,So),i=e.namedCurve,n=rl.namedCurveSize.get(i)||rl.defaultNamedCurveSize;return function(...e){const t=e.map((e=>e.byteLength)).reduce(((e,t)=>e+t)),r=new Uint8Array(t);let i=0;return e.map((e=>new Uint8Array(e))).forEach((e=>{for(const t of e)r[i++]=t})),r.buffer}(this.addPadding(n,this.removePadding(r.r)),this.addPadding(n,this.removePadding(r.s)))}return null}}rl.namedCurveSize=new Map,rl.defaultNamedCurveSize=32;const il="1.3.101.110",nl="1.3.101.111",sl="1.3.101.112",ol="1.3.101.113";var al;e.EdAlgorithm=class{toAsnAlgorithm(e){let t=null;switch(e.name.toLowerCase()){case"ed25519":t=sl;break;case"x25519":t=il;break;case"eddsa":switch(e.namedCurve.toLowerCase()){case"ed25519":t=sl;break;case"ed448":t=ol}break;case"ecdh-es":switch(e.namedCurve.toLowerCase()){case"x25519":t=il;break;case"x448":t=nl}}return t?new yn({algorithm:t}):null}toWebAlgorithm(e){switch(e.algorithm){case sl:return{name:"Ed25519"};case ol:return{name:"EdDSA",namedCurve:"Ed448"};case il:return{name:"X25519"};case nl:return{name:"ECDH-ES",namedCurve:"X448"}}return null}},e.EdAlgorithm=fr([ba()],e.EdAlgorithm),wa.registerSingleton(Aa,e.EdAlgorithm);let cl=al=class extends ur{constructor(e){super(e),Object.setPrototypeOf(this,al.prototype)}};cl=al=fr([nr({type:xt.Sequence,itemType:rn})],cl);class ul{constructor(e={}){this.version=0,this.subject=new Tr,this.subjectPKInfo=new dn,this.attributes=new cl,Object.assign(this,e)}}fr([sr({type:Bt.Integer})],ul.prototype,"version",void 0),fr([sr({type:Tr})],ul.prototype,"subject",void 0),fr([sr({type:dn})],ul.prototype,"subjectPKInfo",void 0),fr([sr({type:cl,implicit:!0,context:0})],ul.prototype,"attributes",void 0);class ll{constructor(e={}){this.certificationRequestInfo=new ul,this.signatureAlgorithm=new yn,this.signature=new ArrayBuffer(0),Object.assign(this,e)}}fr([sr({type:ul})],ll.prototype,"certificationRequestInfo",void 0),fr([sr({type:yn})],ll.prototype,"signatureAlgorithm",void 0),fr([sr({type:Bt.BitString})],ll.prototype,"signature",void 0);class pl extends Lc{constructor(e){Lc.isAsnEncoded(e)?super(e,ll):super(e),this.tag=Vc.CertificateRequestTag}onInit(e){this.tbs=lr.serialize(e.certificationRequestInfo),this.publicKey=new Hc(e.certificationRequestInfo.subjectPKInfo);const t=wa.resolve(xa);this.signatureAlgorithm=t.toWebAlgorithm(e.signatureAlgorithm),this.signature=e.signature,this.attributes=e.certificationRequestInfo.attributes.map((e=>Yu.create(lr.serialize(e))));const r=this.getAttribute(ku);this.extensions=[],r instanceof Zu&&(this.extensions=r.items),this.subjectName=new gc(e.certificationRequestInfo.subject),this.subject=this.subjectName.toString()}getAttribute(e){for(const t of this.attributes)if(t.type===e)return t;return null}getAttributes(e){return this.attributes.filter((t=>t.type===e))}getExtension(e){for(const t of this.extensions)if(t.type===e)return t;return null}getExtensions(e){return this.extensions.filter((t=>t.type===e))}async verify(e=pc.get()){const t={...this.publicKey.algorithm,...this.signatureAlgorithm},r=await this.publicKey.export(t,["verify"],e),i=wa.resolveAll(Qu).reverse();let n=null;for(const e of i)if(n=e.toWebSignature(t,this.signature),n)break;if(!n)throw Error("Cannot convert WebCrypto signature value to ASN.1 format");return await e.subtle.verify(this.signatureAlgorithm,r,n,this.tbs)}toTextObject(){const e=this.toTextObjectEmpty(),t=lr.parse(this.rawData,ll),r=t.certificationRequestInfo,i=new ic("",{Version:`${An[r.version]} (${r.version})`,Subject:this.subject,"Subject Public Key Info":this.publicKey});if(this.attributes.length){const e=new ic("");for(const t of this.attributes){const r=t.toTextObject();e[r[ic.NAME]]=r}i.Attributes=e}return e.Data=i,e.Signature=new ic("",{Algorithm:oc.serializeAlgorithm(t.signatureAlgorithm),"":t.signature}),e}}pl.NAME="PKCS#10 Certificate Request";class hl extends Lc{constructor(e){Lc.isAsnEncoded(e)?super(e,xn):super(e),this.tag=Vc.CertificateTag}onInit(e){const t=e.tbsCertificate;this.tbs=lr.serialize(t),this.serialNumber=l.ToHex(t.serialNumber),this.subjectName=new gc(t.subject),this.subject=new gc(t.subject).toString(),this.issuerName=new gc(t.issuer),this.issuer=this.issuerName.toString();const r=wa.resolve(xa);this.signatureAlgorithm=r.toWebAlgorithm(e.signatureAlgorithm),this.signature=e.signatureValue;const i=t.validity.notBefore.utcTime||t.validity.notBefore.generalTime;if(!i)throw new Error("Cannot get 'notBefore' value");this.notBefore=i;const n=t.validity.notAfter.utcTime||t.validity.notAfter.generalTime;if(!n)throw new Error("Cannot get 'notAfter' value");this.notAfter=n,this.extensions=[],t.extensions&&(this.extensions=t.extensions.map((e=>qc.create(lr.serialize(e))))),this.publicKey=new Hc(t.subjectPublicKeyInfo)}getExtension(e){for(const t of this.extensions)if("string"==typeof e){if(t.type===e)return t}else if(t instanceof e)return t;return null}getExtensions(e){return this.extensions.filter((t=>"string"==typeof e?t.type===e:t instanceof e))}async verify(e={},t=pc.get()){let r,n;const s=e.publicKey;try{if(s)if("publicKey"in s)r={...s.publicKey.algorithm,...this.signatureAlgorithm},n=await s.publicKey.export(r,["verify"],t);else if(s instanceof Hc)r={...s.algorithm,...this.signatureAlgorithm},n=await s.export(r,["verify"],t);else if(i.isBufferSource(s)){const e=new Hc(s);r={...e.algorithm,...this.signatureAlgorithm},n=await e.export(r,["verify"],t)}else r={...s.algorithm,...this.signatureAlgorithm},n=s;else r={...this.publicKey.algorithm,...this.signatureAlgorithm},n=await this.publicKey.export(r,["verify"],t)}catch(e){return!1}const o=wa.resolveAll(Qu).reverse();let a=null;for(const e of o)if(a=e.toWebSignature(r,this.signature),a)break;if(!a)throw Error("Cannot convert ASN.1 signature value to WebCrypto format");const c=await t.subtle.verify(this.signatureAlgorithm,n,a,this.tbs);if(e.signatureOnly)return c;{const t=(e.date||new Date).getTime();return c&&this.notBefore.getTime()<t&&t<this.notAfter.getTime()}}async getThumbprint(...e){let t,r="SHA-1";return e[0]&&(e[0].subtle?t=e[0]:(r=e[0]||r,t=e[1])),null!=t||(t=pc.get()),await t.subtle.digest(r,this.rawData)}async isSelfSigned(e=pc.get()){return this.subject===this.issuer&&await this.verify({signatureOnly:!0},e)}toTextObject(){const e=this.toTextObjectEmpty(),t=lr.parse(this.rawData,xn),r=t.tbsCertificate,i=new ic("",{Version:`${An[r.version]} (${r.version})`,"Serial Number":r.serialNumber,"Signature Algorithm":oc.serializeAlgorithm(r.signature),Issuer:this.issuer,Validity:new ic("",{"Not Before":r.validity.notBefore.getTime(),"Not After":r.validity.notAfter.getTime()}),Subject:this.subject,"Subject Public Key Info":this.publicKey});if(r.issuerUniqueID&&(i["Issuer Unique ID"]=r.issuerUniqueID),r.subjectUniqueID&&(i["Subject Unique ID"]=r.subjectUniqueID),this.extensions.length){const e=new ic("");for(const t of this.extensions){const r=t.toTextObject();e[r[ic.NAME]]=r}i.Extensions=e}return e.Data=i,e.Signature=new ic("",{Algorithm:oc.serializeAlgorithm(t.signatureAlgorithm),"":t.signatureValue}),e}}hl.NAME="Certificate";class fl extends Array{constructor(e){if(super(),Lc.isAsnEncoded(e))this.import(e);else if(e instanceof hl)this.push(e);else if(Array.isArray(e))for(const t of e)this.push(t)}export(e){const t=new eo;t.version=1,t.encapContentInfo.eContentType="1.2.840.113549.1.7.1",t.encapContentInfo.eContent=new As({single:new Ct}),t.certificates=new ws(this.map((e=>new ms({certificate:lr.parse(e.rawData,xn)}))));const r=new bs({contentType:Zs,content:lr.serialize(t)}),i=lr.serialize(r);return"raw"===e?i:this.toString(e)}import(e){const t=Lc.toArrayBuffer(e),r=lr.parse(t,bs);if(r.contentType!==Zs)throw new TypeError("Cannot parse CMS package. Incoming data is not a SignedData object.");const i=lr.parse(r.content,eo);this.clear();for(const e of i.certificates||[])e.certificate&&this.push(new hl(e.certificate))}clear(){for(;this.pop(););}toString(e="pem"){const t=this.export("raw");switch(e){case"pem":return Vc.encode(t,"CMS");case"pem-chain":return this.map((e=>e.toString("pem"))).join("\n");case"asn":return lr.toString(t);case"hex":return l.ToHex(t);case"base64":return l.ToBase64(t);case"base64url":return l.ToBase64Url(t);case"text":return oc.serialize(this.toTextObject());default:throw TypeError("Argument 'format' is unsupported value")}}toTextObject(){const e=lr.parse(this.export("raw"),bs),t=lr.parse(e.content,eo);return new ic("X509Certificates",{"Content Type":sc.toString(e.contentType),Content:new ic("",{Version:`${On[t.version]} (${t.version})`,Certificates:new ic("",{Certificate:this.map((e=>e.toTextObject()))})})})}}var yl;e.X509CrlReason=void 0,(yl=e.X509CrlReason||(e.X509CrlReason={}))[yl.unspecified=0]="unspecified",yl[yl.keyCompromise=1]="keyCompromise",yl[yl.cACompromise=2]="cACompromise",yl[yl.affiliationChanged=3]="affiliationChanged",yl[yl.superseded=4]="superseded",yl[yl.cessationOfOperation=5]="cessationOfOperation",yl[yl.certificateHold=6]="certificateHold",yl[yl.removeFromCRL=8]="removeFromCRL",yl[yl.privilegeWithdrawn=9]="privilegeWithdrawn",yl[yl.aACompromise=10]="aACompromise";class dl extends ac{constructor(...e){let t;t=i.isBufferSource(e[0])?i.toArrayBuffer(e[0]):lr.serialize(new Bn({userCertificate:e[0],revocationDate:new gn(e[1]),crlEntryExtensions:e[2]})),super(t,Bn)}onInit(e){this.serialNumber=l.ToHex(e.userCertificate),this.revocationDate=e.revocationDate.getTime(),this.extensions=[],e.crlEntryExtensions&&(this.extensions=e.crlEntryExtensions.map((e=>{const t=qc.create(lr.serialize(e));switch(t.type){case ki:this.reason=lr.parse(t.value,Ci).reason;break;case Li:this.invalidity=lr.parse(t.value,Hi).value}return t})))}}class gl extends Lc{constructor(e){Lc.isAsnEncoded(e)?super(e,En):super(e),this.tag=Vc.CrlTag}onInit(e){var t,r;const i=e.tbsCertList;this.tbs=lr.serialize(i),this.version=i.version;const n=wa.resolve(xa);this.signatureAlgorithm=n.toWebAlgorithm(e.signatureAlgorithm),this.tbsCertListSignatureAlgorithm=i.signature,this.certListSignatureAlgorithm=e.signatureAlgorithm,this.signature=e.signature,this.issuerName=new gc(i.issuer),this.issuer=this.issuerName.toString();const s=i.thisUpdate.getTime();if(!s)throw new Error("Cannot get 'thisUpdate' value");this.thisUpdate=s;const o=null===(t=i.nextUpdate)||void 0===t?void 0:t.getTime();this.nextUpdate=o,this.entries=(null===(r=i.revokedCertificates)||void 0===r?void 0:r.map((e=>new dl(lr.serialize(e)))))||[],this.extensions=[],i.crlExtensions&&(this.extensions=i.crlExtensions.map((e=>qc.create(lr.serialize(e)))))}getExtension(e){for(const t of this.extensions)if("string"==typeof e){if(t.type===e)return t}else if(t instanceof e)return t;return null}getExtensions(e){return this.extensions.filter((t=>"string"==typeof e?t.type===e:t instanceof e))}async verify(e,t=pc.get()){if(!this.certListSignatureAlgorithm.isEqual(this.tbsCertListSignatureAlgorithm))throw new Error("algorithm identifier in the sequence tbsCertList and CertificateList mismatch");let r,i;const n=e.publicKey;try{n instanceof hl?(r={...n.publicKey.algorithm,...n.signatureAlgorithm},i=await n.publicKey.export(r,["verify"])):n instanceof Hc?(r={...n.algorithm,...this.signature},i=await n.export(r,["verify"])):(r={...n.algorithm,...this.signature},i=n)}catch(e){return!1}const s=wa.resolveAll(Qu).reverse();let o=null;for(const e of s)if(o=e.toWebSignature(r,this.signature),o)break;if(!o)throw Error("Cannot convert ASN.1 signature value to WebCrypto format");return await t.subtle.verify(this.signatureAlgorithm,i,o,this.tbs)}async getThumbprint(...e){let t,r="SHA-1";return e[0]&&(e[0].subtle?t=e[0]:(r=e[0]||r,t=e[1])),null!=t||(t=pc.get()),await t.subtle.digest(r,this.rawData)}findRevoked(e){const t="string"==typeof e?e:e.serialNumber;for(const e of this.entries)if(e.serialNumber===t)return e;return null}}return qc.register(Zr,Mc),qc.register(Oi,Fc),qc.register(Fi,Kc),qc.register(on,zc),qc.register(Gr,$c),qc.register(en,_c),qc.register(gi,Gc),qc.register(_r,Jc),Yu.register(Bu,Xu),Yu.register(ku,Zu),wa.registerSingleton(Qu,el),wa.registerSingleton(Qu,rl),rl.namedCurveSize.set("P-256",32),rl.namedCurveSize.set("K-256",32),rl.namedCurveSize.set("P-384",48),rl.namedCurveSize.set("P-521",66),e.AlgorithmProvider=Sa,e.AsnData=ac,e.AsnDefaultSignatureFormatter=el,e.AsnEcSignatureFormatter=rl,e.Attribute=Ju,e.AttributeFactory=Yu,e.AuthorityInfoAccessExtension=Jc,e.AuthorityKeyIdentifierExtension=$c,e.BasicConstraintsExtension=Mc,e.CRLDistributionPointsExtension=Gc,e.CertificatePolicyExtension=Wc,e.ChallengePasswordAttribute=Xu,e.CryptoProvider=lc,e.DefaultAlgorithmSerializer=nc,e.ExtendedKeyUsageExtension=Fc,e.Extension=cc,e.ExtensionFactory=qc,e.ExtensionsAttribute=Zu,e.GeneralName=Tc,e.GeneralNames=jc,e.KeyUsagesExtension=Kc,e.Name=gc,e.NameIdentifier=fc,e.OidSerializer=sc,e.PemConverter=Vc,e.Pkcs10CertificateRequest=pl,e.Pkcs10CertificateRequestGenerator=class{static async create(e,t=pc.get()){if(!e.keys.privateKey)throw new Error("Bad field 'keys' in 'params' argument. 'privateKey' is empty");if(!e.keys.publicKey)throw new Error("Bad field 'keys' in 'params' argument. 'publicKey' is empty");const r=await t.subtle.exportKey("spki",e.keys.publicKey),i=new ll({certificationRequestInfo:new ul({subjectPKInfo:lr.parse(r,dn)})});if(e.name){const t=e.name instanceof gc?e.name:new gc(e.name);i.certificationRequestInfo.subject=lr.parse(t.toArrayBuffer(),Tr)}if(e.attributes)for(const t of e.attributes)i.certificationRequestInfo.attributes.push(lr.parse(t.rawData,rn));if(e.extensions&&e.extensions.length){const t=new rn({type:ku}),r=new bn;for(const t of e.extensions)r.push(lr.parse(t.rawData,wn));t.values.push(lr.serialize(r)),i.certificationRequestInfo.attributes.push(t)}const n={...e.signingAlgorithm,...e.keys.privateKey.algorithm},s=wa.resolve(xa);i.signatureAlgorithm=s.toAsnAlgorithm(n);const o=lr.serialize(i.certificationRequestInfo),a=await t.subtle.sign(n,e.keys.privateKey,o),c=wa.resolveAll(Qu).reverse();let u=null;for(const e of c)if(u=e.toAsnSignature(n,a),u)break;if(!u)throw Error("Cannot convert WebCrypto signature value to ASN.1 format");return i.signature=u,new pl(lr.serialize(i))}},e.PublicKey=Hc,e.SubjectAlternativeNameExtension=_c,e.SubjectKeyIdentifierExtension=zc,e.TextConverter=oc,e.TextObject=ic,e.X509Certificate=hl,e.X509CertificateGenerator=class{static async createSelfSigned(e,t=pc.get()){if(!e.keys.privateKey)throw new Error("Bad field 'keys' in 'params' argument. 'privateKey' is empty");if(!e.keys.publicKey)throw new Error("Bad field 'keys' in 'params' argument. 'publicKey' is empty");return this.create({serialNumber:e.serialNumber,subject:e.name,issuer:e.name,notBefore:e.notBefore,notAfter:e.notAfter,publicKey:e.keys.publicKey,signingKey:e.keys.privateKey,signingAlgorithm:e.signingAlgorithm,extensions:e.extensions},t)}static async create(e,t=pc.get()){var r;let n;n=e.publicKey instanceof Hc?e.publicKey.rawData:"publicKey"in e.publicKey?e.publicKey.publicKey.rawData:i.isBufferSource(e.publicKey)?e.publicKey:await t.subtle.exportKey("spki",e.publicKey);const s=e.serialNumber?i.toUint8Array(l.FromHex(e.serialNumber)):t.getRandomValues(new Uint8Array(16));s[0]>127&&(s[0]&=127),s.length>1&&0===s[0]&&(s[1]|=128);const o=e.notBefore||new Date,a=e.notAfter||new Date(o.getTime()+31536e6),c=new xn({tbsCertificate:new Sn({version:An.v3,serialNumber:s,validity:new vn({notBefore:o,notAfter:a}),extensions:new bn((null===(r=e.extensions)||void 0===r?void 0:r.map((e=>lr.parse(e.rawData,wn))))||[]),subjectPublicKeyInfo:lr.parse(n,dn)})});if(e.subject){const t=e.subject instanceof gc?e.subject:new gc(e.subject);c.tbsCertificate.subject=lr.parse(t.toArrayBuffer(),Tr)}if(e.issuer){const t=e.issuer instanceof gc?e.issuer:new gc(e.issuer);c.tbsCertificate.issuer=lr.parse(t.toArrayBuffer(),Tr)}const u={hash:"SHA-256"},p="signingKey"in e?{...u,...e.signingAlgorithm,...e.signingKey.algorithm}:{...u,...e.signingAlgorithm},h=wa.resolve(xa);c.tbsCertificate.signature=c.signatureAlgorithm=h.toAsnAlgorithm(p);const f=lr.serialize(c.tbsCertificate),y="signingKey"in e?await t.subtle.sign(p,e.signingKey,f):e.signature,d=wa.resolveAll(Qu).reverse();let g=null;for(const e of d)if(g=e.toAsnSignature(p,y),g)break;if(!g)throw Error("Cannot convert ASN.1 signature value to WebCrypto format");return c.signatureValue=g,new hl(lr.serialize(c))}},e.X509Certificates=fl,e.X509ChainBuilder=class{constructor(e={}){this.certificates=[],e.certificates&&(this.certificates=e.certificates)}async build(e,t=pc.get()){const r=new fl(e);let i=e;for(;i=await this.findIssuer(i,t);){const e=await i.getThumbprint(t);for(const i of r){if(p(e,await i.getThumbprint(t)))throw new Error("Cannot build a certificate chain. Circular dependency.")}r.push(i)}return r}async findIssuer(e,t=pc.get()){if(!await e.isSelfSigned(t)){const r=e.getExtension(Gr);for(const i of this.certificates)if(i.subject===e.issuer){if(r)if(r.keyId){const e=i.getExtension(on);if(e&&e.keyId!==r.keyId)continue}else if(r.certId){const e=i.getExtension(en);if(e&&(r.certId.serialNumber!==i.serialNumber||!p(lr.serialize(r.certId.name),lr.serialize(e))))continue}try{const r={...i.publicKey.algorithm,...e.signatureAlgorithm},n=await i.publicKey.export(r,["verify"],t);if(!await e.verify({publicKey:n,signatureOnly:!0},t))continue}catch(e){continue}return i}}return null}},e.X509Crl=gl,e.X509CrlEntry=dl,e.X509CrlGenerator=class{static async create(e,t=pc.get()){var r;const i=e.issuer instanceof gc?e.issuer:new gc(e.issuer),n=new En({tbsCertList:new kn({version:An.v2,issuer:lr.parse(i.toArrayBuffer(),Tr),thisUpdate:new gn(e.thisUpdate||new Date)})});if(e.nextUpdate&&(n.tbsCertList.nextUpdate=new gn(e.nextUpdate)),e.extensions&&e.extensions.length&&(n.tbsCertList.crlExtensions=new bn(e.extensions.map((e=>lr.parse(e.rawData,wn)))||[])),e.entries&&e.entries.length){n.tbsCertList.revokedCertificates=[];for(const t of e.entries){const i=Lc.toArrayBuffer(t.serialNumber);if(n.tbsCertList.revokedCertificates.findIndex((e=>p(e.userCertificate,i)))>-1)throw new Error(`Certificate serial number ${t.serialNumber} already exists in tbsCertList`);const s=new Bn({userCertificate:i,revocationDate:new gn(t.revocationDate||new Date)});if("extensions"in t&&(null===(r=t.extensions)||void 0===r?void 0:r.length)?s.crlEntryExtensions=t.extensions.map((e=>lr.parse(e.rawData,wn))):s.crlEntryExtensions=[],!(t instanceof dl)&&(t.reason&&s.crlEntryExtensions.push(new wn({extnID:ki,critical:!1,extnValue:new Ct(lr.serialize(new Ci(t.reason)))})),t.invalidity&&s.crlEntryExtensions.push(new wn({extnID:Li,critical:!1,extnValue:new Ct(lr.serialize(new Hi(t.invalidity)))})),t.issuer)){const t=e.issuer instanceof gc?e.issuer:new gc(e.issuer);s.crlEntryExtensions.push(new wn({extnID:ri,critical:!1,extnValue:new Ct(lr.serialize(lr.parse(t.toArrayBuffer(),Tr)))}))}n.tbsCertList.revokedCertificates.push(s)}}const s={...e.signingAlgorithm,...e.signingKey.algorithm},o=wa.resolve(xa);n.tbsCertList.signature=n.signatureAlgorithm=o.toAsnAlgorithm(s);const a=lr.serialize(n.tbsCertList),c=await t.subtle.sign(s,e.signingKey,a),u=wa.resolveAll(Qu).reverse();let l=null;for(const e of u)if(l=e.toAsnSignature(s,c),l)break;if(!l)throw Error("Cannot convert ASN.1 signature value to WebCrypto format");return n.signature=l,new gl(lr.serialize(n))}},e.cryptoProvider=pc,e.diAlgorithm=Aa,e.diAlgorithmProvider=xa,e.diAsnSignatureFormatter=Qu,e.idEd25519=sl,e.idEd448=ol,e.idX25519=il,e.idX448=nl,e}({});
