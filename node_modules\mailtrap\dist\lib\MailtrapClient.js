"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http = __importStar(require("http"));
const https = __importStar(require("https"));
const axios_1 = __importDefault(require("axios"));
const mail_buffer_encoder_1 = __importDefault(require("./mail-buffer-encoder"));
const axios_logger_1 = __importDefault(require("./axios-logger"));
const General_1 = __importDefault(require("./api/General"));
const Testing_1 = __importDefault(require("./api/Testing"));
const config_1 = __importDefault(require("../config"));
const MailtrapError_1 = __importDefault(require("./MailtrapError"));
const { CLIENT_SETTINGS, ERRORS } = config_1.default;
const { SENDING_ENDPOINT, MAX_REDIRECTS, USER_AGENT, TIMEOUT, TESTING_ENDPOINT, BULK_ENDPOINT, } = CLIENT_SETTINGS;
const { TEST_INBOX_ID_MISSING, ACCOUNT_ID_MISSING, BULK_SANDBOX_INCOMPATIBLE } = ERRORS;
/**
 * Mailtrap client class. Initializes instance with available methods.
 */
class MailtrapClient {
    /**
     * Initalizes axios instance with Mailtrap params.
     */
    constructor({ token, testInboxId, accountId, bulk = false, sandbox = false, }) {
        this.axios = axios_1.default.create({
            httpAgent: new http.Agent({ keepAlive: true }),
            httpsAgent: new https.Agent({ keepAlive: true }),
            headers: {
                Authorization: `Bearer ${token}`,
                Connection: "keep-alive",
                "User-Agent": USER_AGENT,
            },
            maxRedirects: MAX_REDIRECTS,
            timeout: TIMEOUT,
        });
        /** Init Axios interceptors for handling response.data, errors. */
        this.axios.interceptors.response.use((response) => response.data, axios_logger_1.default);
        this.testInboxId = testInboxId;
        this.accountId = accountId;
        this.bulk = bulk;
        this.sandbox = sandbox;
    }
    /**
     * Getter for Testing API. Warns if some of the required keys are missing.
     */
    get testing() {
        if (!this.testInboxId) {
            throw new MailtrapError_1.default(TEST_INBOX_ID_MISSING);
        }
        if (!this.accountId) {
            throw new MailtrapError_1.default(ACCOUNT_ID_MISSING);
        }
        return new Testing_1.default(this.axios, this.accountId);
    }
    /**
     * Getter for General API.
     */
    get general() {
        return new General_1.default(this.axios, this.accountId);
    }
    /**
     * Returns configured host. Checks if `bulk` and `sandbox` modes are activated simultaneously,
     *   then reject with Mailtrap Error.
     * Otherwise returns appropriate host url.
     */
    determineHost() {
        let host;
        if (this.bulk && this.sandbox) {
            throw new MailtrapError_1.default(BULK_SANDBOX_INCOMPATIBLE);
        }
        else if (this.sandbox) {
            host = TESTING_ENDPOINT;
        }
        else if (this.bulk) {
            host = BULK_ENDPOINT;
        }
        else {
            host = SENDING_ENDPOINT;
        }
        return host;
    }
    /**
     * Sends mail with given `mail` params. If there is error, rejects with `MailtrapError`.
     */
    async send(mail) {
        const host = this.determineHost();
        const url = `${host}/api/send${this.testInboxId ? `/${this.testInboxId}` : ""}`;
        const preparedMail = (0, mail_buffer_encoder_1.default)(mail);
        return this.axios.post(url, preparedMail);
    }
}
exports.default = MailtrapClient;
