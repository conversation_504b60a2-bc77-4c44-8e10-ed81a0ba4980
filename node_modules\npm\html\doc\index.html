<!doctype html>
<html>
  <title>npm-index</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/index.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="misc/npm-index.html">npm-index</a></h1> <p>Index of all npm documentation</p>
<h3 id="readme-1-"><a href="../doc/README.html">README</a></h3>
<p>a JavaScript package manager</p>
<h2 id="command-line-documentation">Command Line Documentation</h2>
<p>Using npm on the command line</p>
<h3 id="npm-1-"><a href="cli/npm.html">npm(1)</a></h3>
<p>javascript package manager</p>
<h3 id="npm-access-1-"><a href="cli/npm-access.html">npm-access(1)</a></h3>
<p>Set access level on published packages</p>
<h3 id="npm-adduser-1-"><a href="cli/npm-adduser.html">npm-adduser(1)</a></h3>
<p>Add a registry user account</p>
<h3 id="npm-bin-1-"><a href="cli/npm-bin.html">npm-bin(1)</a></h3>
<p>Display npm bin folder</p>
<h3 id="npm-bugs-1-"><a href="cli/npm-bugs.html">npm-bugs(1)</a></h3>
<p>Bugs for a package in a web browser maybe</p>
<h3 id="npm-build-1-"><a href="cli/npm-build.html">npm-build(1)</a></h3>
<p>Build a package</p>
<h3 id="npm-bundle-1-"><a href="cli/npm-bundle.html">npm-bundle(1)</a></h3>
<p>REMOVED</p>
<h3 id="npm-cache-1-"><a href="cli/npm-cache.html">npm-cache(1)</a></h3>
<p>Manipulates packages cache</p>
<h3 id="npm-completion-1-"><a href="cli/npm-completion.html">npm-completion(1)</a></h3>
<p>Tab Completion for npm</p>
<h3 id="npm-config-1-"><a href="cli/npm-config.html">npm-config(1)</a></h3>
<p>Manage the npm configuration files</p>
<h3 id="npm-dedupe-1-"><a href="cli/npm-dedupe.html">npm-dedupe(1)</a></h3>
<p>Reduce duplication</p>
<h3 id="npm-deprecate-1-"><a href="cli/npm-deprecate.html">npm-deprecate(1)</a></h3>
<p>Deprecate a version of a package</p>
<h3 id="npm-dist-tag-1-"><a href="cli/npm-dist-tag.html">npm-dist-tag(1)</a></h3>
<p>Modify package distribution tags</p>
<h3 id="npm-docs-1-"><a href="cli/npm-docs.html">npm-docs(1)</a></h3>
<p>Docs for a package in a web browser maybe</p>
<h3 id="npm-edit-1-"><a href="cli/npm-edit.html">npm-edit(1)</a></h3>
<p>Edit an installed package</p>
<h3 id="npm-explore-1-"><a href="cli/npm-explore.html">npm-explore(1)</a></h3>
<p>Browse an installed package</p>
<h3 id="npm-help-search-1-"><a href="cli/npm-help-search.html">npm-help-search(1)</a></h3>
<p>Search npm help documentation</p>
<h3 id="npm-help-1-"><a href="cli/npm-help.html">npm-help(1)</a></h3>
<p>Get help on npm</p>
<h3 id="npm-init-1-"><a href="cli/npm-init.html">npm-init(1)</a></h3>
<p>Interactively create a package.json file</p>
<h3 id="npm-install-1-"><a href="cli/npm-install.html">npm-install(1)</a></h3>
<p>Install a package</p>
<h3 id="npm-link-1-"><a href="cli/npm-link.html">npm-link(1)</a></h3>
<p>Symlink a package folder</p>
<h3 id="npm-logout-1-"><a href="cli/npm-logout.html">npm-logout(1)</a></h3>
<p>Log out of the registry</p>
<h3 id="npm-ls-1-"><a href="cli/npm-ls.html">npm-ls(1)</a></h3>
<p>List installed packages</p>
<h3 id="npm-outdated-1-"><a href="cli/npm-outdated.html">npm-outdated(1)</a></h3>
<p>Check for outdated packages</p>
<h3 id="npm-owner-1-"><a href="cli/npm-owner.html">npm-owner(1)</a></h3>
<p>Manage package owners</p>
<h3 id="npm-pack-1-"><a href="cli/npm-pack.html">npm-pack(1)</a></h3>
<p>Create a tarball from a package</p>
<h3 id="npm-ping-1-"><a href="cli/npm-ping.html">npm-ping(1)</a></h3>
<p>Ping npm registry</p>
<h3 id="npm-prefix-1-"><a href="cli/npm-prefix.html">npm-prefix(1)</a></h3>
<p>Display prefix</p>
<h3 id="npm-prune-1-"><a href="cli/npm-prune.html">npm-prune(1)</a></h3>
<p>Remove extraneous packages</p>
<h3 id="npm-publish-1-"><a href="cli/npm-publish.html">npm-publish(1)</a></h3>
<p>Publish a package</p>
<h3 id="npm-rebuild-1-"><a href="cli/npm-rebuild.html">npm-rebuild(1)</a></h3>
<p>Rebuild a package</p>
<h3 id="npm-repo-1-"><a href="cli/npm-repo.html">npm-repo(1)</a></h3>
<p>Open package repository page in the browser</p>
<h3 id="npm-restart-1-"><a href="cli/npm-restart.html">npm-restart(1)</a></h3>
<p>Restart a package</p>
<h3 id="npm-rm-1-"><a href="cli/npm-rm.html">npm-rm(1)</a></h3>
<p>Remove a package</p>
<h3 id="npm-root-1-"><a href="cli/npm-root.html">npm-root(1)</a></h3>
<p>Display npm root</p>
<h3 id="npm-run-script-1-"><a href="cli/npm-run-script.html">npm-run-script(1)</a></h3>
<p>Run arbitrary package scripts</p>
<h3 id="npm-search-1-"><a href="cli/npm-search.html">npm-search(1)</a></h3>
<p>Search for packages</p>
<h3 id="npm-shrinkwrap-1-"><a href="cli/npm-shrinkwrap.html">npm-shrinkwrap(1)</a></h3>
<p>Lock down dependency versions</p>
<h3 id="npm-star-1-"><a href="cli/npm-star.html">npm-star(1)</a></h3>
<p>Mark your favorite packages</p>
<h3 id="npm-stars-1-"><a href="cli/npm-stars.html">npm-stars(1)</a></h3>
<p>View packages marked as favorites</p>
<h3 id="npm-start-1-"><a href="cli/npm-start.html">npm-start(1)</a></h3>
<p>Start a package</p>
<h3 id="npm-stop-1-"><a href="cli/npm-stop.html">npm-stop(1)</a></h3>
<p>Stop a package</p>
<h3 id="npm-tag-1-"><a href="cli/npm-tag.html">npm-tag(1)</a></h3>
<p>Tag a published version</p>
<h3 id="npm-team-1-"><a href="cli/npm-team.html">npm-team(1)</a></h3>
<p>Manage organization teams and team memberships</p>
<h3 id="npm-test-1-"><a href="cli/npm-test.html">npm-test(1)</a></h3>
<p>Test a package</p>
<h3 id="npm-uninstall-1-"><a href="cli/npm-uninstall.html">npm-uninstall(1)</a></h3>
<p>Remove a package</p>
<h3 id="npm-unpublish-1-"><a href="cli/npm-unpublish.html">npm-unpublish(1)</a></h3>
<p>Remove a package from the registry</p>
<h3 id="npm-update-1-"><a href="cli/npm-update.html">npm-update(1)</a></h3>
<p>Update a package</p>
<h3 id="npm-version-1-"><a href="cli/npm-version.html">npm-version(1)</a></h3>
<p>Bump a package version</p>
<h3 id="npm-view-1-"><a href="cli/npm-view.html">npm-view(1)</a></h3>
<p>View registry info</p>
<h3 id="npm-whoami-1-"><a href="cli/npm-whoami.html">npm-whoami(1)</a></h3>
<p>Display npm username</p>
<h2 id="api-documentation">API Documentation</h2>
<p>Using npm in your Node programs</p>
<h3 id="npm-3-"><a href="api/npm.html">npm(3)</a></h3>
<p>javascript package manager</p>
<h3 id="npm-bin-3-"><a href="api/npm-bin.html">npm-bin(3)</a></h3>
<p>Display npm bin folder</p>
<h3 id="npm-bugs-3-"><a href="api/npm-bugs.html">npm-bugs(3)</a></h3>
<p>Bugs for a package in a web browser maybe</p>
<h3 id="npm-cache-3-"><a href="api/npm-cache.html">npm-cache(3)</a></h3>
<p>manage the npm cache programmatically</p>
<h3 id="npm-commands-3-"><a href="api/npm-commands.html">npm-commands(3)</a></h3>
<p>npm commands</p>
<h3 id="npm-config-3-"><a href="api/npm-config.html">npm-config(3)</a></h3>
<p>Manage the npm configuration files</p>
<h3 id="npm-deprecate-3-"><a href="api/npm-deprecate.html">npm-deprecate(3)</a></h3>
<p>Deprecate a version of a package</p>
<h3 id="npm-docs-3-"><a href="api/npm-docs.html">npm-docs(3)</a></h3>
<p>Docs for a package in a web browser maybe</p>
<h3 id="npm-edit-3-"><a href="api/npm-edit.html">npm-edit(3)</a></h3>
<p>Edit an installed package</p>
<h3 id="npm-explore-3-"><a href="api/npm-explore.html">npm-explore(3)</a></h3>
<p>Browse an installed package</p>
<h3 id="npm-help-search-3-"><a href="api/npm-help-search.html">npm-help-search(3)</a></h3>
<p>Search the help pages</p>
<h3 id="npm-init-3-"><a href="api/npm-init.html">npm-init(3)</a></h3>
<p>Interactively create a package.json file</p>
<h3 id="npm-install-3-"><a href="api/npm-install.html">npm-install(3)</a></h3>
<p>install a package programmatically</p>
<h3 id="npm-link-3-"><a href="api/npm-link.html">npm-link(3)</a></h3>
<p>Symlink a package folder</p>
<h3 id="npm-load-3-"><a href="api/npm-load.html">npm-load(3)</a></h3>
<p>Load config settings</p>
<h3 id="npm-ls-3-"><a href="api/npm-ls.html">npm-ls(3)</a></h3>
<p>List installed packages</p>
<h3 id="npm-outdated-3-"><a href="api/npm-outdated.html">npm-outdated(3)</a></h3>
<p>Check for outdated packages</p>
<h3 id="npm-owner-3-"><a href="api/npm-owner.html">npm-owner(3)</a></h3>
<p>Manage package owners</p>
<h3 id="npm-pack-3-"><a href="api/npm-pack.html">npm-pack(3)</a></h3>
<p>Create a tarball from a package</p>
<h3 id="npm-ping-3-"><a href="api/npm-ping.html">npm-ping(3)</a></h3>
<p>Ping npm registry</p>
<h3 id="npm-prefix-3-"><a href="api/npm-prefix.html">npm-prefix(3)</a></h3>
<p>Display prefix</p>
<h3 id="npm-prune-3-"><a href="api/npm-prune.html">npm-prune(3)</a></h3>
<p>Remove extraneous packages</p>
<h3 id="npm-publish-3-"><a href="api/npm-publish.html">npm-publish(3)</a></h3>
<p>Publish a package</p>
<h3 id="npm-rebuild-3-"><a href="api/npm-rebuild.html">npm-rebuild(3)</a></h3>
<p>Rebuild a package</p>
<h3 id="npm-repo-3-"><a href="api/npm-repo.html">npm-repo(3)</a></h3>
<p>Open package repository page in the browser</p>
<h3 id="npm-restart-3-"><a href="api/npm-restart.html">npm-restart(3)</a></h3>
<p>Restart a package</p>
<h3 id="npm-root-3-"><a href="api/npm-root.html">npm-root(3)</a></h3>
<p>Display npm root</p>
<h3 id="npm-run-script-3-"><a href="api/npm-run-script.html">npm-run-script(3)</a></h3>
<p>Run arbitrary package scripts</p>
<h3 id="npm-search-3-"><a href="api/npm-search.html">npm-search(3)</a></h3>
<p>Search for packages</p>
<h3 id="npm-shrinkwrap-3-"><a href="api/npm-shrinkwrap.html">npm-shrinkwrap(3)</a></h3>
<p>programmatically generate package shrinkwrap file</p>
<h3 id="npm-start-3-"><a href="api/npm-start.html">npm-start(3)</a></h3>
<p>Start a package</p>
<h3 id="npm-stop-3-"><a href="api/npm-stop.html">npm-stop(3)</a></h3>
<p>Stop a package</p>
<h3 id="npm-tag-3-"><a href="api/npm-tag.html">npm-tag(3)</a></h3>
<p>Tag a published version</p>
<h3 id="npm-test-3-"><a href="api/npm-test.html">npm-test(3)</a></h3>
<p>Test a package</p>
<h3 id="npm-uninstall-3-"><a href="api/npm-uninstall.html">npm-uninstall(3)</a></h3>
<p>uninstall a package programmatically</p>
<h3 id="npm-unpublish-3-"><a href="api/npm-unpublish.html">npm-unpublish(3)</a></h3>
<p>Remove a package from the registry</p>
<h3 id="npm-update-3-"><a href="api/npm-update.html">npm-update(3)</a></h3>
<p>Update a package</p>
<h3 id="npm-version-3-"><a href="api/npm-version.html">npm-version(3)</a></h3>
<p>Bump a package version</p>
<h3 id="npm-view-3-"><a href="api/npm-view.html">npm-view(3)</a></h3>
<p>View registry info</p>
<h3 id="npm-whoami-3-"><a href="api/npm-whoami.html">npm-whoami(3)</a></h3>
<p>Display npm username</p>
<h2 id="files">Files</h2>
<p>File system structures npm uses</p>
<h3 id="npm-folders-5-"><a href="files/npm-folders.html">npm-folders(5)</a></h3>
<p>Folder Structures Used by npm</p>
<h3 id="npmrc-5-"><a href="files/npmrc.html">npmrc(5)</a></h3>
<p>The npm config files</p>
<h3 id="package-json-5-"><a href="files/package.json.html">package.json(5)</a></h3>
<p>Specifics of npm&#39;s package.json handling</p>
<h2 id="misc">Misc</h2>
<p>Various other bits and bobs</p>
<h3 id="npm-coding-style-7-"><a href="misc/npm-coding-style.html">npm-coding-style(7)</a></h3>
<p>npm&#39;s &quot;funny&quot; coding style</p>
<h3 id="npm-config-7-"><a href="misc/npm-config.html">npm-config(7)</a></h3>
<p>More than you probably want to know about npm configuration</p>
<h3 id="npm-developers-7-"><a href="misc/npm-developers.html">npm-developers(7)</a></h3>
<p>Developer Guide</p>
<h3 id="npm-disputes-7-"><a href="misc/npm-disputes.html">npm-disputes(7)</a></h3>
<p>Handling Module Name Disputes</p>
<h3 id="npm-index-7-"><a href="misc/npm-index.html">npm-index(7)</a></h3>
<p>Index of all npm documentation</p>
<h3 id="npm-orgs-7-"><a href="misc/npm-orgs.html">npm-orgs(7)</a></h3>
<p>Working with Teams &amp; Orgs</p>
<h3 id="npm-registry-7-"><a href="misc/npm-registry.html">npm-registry(7)</a></h3>
<p>The JavaScript Package Registry</p>
<h3 id="npm-scope-7-"><a href="misc/npm-scope.html">npm-scope(7)</a></h3>
<p>Scoped packages</p>
<h3 id="npm-scripts-7-"><a href="misc/npm-scripts.html">npm-scripts(7)</a></h3>
<p>How npm handles the &quot;scripts&quot; field</p>
<h3 id="removing-npm-7-"><a href="misc/removing-npm.html">removing-npm(7)</a></h3>
<p>Cleaning the Slate</p>
<h3 id="semver-7-"><a href="misc/semver.html">semver(7)</a></h3>
<p>The semantic versioner for npm</p>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-index &mdash; npm@2.15.12</p>

