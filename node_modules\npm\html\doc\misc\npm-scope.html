<!doctype html>
<html>
  <title>npm-scope</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/misc/npm-scope.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../misc/npm-scope.html">npm-scope</a></h1> <p>Scoped packages</p>
<h2 id="description">DESCRIPTION</h2>
<p>All npm packages have a name. Some package names also have a scope. A scope
follows the usual rules for package names (url-safe characters, no leading dots
or underscores). When used in package names, preceded by an @-symbol and
followed by a slash, e.g.</p>
<pre><code>@somescope/somepackagename
</code></pre><p>Scopes are a way of grouping related packages together, and also affect a few
things about the way npm treats the package.</p>
<p>Scoped packages can be published and installed as of <code>npm@2</code> and are supported
by the primary npm registry. The npm client is backwards-compatible with
un-scoped registries, so it can be used to work with scoped and un-scoped
registries at the same time.</p>
<h2 id="installing-scoped-packages">Installing scoped packages</h2>
<p>Scoped packages are installed to a sub-folder of the regular installation
folder, e.g. if your other packages are installed in <code>node_modules/packagename</code>,
scoped modules will be in <code>node_modules/@myorg/packagename</code>. The scope folder
(<code>@myorg</code>) is simply the name of the scope preceded by an @-symbol, and can
contain any number of scoped packages.</p>
<p>A scoped package is installed by referencing it by name, preceded by an
@-symbol, in <code>npm install</code>:</p>
<pre><code>npm install @myorg/mypackage
</code></pre><p>Or in <code>package.json</code>:</p>
<pre><code>&quot;dependencies&quot;: {
  &quot;@myorg/mypackage&quot;: &quot;^1.3.0&quot;
}
</code></pre><p>Note that if the @-symbol is omitted in either case npm will instead attempt to
install from GitHub; see <code><a href="../cli/npm-install.html">npm-install(1)</a></code>.</p>
<h2 id="requiring-scoped-packages">Requiring scoped packages</h2>
<p>Because scoped packages are installed into a scope folder, you have to
include the name of the scope when requiring them in your code, e.g.</p>
<pre><code>require(&#39;@myorg/mypackage&#39;)
</code></pre><p>There is nothing special about the way Node treats scope folders, this is
just specifying to require the module <code>mypackage</code> in the folder called <code>@myorg</code>.</p>
<h2 id="publishing-scoped-packages">Publishing scoped packages</h2>
<p>Scoped packages can be published from the CLI as of <code>npm@2</code> and can be
published to any registry that supports them, including the primary npm
registry.</p>
<p>(As of 2015-04-19, and with npm 2.0 or newer, the primary npm registry <strong>does</strong>
support scoped packages)</p>
<p>If you wish, you may associate a scope with a registry; see below.</p>
<h3 id="publishing-public-scoped-packages-to-the-primary-npm-registry">Publishing public scoped packages to the primary npm registry</h3>
<p>To publish a public scoped package, you must specify <code>--access public</code> with
the initial publication.  This will publish the package and set access
to <code>public</code> as if you had run <code>npm access public</code> after publishing.</p>
<h3 id="publishing-private-scoped-packages-to-the-npm-registry">Publishing private scoped packages to the npm registry</h3>
<p>To publish a private scoped package to the npm registry, you must have
an <a href="https://www.npmjs.com/private-modules">npm Private Modules</a>
account.</p>
<p>You can then publish the module with <code>npm publish</code> or <code>npm publish
--access restricted</code>, and it will be present in the npm registry, with
restricted access.  You can then change the access permissions, if
desired, with <code>npm access</code> or on the npmjs.com website.</p>
<h2 id="associating-a-scope-with-a-registry">Associating a scope with a registry</h2>
<p>Scopes can be associated with a separate registry. This allows you to
seamlessly use a mix of packages from the primary npm registry and one or more
private registries, such as npm Enterprise.</p>
<p>You can associate a scope with a registry at login, e.g.</p>
<pre><code>npm login --registry=http://reg.example.com --scope=@myco
</code></pre><p>Scopes have a many-to-one relationship with registries: one registry can
host multiple scopes, but a scope only ever points to one registry.</p>
<p>You can also associate a scope with a registry using <code>npm config</code>:</p>
<pre><code>npm config set @myco:registry http://reg.example.com
</code></pre><p>Once a scope is associated with a registry, any <code>npm install</code> for a package
with that scope will request packages from that registry instead. Any
<code>npm publish</code> for a package name that contains the scope will be published to
that registry instead.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-install.html">npm-install(1)</a></li>
<li><a href="../cli/npm-publish.html">npm-publish(1)</a></li>
<li><a href="../cli/npm-access.html">npm-access(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-scope &mdash; npm@2.15.12</p>

