<!doctype html>
<html>
  <title>npm-owner</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/api/npm-owner.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../api/npm-owner.html">npm-owner</a></h1> <p>Manage package owners</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm.commands.owner(args, callback)
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>The first element of the &#39;args&#39; parameter defines what to do, and the subsequent
elements depend on the action. Possible values for the action are (order of
parameters are given in parenthesis):</p>
<ul>
<li>ls (package):
List all the users who have access to modify a package and push new versions.
Handy when you need to know who to bug for help.</li>
<li>add (user, package):
Add a new user as a maintainer of a package.  This user is enabled to modify
metadata, publish new versions, and add other owners.</li>
<li>rm (user, package):
Remove a user from the package owner list.  This immediately revokes their
privileges.</li>
</ul>
<p>Note that there is only one level of access.  Either you can modify a package,
or you can&#39;t.  Future versions may contain more fine-grained access levels, but
that is not implemented at this time.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../api/npm-publish.html">npm-publish(3)</a></li>
<li><a href="../misc/npm-registry.html">npm-registry(7)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-owner &mdash; npm@2.15.12</p>

