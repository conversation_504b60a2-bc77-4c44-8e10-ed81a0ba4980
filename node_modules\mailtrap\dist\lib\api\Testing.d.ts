import { AxiosInstance } from "axios";
import ProjectsApi from "./resources/Projects";
import InboxesApi from "./resources/Inboxes";
import MessagesApi from "./resources/Messages";
import AttachmentsApi from "./resources/Attachments";
export default class TestingAPI {
    private client;
    private accountId?;
    projects: ProjectsApi;
    inboxes: InboxesApi;
    messages: MessagesApi;
    attachments: AttachmentsApi;
    constructor(client: AxiosInstance, accountId?: number);
}
