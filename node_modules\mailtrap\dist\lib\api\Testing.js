"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Projects_1 = __importDefault(require("./resources/Projects"));
const Inboxes_1 = __importDefault(require("./resources/Inboxes"));
const Messages_1 = __importDefault(require("./resources/Messages"));
const Attachments_1 = __importDefault(require("./resources/Attachments"));
class TestingAPI {
    constructor(client, accountId) {
        this.client = client;
        this.accountId = accountId;
        this.projects = new Projects_1.default(this.client, this.accountId);
        this.inboxes = new Inboxes_1.default(this.client, this.accountId);
        this.messages = new Messages_1.default(this.client, this.accountId);
        this.attachments = new Attachments_1.default(this.client, this.accountId);
    }
}
exports.default = TestingAPI;
