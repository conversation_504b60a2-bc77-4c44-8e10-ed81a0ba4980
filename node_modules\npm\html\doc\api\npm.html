<!doctype html>
<html>
  <title>npm</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/api/npm.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../api/npm.html">npm</a></h1> <p>javascript package manager</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>var npm = require(&quot;npm&quot;)
npm.load([configObject, ]function (er, npm) {
  // use the npm object, now that it&#39;s loaded.

  npm.config.set(key, val)
  val = npm.config.get(key)

  console.log(&quot;prefix = %s&quot;, npm.prefix)

  npm.commands.install([&quot;package&quot;], cb)
})
</code></pre><h2 id="version">VERSION</h2>
<p>2.15.12</p>
<h2 id="description">DESCRIPTION</h2>
<p>This is the API documentation for npm.
To find documentation of the command line
client, see <code><a href="../cli/npm.html">npm(1)</a></code>.</p>
<p>Prior to using npm&#39;s commands, <code>npm.load()</code> must be called.  If you provide
<code>configObject</code> as an object map of top-level configs, they override the values
stored in the various config locations. In the npm command line client, this
set of configs is parsed from the command line options. Additional
configuration params are loaded from two configuration files. See
<code><a href="../cli/npm-config.html">npm-config(1)</a></code>, <code><a href="../misc/npm-config.html">npm-config(7)</a></code>, and <code><a href="../files/npmrc.html">npmrc(5)</a></code> for more information.</p>
<p>After that, each of the functions are accessible in the
commands object: <code>npm.commands.&lt;cmd&gt;</code>.  See <code><a href="../misc/npm-index.html">npm-index(7)</a></code> for a list of
all possible commands.</p>
<p>All commands on the command object take an <strong>array</strong> of positional argument
<strong>strings</strong>. The last argument to any function is a callback. Some
commands take other optional arguments.</p>
<p>Configs cannot currently be set on a per function basis, as each call to
npm.config.set will change the value for <em>all</em> npm commands in that process.</p>
<p>To find API documentation for a specific command, run the <code>npm apihelp</code>
command.</p>
<h2 id="methods-and-properties">METHODS AND PROPERTIES</h2>
<ul>
<li><p><code>npm.load(configs, cb)</code></p>
<p>  Load the configuration params, and call the <code>cb</code> function once the
  globalconfig and userconfig files have been loaded as well, or on
  nextTick if they&#39;ve already been loaded.</p>
</li>
<li><p><code>npm.config</code></p>
<p>  An object for accessing npm configuration parameters.</p>
<ul>
<li><code>npm.config.get(key)</code></li>
<li><code>npm.config.set(key, val)</code></li>
<li><code>npm.config.del(key)</code></li>
</ul>
</li>
<li><p><code>npm.dir</code> or <code>npm.root</code></p>
<p>  The <code>node_modules</code> directory where npm will operate.</p>
</li>
<li><p><code>npm.prefix</code></p>
<p>  The prefix where npm is operating.  (Most often the current working
  directory.)</p>
</li>
<li><p><code>npm.cache</code></p>
<p>  The place where npm keeps JSON and tarballs it fetches from the
  registry (or uploads to the registry).</p>
</li>
<li><p><code>npm.tmp</code></p>
<p>  npm&#39;s temporary working directory.</p>
</li>
<li><p><code>npm.deref</code></p>
<p>  Get the &quot;real&quot; name for a command that has either an alias or
  abbreviation.</p>
</li>
</ul>
<h2 id="magic">MAGIC</h2>
<p>For each of the methods in the <code>npm.commands</code> object, a method is added to the
npm object, which takes a set of positional string arguments rather than an
array and a callback.</p>
<p>If the last argument is a callback, then it will use the supplied
callback.  However, if no callback is provided, then it will print out
the error or results.</p>
<p>For example, this would work in a node repl:</p>
<pre><code>&gt; npm = require(&quot;npm&quot;)
&gt; npm.load()  // wait a sec...
&gt; npm.install(&quot;dnode&quot;, &quot;express&quot;)
</code></pre><p>Note that that <em>won&#39;t</em> work in a node program, since the <code>install</code>
method will get called before the configuration load is completed.</p>
<h2 id="abbrevs">ABBREVS</h2>
<p>In order to support <code>npm ins foo</code> instead of <code>npm install foo</code>, the
<code>npm.commands</code> object has a set of abbreviations as well as the full
method names.  Use the <code>npm.deref</code> method to find the real name.</p>
<p>For example:</p>
<pre><code>var cmd = npm.deref(&quot;unp&quot;) // cmd === &quot;unpublish&quot;
</code></pre>
</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm &mdash; npm@2.15.12</p>

