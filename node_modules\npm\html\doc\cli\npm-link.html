<!doctype html>
<html>
  <title>npm-link</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-link.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-link.html">npm-link</a></h1> <p>Symlink a package folder</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm link (in package folder)
npm link [@&lt;scope&gt;/]&lt;pkgname&gt;
npm ln (with any of the previous argument usage)
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>Package linking is a two-step process.</p>
<p>First, <code>npm link</code> in a package folder will create a symlink in the global folder
<code>{prefix}/lib/node_modules/&lt;package&gt;</code> that links to the package where the <code>npm
link</code> command was executed. (see <code><a href="../misc/npm-config.html">npm-config(7)</a></code> for the value of <code>prefix</code>). It
will also link any bins in the package to <code>{prefix}/bin/{name}</code>.</p>
<p>Next, in some other location, <code>npm link package-name</code> will create a
symbolic link from globally-installed <code>package-name</code> to <code>node_modules/</code>
of the current folder.</p>
<p>Note that <code>package-name</code> is taken from <code>package.json</code>,
not from directory name.</p>
<p>The package name can be optionally prefixed with a scope. See <code><a href="../misc/npm-scope.html">npm-scope(7)</a></code>.
The scope must be preceded by an @-symbol and followed by a slash.</p>
<p>When creating tarballs for <code>npm publish</code>, the linked packages are
&quot;snapshotted&quot; to their current state by resolving the symbolic links.</p>
<p>This is handy for installing your own stuff, so that you can work on it and
test it iteratively without having to continually rebuild.</p>
<p>For example:</p>
<pre><code>cd ~/projects/node-redis    # go into the package directory
npm link                    # creates global link
cd ~/projects/node-bloggy   # go into some other package directory.
npm link redis              # link-install the package
</code></pre><p>Now, any changes to ~/projects/node-redis will be reflected in
~/projects/node-bloggy/node_modules/node-redis/. Note that the link should
be to the package name, not the directory name for that package.</p>
<p>You may also shortcut the two steps in one.  For example, to do the
above use-case in a shorter way:</p>
<pre><code>cd ~/projects/node-bloggy  # go into the dir of your main project
npm link ../node-redis     # link the dir of your dependency
</code></pre><p>The second line is the equivalent of doing:</p>
<pre><code>(cd ../node-redis; npm link)
npm link node-redis
</code></pre><p>That is, it first creates a global link, and then links the global
installation target into your project&#39;s <code>node_modules</code> folder.</p>
<p>If your linked package is scoped (see <code><a href="../misc/npm-scope.html">npm-scope(7)</a></code>) your link command must
include that scope, e.g.</p>
<pre><code>npm link @myorg/privatepackage
</code></pre><h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../misc/npm-developers.html">npm-developers(7)</a></li>
<li><a href="../misc/npm-faq.html">npm-faq(7)</a></li>
<li><a href="../files/package.json.html">package.json(5)</a></li>
<li><a href="../cli/npm-install.html">npm-install(1)</a></li>
<li><a href="../files/npm-folders.html">npm-folders(5)</a></li>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../files/npmrc.html">npmrc(5)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-link &mdash; npm@2.15.12</p>

