// prettier-ignore
module.exports = {
  'Android': [
  'AND', 'CYN', 'FIR', 'REM', 'RZD', 'MLD', 'MCD', 'YNS', 'GRI', 'HAR',
  'ADR', '<PERSON><PERSON>', 'B<PERSON>', 'REV', '<PERSON><PERSON>', 'S<PERSON>', 'R<PERSON>', 'WER', 'P<PERSON>', 'ARM',
  'HEL', 'BYI', 'RIS', 'PUF', 'LEA', 'MET',
  ],
  'AmigaOS': ['AMG', 'MOR', 'ARO'],
  'BlackB<PERSON>': ['BLB', 'QNX'],
  'Brew': ['BMP'],
  'BeOS': ['BEO', 'HAI'],
  'Chrome OS': ['COS', 'CRS', 'FYD', 'SEE'],
  'Firefox OS': ['FOS', 'KOS'],
  'Gaming Console': ['WII', 'PS3'],
  'Google TV': ['GTV'],
  'IBM': ['OS2'],
  'iOS': ['IOS', 'ATV', 'WAS', 'IPA'],
  'RISC OS': ['ROS'],
  'GNU/Linux': [
  'LIN', 'ARL', 'DEB', 'KNO', 'MIN', 'UBT', 'KBT', 'XBT', 'LBT', 'FED',
  'RHT', 'VLN', 'MDR', 'GNT', 'SAB', 'SLW', 'SSE', 'CES', 'BTR', 'SAF',
  'ORD', 'TOS', 'RSO', 'DEE', 'FRE', 'MAG', 'FEN', 'CAI', 'PCL', 'HAS',
  'LOS', 'DVK', 'ROK', 'OWR', 'OTV', 'KTV', 'PUR', 'PLA', 'FUC', 'PAR',
  'FOR', 'MON', 'KAN', 'ZEN', 'LND', 'LNS', 'CHN', 'AMZ', 'TEN', 'CST',
  'NOV', 'ROU', 'ZOR', 'RED', 'KAL', 'ORA', 'VID', 'TIV', 'BSN', 'RAS',
  'UOS', 'PIO', 'FRI', 'LIR', 'WEB', 'SER', 'ASP', 'AOS', 'LOO', 'EUL',
  'SCI', 'ALP', 'CLO', 'ROC', 'OVZ', 'PVE', 'RST', 'EZX', 'GNS', 'JOL',
  'TUR', 'QTP', 'WPO', 'PAN', 'VIZ', 'AZU', 'COL',
  ],
  'Mac': ['MAC'],
  'Mobile Gaming Console': ['PSP', 'NDS', 'XBX'],
  'OpenVMS': ['OVS'],
  'Real-time OS': ['MTK', 'TDX', 'MRE', 'JME', 'REX', 'RXT'],
  'Other Mobile': ['WOS', 'POS', 'SBA', 'TIZ', 'SMG', 'MAE', 'LUN', 'GEO'],
  'Symbian': ['SYM', 'SYS', 'SY3', 'S60', 'S40'],
  'Unix': [
  'SOS', 'AIX', 'HPX', 'BSD', 'NBS', 'OBS', 'DFB', 'SYL', 'IRI', 'T64',
  'INF', 'ELE', 'GNX', 'ULT', 'NWS', 'NXT', 'SBL',
  ],
  'WebTV': ['WTV'],
  'Windows': ['WIN'],
  'Windows Mobile': ['WPH', 'WMO', 'WCE', 'WRT', 'WIO', 'KIN'],
  'Other Smart TV': ['WHS'],
  
};
