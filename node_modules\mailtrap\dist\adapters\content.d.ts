/// <reference types="node" />
/// <reference types="node" />
import { Readable } from "node:stream";
import { AttachmentLike } from "nodemailer/lib/mailer";
/**
 * Checks if content type is rather string or buffer, returns content.
 * If content is Readble stream, then calls .read().
 * If content has recursive content property then calls the same function recursively.
 * Otherwise reads file.
 */
export default function adaptContent(content: string | Buffer | Readable | AttachmentLike): string | Buffer;
