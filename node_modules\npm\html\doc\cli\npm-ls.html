<!doctype html>
<html>
  <title>npm-ls</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-ls.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-ls.html">npm-ls</a></h1> <p>List installed packages</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm list [[@&lt;scope&gt;/]&lt;pkg&gt; ...]
npm ls [[@&lt;scope&gt;/]&lt;pkg&gt; ...]
npm la [[@&lt;scope&gt;/]&lt;pkg&gt; ...]
npm ll [[@&lt;scope&gt;/]&lt;pkg&gt; ...]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>This command will print to stdout all the versions of packages that are
installed, as well as their dependencies, in a tree-structure.</p>
<p>Positional arguments are <code>name@version-range</code> identifiers, which will
limit the results to only the paths to the packages named.  Note that
nested packages will <em>also</em> show the paths to the specified packages.
For example, running <code>npm ls promzard</code> in npm&#39;s source tree will show:</p>
<pre><code>npm@2.15.12 /path/to/npm
└─┬ init-package-json@0.0.4
  └── promzard@0.1.5
</code></pre><p>It will print out extraneous, missing, and invalid packages.</p>
<p>If a project specifies git urls for dependencies these are shown
in parentheses after the name@version to make it easier for users to
recognize potential forks of a project.</p>
<p>When run as <code>ll</code> or <code>la</code>, it shows extended information by default.</p>
<h2 id="configuration">CONFIGURATION</h2>
<h3 id="json">json</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Show information in JSON format.</p>
<h3 id="long">long</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Show extended information.</p>
<h3 id="parseable">parseable</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Show parseable output instead of tree view.</p>
<h3 id="global">global</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>List packages in the global install prefix instead of in the current
project.</p>
<h3 id="depth">depth</h3>
<ul>
<li>Type: Int</li>
</ul>
<p>Max display depth of the dependency tree.</p>
<h3 id="prod-production">prod / production</h3>
<ul>
<li>Type: Boolean</li>
<li>Default: false</li>
</ul>
<p>Display only the dependency tree for packages in <code>dependencies</code>.</p>
<h3 id="dev">dev</h3>
<ul>
<li>Type: Boolean</li>
<li>Default: false</li>
</ul>
<p>Display only the dependency tree for packages in <code>devDependencies</code>.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../files/npmrc.html">npmrc(5)</a></li>
<li><a href="../files/npm-folders.html">npm-folders(5)</a></li>
<li><a href="../cli/npm-install.html">npm-install(1)</a></li>
<li><a href="../cli/npm-link.html">npm-link(1)</a></li>
<li><a href="../cli/npm-prune.html">npm-prune(1)</a></li>
<li><a href="../cli/npm-outdated.html">npm-outdated(1)</a></li>
<li><a href="../cli/npm-update.html">npm-update(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-ls &mdash; npm@2.15.12</p>

