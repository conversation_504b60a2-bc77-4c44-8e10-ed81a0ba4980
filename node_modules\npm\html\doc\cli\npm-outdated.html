<!doctype html>
<html>
  <title>npm-outdated</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-outdated.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-outdated.html">npm-outdated</a></h1> <p>Check for outdated packages</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm outdated [&lt;name&gt; [&lt;name&gt; ...]]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>This command will check the registry to see if any (or, specific) installed
packages are currently outdated.</p>
<p>In the output:</p>
<ul>
<li><code>wanted</code> is the maximum version of the package that satisfies the semver
range specified in <code>package.json</code>. If there&#39;s no available semver range (i.e.
you&#39;re running <code>npm outdated --global</code>, or the package isn&#39;t included in
<code>package.json</code>), then <code>wanted</code> shows the currently-installed version.</li>
<li><code>latest</code> is the version of the package tagged as latest in the registry.
Running <code>npm publish</code> with no special configuration will publish the package
with a dist-tag of <code>latest</code>. This may or may not be the maximum version of
the package, or the most-recently published version of the package, depending
on how the package&#39;s developer manages the latest <a href="../cli/dist-tag.html">dist-tag(1)</a>.</li>
<li><code>location</code> is where in the dependency tree the package is located. Note that
<code>npm outdated</code> defaults to a depth of 0, so unless you override that, you&#39;ll
always be seeing only top-level dependencies that are outdated.</li>
<li><code>package type</code> (when using <code>--long</code> / <code>-l</code>) tells you whether this package is
a <code>dependency</code> or a <code>devDependency</code>. Packages not included in <code>package.json</code>
are always marked <code>dependencies</code>.</li>
</ul>
<h3 id="an-example">An example</h3>
<pre><code>$ npm outdated
Package      Current   Wanted   Latest  Location
glob          5.0.15   5.0.15    6.0.1  test-outdated-output
nothingness    0.0.3      git      git  test-outdated-output
npm            3.5.1    3.5.2    3.5.1  test-outdated-output
local-dev      0.0.3   linked   linked  test-outdated-output
once           1.3.2    1.3.3    1.3.3  test-outdated-output
</code></pre><p>With these <code>dependencies</code>:</p>
<pre><code class="lang-json">{
  &quot;glob&quot;: &quot;^5.0.15&quot;,
  &quot;nothingness&quot;: &quot;github:othiym23/nothingness#master&quot;,
  &quot;npm&quot;: &quot;^3.5.1&quot;,
  &quot;once&quot;: &quot;^1.3.1&quot;
}
</code></pre>
<p>A few things to note:</p>
<ul>
<li><code>glob</code> requires <code>^5</code>, which prevents npm from installing <code>glob@6</code>, which is
outside the semver range.</li>
<li>Git dependencies will always be reinstalled, because of how they&#39;re specified.
The installed committish might satisfy the dependency specifier (if it&#39;s
something immutable, like a commit SHA), or it might not, so <code>npm outdated</code> and
<code>npm update</code> have to fetch Git repos to check. This is why currently doing a
reinstall of a Git dependency always forces a new clone and install.</li>
<li><code>npm@3.5.2</code> is marked as &quot;wanted&quot;, but &quot;latest&quot; is <code>npm@3.5.1</code> because npm
uses dist-tags to manage its <code>latest</code> and <code>next</code> release channels. <code>npm update</code>
will install the <em>newest</em> version, but <code>npm install npm</code> (with no semver range)
will install whatever&#39;s tagged as <code>latest</code>.</li>
<li><code>once</code> is just plain out of date. Reinstalling <code>node_modules</code> from scratch or
running <code>npm update</code> will bring it up to spec.</li>
</ul>
<h2 id="configuration">CONFIGURATION</h2>
<h3 id="json">json</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Show information in JSON format.</p>
<h3 id="long">long</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Show extended information.</p>
<h3 id="parseable">parseable</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Show parseable output instead of tree view.</p>
<h3 id="global">global</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Check packages in the global install prefix instead of in the current
project.</p>
<h3 id="depth">depth</h3>
<ul>
<li>Default: 0</li>
<li>Type: Int</li>
</ul>
<p>Max depth for checking dependency tree.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-update.html">npm-update(1)</a></li>
<li><a href="../cli/npm-dist-tag.html">npm-dist-tag(1)</a></li>
<li><a href="../misc/npm-registry.html">npm-registry(7)</a></li>
<li><a href="../files/npm-folders.html">npm-folders(5)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-outdated &mdash; npm@2.15.12</p>

