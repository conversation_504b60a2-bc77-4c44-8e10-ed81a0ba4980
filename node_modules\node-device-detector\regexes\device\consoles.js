module.exports = {
  "Archos": {
    "regex": "Archos.*GAMEPAD([2]?)",
    "device": "console",
    "model": "Gamepad $1"
  },
  "Microsoft": {
    "regex": "Xbox",
    "device": "console",
    "models": [
      {
        "regex": "Xbox Series X",
        "model": "Xbox Series X"
      },
      {
        "regex": "Xbox One X",
        "model": "Xbox One X"
      },
      {
        "regex": "Xbox One",
        "model": "Xbox One"
      },
      {
        "regex": "XBOX_ONE_ED",
        "model": "Xbox One S"
      },
      {
        "regex": "Xbox",
        "model": "Xbox 360"
      }
    ]
  },
  "Nintendo": {
    "regex": "Nintendo (([3]?DS[i]?)|Wii[U]?|Switch|GameBoy)",
    "device": "console",
    "model": "$1"
  },
  "OUYA": {
    "regex": "OUYA",
    "device": "console",
    "model": "OUYA"
  },
  "Sanyo": {
    "regex": "Aplix_SANYO",
    "device": "console",
    "model": "3DO TRY"
  },
  "Sega": {
    "regex": "Dreamcast|Aplix_SEGASATURN",
    "device": "console",
    "models": [
      {
        "regex": "Dreamcast",
        "model": "Dreamcast"
      },
      {
        "regex": "Aplix_SEGASATURN",
        "model": "Saturn"
      }
    ]
  },
  "JXD": {
    "regex": "JXD_S601WIFI",
    "device": "console",
    "model": "S601 WiFi"
  },
  "Sony": {
    "regex": "(?:PlayStation ?(4 Pro|[2-5]|Portable|Vita)|sony_tv;ps5;|\\(PS3\\))",
    "device": "console",
    "models": [
      {
        "regex": "sony_tv;ps5;",
        "model": "PlayStation 5"
      },
      {
        "regex": "PlayStation 4 PRO",
        "model": "PlayStation 4 Pro"
      },
      {
        "regex": "\\(PS3\\)",
        "model": "PlayStation 3"
      },
      {
        "regex": "PlayStation ?(4 Pro|[2-5]|Portable|Vita)",
        "model": "PlayStation $1"
      }
    ]
  },
  "Retroid Pocket": {
    "regex": "Retroid Pocket",
    "device": "console",
    "models": [
      {
        "regex": "Pocket ([23]) ?(?:Plus|\\+)",
        "model": "$1 Plus"
      },
      {
        "regex": "Pocket 4 Pro",
        "model": "4 Pro"
      },
      {
        "regex": "Pocket ([235])",
        "model": "$1"
      }
    ]
  }
};
