<!doctype html>
<html>
  <title>npm-search</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/api/npm-search.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../api/npm-search.html">npm-search</a></h1> <p>Search for packages</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm.commands.search(searchTerms, [silent,] [staleness,] callback)
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>Search the registry for packages matching the search terms. The available parameters are:</p>
<ul>
<li>searchTerms:
Array of search terms. These terms are case-insensitive.</li>
<li>silent:
If true, npm will not log anything to the console.</li>
<li>staleness:
This is the threshold for stale packages. &quot;Fresh&quot; packages are not refreshed
from the registry. This value is measured in seconds.</li>
<li><p>callback:
Returns an object where each key is the name of a package, and the value
is information about that package along with a &#39;words&#39; property, which is
a space-delimited string of all of the interesting words in that package.
The only properties included are those that are searched, which generally include:</p>
<ul>
<li>name</li>
<li>description</li>
<li>maintainers</li>
<li>url</li>
<li>keywords</li>
</ul>
</li>
</ul>
<p>A search on the registry excludes any result that does not match all of the
search terms. It also removes any items from the results that contain an
excluded term (the &quot;searchexclude&quot; config). The search is case insensitive
and doesn&#39;t try to read your mind (it doesn&#39;t do any verb tense matching or the
like).</p>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-search &mdash; npm@2.15.12</p>

