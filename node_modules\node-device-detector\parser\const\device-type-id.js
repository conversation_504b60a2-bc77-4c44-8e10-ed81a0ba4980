const DEVICE_TYPE = require('./device-type');

const DEVICE_TYPE_IDS = {}
DEVICE_TYPE_IDS[DEVICE_TYPE.DESKTOP] = 0;
DEVICE_TYPE_IDS[DEVICE_TYPE.SMARTPHONE] = 1;
DEVICE_TYPE_IDS[DEVICE_TYPE.TABLET] = 2;
DEVICE_TYPE_IDS[DEVICE_TYPE.FEATURE_PHONE] = 3;
DEVICE_TYPE_IDS[DEVICE_TYPE.CONSOLE] = 4;
DEVICE_TYPE_IDS[DEVICE_TYPE.TV] = 5;
DEVICE_TYPE_IDS[DEVICE_TYPE.CAR_BROWSER] = 6;
DEVICE_TYPE_IDS[DEVICE_TYPE.SMART_DISPLAY] = 7;
DEVICE_TYPE_IDS[DEVICE_TYPE.CAMERA] = 8;
DEVICE_TYPE_IDS[DEVICE_TYPE.PORTABLE_MEDIA_PLAYER] = 9;
DEVICE_TYPE_IDS[DEVICE_TYPE.PHABLET] = 10;
DEVICE_TYPE_IDS[DEVICE_TYPE.SMART_SPEAKER] = 11;
DEVICE_TYPE_IDS[DEVICE_TYPE.WEARABLE] = 12;
DEVICE_TYPE_IDS[DEVICE_TYPE.PERIPHERAL] = 13;

module.exports = DEVICE_TYPE_IDS;
