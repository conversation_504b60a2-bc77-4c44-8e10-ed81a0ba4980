<!doctype html>
<html>
  <title>npm-restart</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/api/npm-restart.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../api/npm-restart.html">npm-restart</a></h1> <p>Restart a package</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm.commands.restart(packages, callback)
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>This restarts a package (or multiple packages).</p>
<p>This runs a package&#39;s &quot;stop&quot;, &quot;restart&quot;, and &quot;start&quot; scripts, and associated
pre- and post- scripts, in the order given below:</p>
<ol>
<li>prerestart</li>
<li>prestop</li>
<li>stop</li>
<li>poststop</li>
<li>restart</li>
<li>prestart</li>
<li>start</li>
<li>poststart</li>
<li>postrestart</li>
</ol>
<p>If no version is specified, then it restarts the &quot;active&quot; version.</p>
<p>npm can restart multiple packages. Just specify multiple packages in
the <code>packages</code> parameter.</p>
<h2 id="note">NOTE</h2>
<p>Note that the &quot;restart&quot; script is run <strong>in addition to</strong> the &quot;stop&quot;
and &quot;start&quot; scripts, not instead of them.</p>
<p>This is the behavior as of <code>npm</code> major version 2.  A change in this
behavior will be accompanied by an increase in major version number</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../api/npm-start.html">npm-start(3)</a></li>
<li><a href="../api/npm-stop.html">npm-stop(3)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-restart &mdash; npm@2.15.12</p>

