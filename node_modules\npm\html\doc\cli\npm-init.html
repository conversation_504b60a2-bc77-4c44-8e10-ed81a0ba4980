<!doctype html>
<html>
  <title>npm-init</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-init.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-init.html">npm-init</a></h1> <p>Interactively create a package.json file</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm init [-f|--force|-y|--yes]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>This will ask you a bunch of questions, and then write a package.json for you.</p>
<p>It attempts to make reasonable guesses about what you want things to be set to,
and then writes a package.json file with the options you&#39;ve selected.</p>
<p>If you already have a package.json file, it&#39;ll read that first, and default to
the options in there.</p>
<p>It is strictly additive, so it does not delete options from your package.json
without a really good reason to do so.</p>
<p>If you invoke it with <code>-f</code>, <code>--force</code>, <code>-y</code>, or <code>--yes</code>, it will use only
defaults and not prompt you for any options.</p>
<h2 id="configuration">CONFIGURATION</h2>
<h3 id="scope">scope</h3>
<ul>
<li>Default: none</li>
<li>Type: String</li>
</ul>
<p>The scope under which the new module should be created.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="https://github.com/isaacs/init-package-json">https://github.com/isaacs/init-package-json</a></li>
<li><a href="../files/package.json.html">package.json(5)</a></li>
<li><a href="../cli/npm-version.html">npm-version(1)</a></li>
<li><a href="../misc/npm-scope.html">npm-scope(7)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-init &mdash; npm@2.15.12</p>

