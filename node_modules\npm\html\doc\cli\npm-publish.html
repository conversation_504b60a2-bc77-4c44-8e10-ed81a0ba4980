<!doctype html>
<html>
  <title>npm-publish</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-publish.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-publish.html">npm-publish</a></h1> <p>Publish a package</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm publish &lt;tarball&gt; [--tag &lt;tag&gt;] [--access &lt;public|restricted&gt;]
npm publish &lt;folder&gt; [--tag &lt;tag&gt;] [--access &lt;public|restricted&gt;]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>Publishes a package to the registry so that it can be installed by name. All
files in the package directory are included if no local <code>.gitignore</code> or
<code>.npmignore</code> file is present. See <code><a href="../misc/npm-developers.html">npm-developers(7)</a></code> for full details on
what&#39;s included in the published package, as well as details on how the package
is built.</p>
<p>By default npm will publish to the public registry. This can be overridden by
specifying a different default registry or using a <code><a href="../misc/npm-scope.html">npm-scope(7)</a></code> in the name
(see <code><a href="../files/package.json.html">package.json(5)</a></code>).</p>
<ul>
<li><p><code>&lt;folder&gt;</code>:
A folder containing a package.json file</p>
</li>
<li><p><code>&lt;tarball&gt;</code>:
A url or file path to a gzipped tar archive containing a single folder
with a package.json file inside.</p>
</li>
<li><p><code>[--tag &lt;tag&gt;]</code>
Registers the published package with the given tag, such that <code>npm install
&lt;name&gt;@&lt;tag&gt;</code> will install this version.  By default, <code>npm publish</code> updates
and <code>npm install</code> installs the <code>latest</code> tag. See <code><a href="../cli/npm-dist-tag.html">npm-dist-tag(1)</a></code> for
details about tags.</p>
</li>
<li><p><code>[--access &lt;public|restricted&gt;]</code>
Tells the registry whether this package should be published as public or
restricted. Only applies to scoped packages, which default to <code>restricted</code>.
If you don&#39;t have a paid account, you must publish with <code>--access public</code>
to publish scoped packages.</p>
</li>
</ul>
<p>Fails if the package name and version combination already exists in
the specified registry.</p>
<p>Once a package is published with a given name and version, that
specific name and version combination can never be used again, even if
it is removed with <a href="../cli/npm-unpublish.html">npm-unpublish(1)</a>.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../misc/npm-registry.html">npm-registry(7)</a></li>
<li><a href="../misc/npm-scope.html">npm-scope(7)</a></li>
<li><a href="../cli/npm-adduser.html">npm-adduser(1)</a></li>
<li><a href="../cli/npm-owner.html">npm-owner(1)</a></li>
<li><a href="../cli/npm-deprecate.html">npm-deprecate(1)</a></li>
<li><a href="../cli/npm-tag.html">npm-tag(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-publish &mdash; npm@2.15.12</p>

