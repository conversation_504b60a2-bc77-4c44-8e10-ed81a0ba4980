<!doctype html>
<html>
  <title>npm-prune</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-prune.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-prune.html">npm-prune</a></h1> <p>Remove extraneous packages</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm prune [&lt;name&gt; [&lt;name ...]]
npm prune [&lt;name&gt; [&lt;name ...]] [--production]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>This command removes &quot;extraneous&quot; packages.  If a package name is
provided, then only packages matching one of the supplied names are
removed.</p>
<p>Extraneous packages are packages that are not listed on the parent
package&#39;s dependencies list.</p>
<p>If the <code>--production</code> flag is specified or the <code>NODE_ENV</code> environment
variable is set to <code>production</code>, this command will remove the packages
specified in your <code>devDependencies</code>. Setting <code>--production=false</code> will
negate <code>NODE_ENV</code> being set to <code>production</code>.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-uninstall.html">npm-uninstall(1)</a></li>
<li><a href="../files/npm-folders.html">npm-folders(5)</a></li>
<li><a href="../cli/npm-ls.html">npm-ls(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-prune &mdash; npm@2.15.12</p>

