module.exports = [
  {
    "regex": "Audacious(?:[ /]([\\d.]+))?",
    "name": "Audacious",
    "version": "$1"
  },
  {
    "regex": "(?:AlexaMediaPlayer/|^AlexaMediaPlayer/|^Echo/|Amazon;Echo(?:_|;)|^AlexaService/|^Alexa Mobile Voice/)([a-z\\d]+\\.[a-z.\\d]+)?",
    "name": "<PERSON><PERSON>",
    "version": "$1"
  },
  {
    "regex": "Banshee(?:[ /]([\\d.]+))?",
    "name": "Banshee",
    "version": "$1"
  },
  {
    "regex": "Boxee(?:[ /]([\\d.]+))?",
    "name": "<PERSON>ee",
    "version": "$1"
  },
  {
    "regex": "<PERSON>ine(?:[ /]([\\d.]+))?",
    "name": "<PERSON><PERSON>",
    "version": "$1"
  },
  {
    "regex": "<PERSON><PERSON>(?:/([\\d.]+))?",
    "name": "<PERSON><PERSON>",
    "version": "$1"
  },
  {
    "regex": "iTunes(?:-iPhone|-iPad)?(?:/([\\d.]+))?",
    "name": "iTunes",
    "version": "$1"
  },
  {
    "regex": "FlyCast(?:/([\\d.]+))?",
    "name": "FlyCast",
    "version": "$1"
  },
  {
    "regex": "foobar2000(?:/([\\d.]+))?",
    "name": "Foobar2000",
    "version": "$1"
  },
  {
    "regex": "MediaMonkey(?:[ /](\\d+[.\\d]+))?",
    "name": "MediaMonkey",
    "version": "$1"
  },
  {
    "regex": "Miro(?:/(\\d+[.\\d]+))?",
    "name": "Miro",
    "version": "$1"
  },
  {
    "regex": "NexPlayer(?:/(\\d+[.\\d]+))?",
    "name": "NexPlayer",
    "version": "$1"
  },
  {
    "regex": "Nightingale(?:/([\\d.]+))?",
    "name": "Nightingale",
    "version": "$1"
  },
  {
    "regex": "QuickTime(?:(?:(?:.+qtver=)|(?:(?: E-)?[\\./]))([\\d.]+))?",
    "name": "QuickTime",
    "version": "$1"
  },
  {
    "regex": "Songbird(?:/([\\d.]+))?",
    "name": "Songbird",
    "version": "$1"
  },
  {
    "regex": "SubStream(?:/([\\d.]+))?",
    "name": "SubStream",
    "version": "$1"
  },
  {
    "regex": "Sonos/([\\d.]+)?",
    "name": "SONOS",
    "version": "$1"
  },
  {
    "regex": "(?:Lib)?VLC(?:/([\\d.]+))?",
    "name": "VLC",
    "version": "$1"
  },
  {
    "regex": "Winamp(?:MPEG)?(?:/(\\d+[.\\d]+))?",
    "name": "Winamp",
    "version": "$1"
  },
  {
    "regex": "J\\. River Internet Reader/(\\d+\\.[.\\d]+)",
    "name": "JRiver Media Center",
    "version": "$1"
  },
  {
    "regex": "(?:Windows-Media-Player|NSPlayer)(?:/(\\d+[.\\d]+))?",
    "name": "Windows Media Player",
    "version": "$1"
  },
  {
    "regex": "XBMC(?:/([\\d.]+))?",
    "name": "XBMC",
    "version": "$1"
  },
  {
    "regex": "Kodi(?:/([\\d.]+))?",
    "name": "Kodi",
    "version": "$1"
  },
  {
    "regex": "stagefright(?:/([\\d.]+))?",
    "name": "Stagefright",
    "version": "$1"
  },
  {
    "regex": "GoogleChirp(?:/(\\d+[.\\d]+))?",
    "name": "Google Podcasts",
    "version": "$1"
  },
  {
    "regex": "Music Player Daemon (?:(\\d+[.\\d]+))?",
    "name": "Music Player Daemon",
    "version": "$1"
  },
  {
    "regex": "mpv (?:(\\d+[.\\d]+))?",
    "name": "mpv",
    "version": "$1"
  },
  {
    "regex": "HTC Streaming Player",
    "name": "HTC Streaming Player",
    "version": ""
  },
  {
    "regex": "MediaGo(?:/([\\w\\.]+))?",
    "name": "Sony Media Go",
    "version": "$1"
  },
  {
    "regex": "MPlayer[ /](\\d+\\.[\\d.])",
    "name": "MPlayer",
    "version": "$1"
  },
  {
    "regex": "Downcast/(\\d+\\.[\\d.]+)?",
    "name": "Downcast",
    "version": "$1"
  },
  {
    "regex": "^Juice/([\\d.]+)",
    "name": "Juice",
    "version": "$1"
  },
  {
    "regex": "just_audio/(\\d+\\.[.\\d]+)",
    "name": "Just Audio",
    "version": "$1"
  },
  {
    "regex": "^Kasts/(\\d+\\.[.\\d]+)",
    "name": "Kasts",
    "version": "$1"
  },
  {
    "regex": "MixerBox(?:%20Pro)?/([.\\d]+)",
    "name": "MixerBox",
    "version": "$1"
  },
  {
    "regex": "^MusicBee(?:/(\\d+\\.[.\\d]+))?",
    "name": "MusicBee",
    "version": "$1"
  },
  {
    "regex": "^amarok/(\\d+\\.[.\\d]+)",
    "name": "Amarok",
    "version": "$1"
  },
  {
    "regex": "Hubhopper/([\\d.]+)",
    "name": "Hubhopper",
    "version": "$1"
  },
  {
    "regex": "StudioDisplay/(\\d+\\.[\\d.]+)",
    "name": "StudioDisplay",
    "version": "$1"
  },
  {
    "regex": "JHV/SWHV-([.\\d+]+)",
    "name": "JHelioviewer",
    "version": "$1"
  },
  {
    "regex": "com\\.devcoder\\.iptvxtreamplayer",
    "name": "Xtream Player",
    "version": ""
  },
  {
    "regex": "DIGA(?:Plus/(\\d+\\.[.\\d]+))?",
    "name": "DIGA",
    "version": "$1"
  },
  {
    "regex": "YouView(?:HTML/(\\d+\\.[.\\d]+))?",
    "name": "YouView",
    "version": "$1"
  }
];
