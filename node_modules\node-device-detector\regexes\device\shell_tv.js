module.exports = {
  "Telefunken": {
    "regex": "Telefunken Shell",
    "device": "tv",
    "model": ""
  },
  "JVC": {
    "regex": "JVC Shell",
    "device": "tv",
    "model": ""
  },
  "Leff": {
    "regex": "Leff Shell",
    "device": "tv",
    "model": ""
  },
  "Leben": {
    "regex": "Leben Shell",
    "device": "tv",
    "model": ""
  },
  "Lumus": {
    "regex": "LUMUS Shell",
    "device": "tv",
    "model": ""
  },
  "Erisson": {
    "regex": "<PERSON>risson[_ ]Shell",
    "device": "tv",
    "model": ""
  },
  "BBK": {
    "regex": "BBK shell",
    "device": "tv",
    "model": ""
  },
  "Novex": {
    "regex": "Novex shell",
    "device": "tv",
    "model": ""
  },
  "Digma": {
    "regex": "Digma Shell",
    "device": "tv",
    "model": ""
  },
  "AMCV": {
    "regex": "AMCV Shell",
    "device": "tv",
    "model": ""
  },
  "Mystery": {
    "regex": "Mystery Shell",
    "device": "tv",
    "model": ""
  },
  "ECON": {
    "regex": "ECON Shell",
    "device": "tv",
    "model": ""
  },
  "Starwind": {
    "regex": "Starwind Shell",
    "device": "tv",
    "model": ""
  },
  "Kvant": {
    "regex": "Kvant Shell",
    "device": "tv",
    "model": ""
  },
  "Hi": {
    "regex": "Hi Shell",
    "device": "tv",
    "model": ""
  },
  "AKIRA": {
    "regex": "AKIRA Shell",
    "device": "tv",
    "model": ""
  },
  "Loview": {
    "regex": "Loview Shell",
    "device": "tv",
    "model": ""
  },
  "Supra": {
    "regex": "Supra Shell",
    "device": "tv",
    "model": ""
  },
  "Yuno": {
    "regex": "Yuno Shell",
    "device": "tv",
    "model": ""
  },
  "TCL": {
    "regex": "TCL/TCL-",
    "device": "tv",
    "model": ""
  },
  "RCA Tablets": {
    "regex": "TCL/RCA-",
    "device": "tv",
    "model": ""
  },
  "Thomson": {
    "regex": "TCL/THOM-",
    "device": "tv",
    "model": ""
  },
  "DEXP": {
    "regex": "DEXP Shell",
    "device": "tv",
    "model": ""
  }
};
