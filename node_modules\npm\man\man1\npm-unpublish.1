.TH "NPM\-UNPUBLISH" "1" "March 2017" "" ""
.SH "NAME"
\fBnpm-unpublish\fR \- Remove a package from the registry
.SH SYNOPSIS
.P
.RS 2
.nf
npm unpublish [@<scope>/]<name>[@<version>]
.fi
.RE
.SH WARNING
.P
\fBIt is generally considered bad behavior to remove versions of a library
that others are depending on!\fR
.P
Consider using the \fBdeprecate\fP command
instead, if your intent is to encourage users to upgrade\.
.P
There is plenty of room on the registry\.
.SH DESCRIPTION
.P
This removes a package version from the registry, deleting its
entry and removing the tarball\.
.P
If no version is specified, or if all versions are removed then
the root package entry is removed from the registry entirely\.
.P
Even if a package version is unpublished, that specific name and
version combination can never be reused\.  In order to publish the
package again, a new version number must be used\.
.P
The scope is optional and follows the usual rules for npm help 7 \fBnpm\-scope\fP\|\.
.SH SEE ALSO
.RS 0
.IP \(bu 2
npm help deprecate
.IP \(bu 2
npm help publish
.IP \(bu 2
npm help 7 registry
.IP \(bu 2
npm help adduser
.IP \(bu 2
npm help owner

.RE

