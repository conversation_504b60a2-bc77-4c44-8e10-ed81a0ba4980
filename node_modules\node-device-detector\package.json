{"name": "node-device-detector", "version": "2.2.1", "description": "Nodejs device detector (port matomo-org/device-detector)", "main": "index.js", "scripts": {"test": "mocha -- -R dot tests/*.spec.js", "test-coverage-text": "nyc --check-coverage --lines 90 --per-file --reporter=text mocha -- -R dot tests/*.spec.js", "test-coverage-html": "nyc --reporter=html mocha -- -R dot tests/*.spec.js", "prettier": "prettier --write .", "eslint": "eslint . --ext .jsx,.ts,.tsx", "publish-info": "npm publish --dry-run"}, "repository": {"type": "git", "url": "git+https://github.com/sanchezzzhak/node-device-detector.git"}, "keywords": ["device-detector", "device detect", "detect mobile", "detect tablet", "detect phablet", "detect os", "detect browser", "detect device brand", "detect device model", "detect user-agent", "detect trusted device", "client-hints", "useragent"], "author": "sanchezzzhak", "license": "MIT", "bugs": {"url": "https://github.com/sanchezzzhak/node-device-detector/issues"}, "homepage": "https://github.com/sanchezzzhak/node-device-detector#readme", "dependencies": {}, "engines": {"node": ">= 10.x", "npm": ">= 6.x"}, "devDependencies": {"js-yaml": "^4.1.0", "@fast-csv/parse": "^5.0.0", "@fast-csv/format": "^5.0.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "@typescript-eslint/parser": "^5.42.1", "benchmark": "^2.1.4", "chai": "^4.5.0", "cli-table": "^0.3.6", "commander": "^11.1.0", "eslint": "^8.27.0", "mocha": "^10.8.2", "prettier": "^2.4.1", "typescript": "^4.8.4"}}