<!doctype html>
<html>
  <title>npm-version</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-version.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-version.html">npm-version</a></h1> <p>Bump a package version</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm version [&lt;newversion&gt; | major | minor | patch | premajor | preminor | prepatch | prerelease]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>Run this in a package directory to bump the version and write the new
data back to <code>package.json</code> and, if present, <code>npm-shrinkwrap.json</code>.</p>
<p>The <code>newversion</code> argument should be a valid semver string, <em>or</em> a
valid second argument to semver.inc (one of <code>patch</code>, <code>minor</code>, <code>major</code>,
<code>prepatch</code>, <code>preminor</code>, <code>premajor</code>, <code>prerelease</code>). In the second case,
the existing version will be incremented by 1 in the specified field.</p>
<p>If run in a git repo, it will also create a version commit and tag.
This behavior is controlled by <code>git-tag-version</code> (see below), and can 
be disabled on the command line by running <code>npm --no-git-tag-version version</code>.
It will fail if the working directory is not clean, unless the <code>--force</code>
flag is set.</p>
<p>If supplied with <code>--message</code> (shorthand: <code>-m</code>) config option, npm will
use it as a commit message when creating a version commit.  If the
<code>message</code> config contains <code>%s</code> then that will be replaced with the
resulting version number.  For example:</p>
<pre><code>npm version patch -m &quot;Upgrade to %s for reasons&quot;
</code></pre><p>If the <code>sign-git-tag</code> config is set, then the tag will be signed using
the <code>-s</code> flag to git.  Note that you must have a default GPG key set up
in your git config for this to work properly.  For example:</p>
<pre><code>$ npm config set sign-git-tag true
$ npm version patch

You need a passphrase to unlock the secret key for
user: &quot;isaacs (http://blog.izs.me/) &lt;<EMAIL>&gt;&quot;
2048-bit RSA key, ID 6C481CF6, created 2010-08-31

Enter passphrase:
</code></pre><p>If <code>preversion</code>, <code>version</code>, or <code>postversion</code> are in the <code>scripts</code> property of
the package.json, they will be executed as part of running <code>npm version</code>.</p>
<p>The exact order of execution is as follows:</p>
<ol>
<li>Check to make sure the git working directory is clean before we get started.
Your scripts may add files to the commit in future steps.
This step is skipped if the <code>--force</code> flag is set.</li>
<li>Run the <code>preversion</code> script. These scripts have access to the old <code>version</code> in package.json.
A typical use would be running your full test suite before deploying.
Any files you want added to the commit should be explicitly added using <code>git add</code>.</li>
<li>Bump <code>version</code> in <code>package.json</code> as requested (<code>patch</code>, <code>minor</code>, <code>major</code>, etc). </li>
<li>Run the <code>version</code> script. These scripts have access to the new <code>version</code> in package.json
(so they can incorporate it into file headers in generated files for example). 
Again, scripts should explicitly add generated files to the commit using <code>git add</code>.</li>
<li>Commit and tag.</li>
<li>Run the <code>postversion</code> script. Use it to clean up the file system or automatically push 
the commit and/or tag.</li>
</ol>
<p>Take the following example:</p>
<pre><code>&quot;scripts&quot;: {
  &quot;preversion&quot;: &quot;npm test&quot;,
  &quot;version&quot;: &quot;npm run build &amp;&amp; git add -A dist&quot;,
  &quot;postversion&quot;: &quot;git push &amp;&amp; git push --tags &amp;&amp; rm -rf build/temp&quot;
}
</code></pre><p>This runs all your tests, and proceeds only if they pass. Then runs your <code>build</code> script, and
adds everything in the <code>dist</code> directory to the commit. After the commit, it pushes the new commit
and tag up to the server, and deletes the <code>build/temp</code> directory.</p>
<h2 id="configuration">CONFIGURATION</h2>
<h3 id="git-tag-version">git-tag-version</h3>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>Commit and tag the version change.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-init.html">npm-init(1)</a></li>
<li><a href="../cli/npm-run-script.html">npm-run-script(1)</a></li>
<li><a href="../misc/npm-scripts.html">npm-scripts(7)</a></li>
<li><a href="../files/package.json.html">package.json(5)</a></li>
<li><a href="../misc/semver.html">semver(7)</a></li>
<li><a href="../misc/config.html">config(7)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-version &mdash; npm@2.15.12</p>

