<!doctype html>
<html>
  <title>npm-team</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-team.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-team.html">npm-team</a></h1> <p>Manage organization teams and team memberships</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm team create &lt;scope:team&gt;
npm team destroy &lt;scope:team&gt;

npm team add &lt;scope:team&gt; &lt;user&gt;
npm team rm &lt;scope:team&gt; &lt;user&gt;

npm team ls &lt;scope&gt;|&lt;scope:team&gt;

npm team edit &lt;scope:team&gt;
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>Used to manage teams in organizations, and change team memberships. Does not
handle permissions for packages.</p>
<p>Teams must always be fully qualified with the organization/scope they belond to
when operating on them, separated by a colon (<code>:</code>). That is, if you have a
<code>developers</code> team on a <code>foo</code> organization, you must always refer to that team as
<code>foo:developers</code> in these commands.</p>
<ul>
<li><p>create / destroy:
Create a new team, or destroy an existing one.</p>
</li>
<li><p>add / rm:
Add a user to an existing team, or remove a user from a team they belong to.</p>
</li>
<li><p>ls:
If performed on an organization name, will return a list of existing teams
under that organization. If performed on a team, it will instead return a list
of all users belonging to that particular team.</p>
</li>
</ul>
<h2 id="details">DETAILS</h2>
<p><code>npm team</code> always operates directly on the current registry, configurable from
the command line using <code>--registry=&lt;registry url&gt;</code>.</p>
<p>In order to create teams and manage team membership, you must be a <em>team admin</em>
under the given organization. Listing teams and team memberships may be done by
any member of the organizations.</p>
<p>Organization creation and management of team admins and <em>organization</em> members
is done through the website, not the npm CLI.</p>
<p>To use teams to manage permissions on packages belonging to your organization,
use the <code>npm access</code> command to grant or revoke the appropriate permissions.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-access.html">npm-access(1)</a></li>
<li><a href="../misc/npm-registr.html">npm-registr(7)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-team &mdash; npm@2.15.12</p>

