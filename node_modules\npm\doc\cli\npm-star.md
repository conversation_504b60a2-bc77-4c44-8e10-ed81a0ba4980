npm-star(1) -- Mark your favorite packages
==========================================

## SYNOPSIS

    npm star <pkgname> [<pkg>, ...]
    npm unstar <pkgname> [<pkg>, ...]

## DESCRIPTION

"Starring" a package means that you have some interest in it.  It's
a vaguely positive way to show that you care.

"Unstarring" is the same thing, but in reverse.

It's a boolean thing.  Starring repeatedly has no additional effect.

## SEE ALSO

* npm-view(1)
* npm-whoami(1)
* npm-adduser(1)
