<!doctype html>
<html>
  <title>npm-access</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-access.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-access.html">npm-access</a></h1> <p>Set access level on published packages</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm access public [&lt;package&gt;]
npm access restricted [&lt;package&gt;]

npm access grant &lt;read-only|read-write&gt; &lt;scope:team&gt; [&lt;package&gt;]
npm access revoke &lt;scope:team&gt; [&lt;package&gt;]

npm access ls-packages [&lt;user&gt;|&lt;scope&gt;|&lt;scope:team&gt;]
npm access ls-collaborators [&lt;package&gt; [&lt;user&gt;]]
npm access edit [&lt;package&gt;]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>Used to set access controls on private packages.</p>
<p>For all of the subcommands, <code>npm access</code> will perform actions on the packages
in the current working directory if no package name is passed to the
subcommand.</p>
<ul>
<li><p>public / restricted:
Set a package to be either publicly accessible or restricted.</p>
</li>
<li><p>grant / revoke:
Add or remove the ability of users and teams to have read-only or read-write
access to a package.</p>
</li>
<li><p>ls-packages:</p>
<p>Show all of the packages a user or a team is able to access, along with the
access level, except for read-only public packages (it won&#39;t print the whole
registry listing)</p>
</li>
<li><p>ls-collaborators:
Show all of the access privileges for a package. Will only show permissions
for packages to which you have at least read access. If <code>&lt;user&gt;</code> is passed in,
the list is filtered only to teams <em>that</em> user happens to belong to.</p>
</li>
<li><p>edit:
Set the access privileges for a package at once using <code>$EDITOR</code>.</p>
</li>
</ul>
<h2 id="details">DETAILS</h2>
<p><code>npm access</code> always operates directly on the current registry, configurable
from the command line using <code>--registry=&lt;registry url&gt;</code>.</p>
<p>Unscoped packages are <em>always public</em>.</p>
<p>Scoped packages <em>default to restricted</em>, but you can either publish them as
public using <code>npm publish --access=public</code>, or set their access as public using
<code>npm access public</code> after the initial publish.</p>
<p>You must have privileges to set the access of a package:</p>
<ul>
<li>You are an owner of an unscoped or scoped package.</li>
<li>You are a member of the team that owns a scope.</li>
<li>You have been given read-write privileges for a package, either as a member
of a team or directly as an owner.</li>
</ul>
<p>If your account is not paid, then attempts to publish scoped packages will fail
with an HTTP 402 status code (logically enough), unless you use
<code>--access=public</code>.</p>
<p>Management of teams and team memberships is done with the <code>npm team</code> command.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-team.html">npm-team(1)</a></li>
<li><a href="../cli/npm-publish.html">npm-publish(1)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../misc/npm-registry.html">npm-registry(7)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-access &mdash; npm@2.15.12</p>

