{"name": "@peculiar/asn1-pfx", "version": "2.3.15", "description": "ASN.1 schema of `PKCS #12: Personal Information Exchange Syntax v1.1` (RFC7292)", "files": ["build/**/*.{js,d.ts}", "LICENSE", "README.md"], "bugs": {"url": "https://github.com/PeculiarVentures/asn1-schema/issues"}, "homepage": "https://github.com/PeculiarVentures/asn1-schema/tree/master/packages/pfx#readme", "keywords": ["asn"], "author": "PeculiarVentures, LLC", "license": "MIT", "main": "build/cjs/index.js", "module": "build/es2015/index.js", "types": "build/types/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"test": "mocha", "clear": "<PERSON><PERSON><PERSON> build", "build": "npm run build:module && npm run build:types", "build:module": "npm run build:cjs && npm run build:es2015", "build:cjs": "tsc -p tsconfig.compile.json --removeComments --module commonjs --outDir build/cjs", "build:es2015": "tsc -p tsconfig.compile.json --removeComments --module ES2015 --outDir build/es2015", "prebuild:types": "rimraf build/types", "build:types": "tsc -p tsconfig.compile.json --outDir build/types --declaration --emitDeclarationOnly", "rebuild": "npm run clear && npm run build"}, "dependencies": {"@peculiar/asn1-cms": "^2.3.15", "@peculiar/asn1-pkcs8": "^2.3.15", "@peculiar/asn1-rsa": "^2.3.15", "@peculiar/asn1-schema": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}, "gitHead": "b6c7130efa9bc3ee5e2ea1da5d01aede5182921f"}