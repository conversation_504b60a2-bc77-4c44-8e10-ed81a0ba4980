module.exports = [
  {
    "regex": "Outlook-Express(?:/(\\d+[.\\d]+))?",
    "name": "Outlook Express",
    "version": "$1"
  },
  {
    "regex": "^Outlook-iOS/(?:.+\\((\\d+[.\\d]+)\\)$)?",
    "name": "Microsoft Outlook",
    "version": "$1"
  },
  {
    "regex": "(?:(?:Microsoft )?Outlook|MacOutlook)(?:[/ ](\\d+[.\\d]+))?",
    "name": "Microsoft Outlook",
    "version": "$1"
  },
  {
    "regex": "WindowsMail(?:/(\\d+[.\\d]+))",
    "name": "Windows Mail",
    "version": "$1"
  },
  {
    "regex": "(?:Thunderbird|Icedove|Shredder)(?:/(\\d+[.\\d]+))?",
    "name": "Thunderbird",
    "version": "$1"
  },
  {
    "regex": "Spicebird/(\\d+\\.[.\\d]+)",
    "name": "Spicebird",
    "version": "$1"
  },
  {
    "regex": "Airmail(?: (\\d+[.\\d]+))?",
    "name": "Airmail",
    "version": "$1"
  },
  {
    "regex": "Lotus-Notes(?:/(\\d+[.\\d]+))?",
    "name": "Lotus Notes",
    "version": "$1"
  },
  {
    "regex": "Barca(?:Pro)?(?:[/ ](\\d+[.\\d]+))?",
    "name": "Barca",
    "version": "$1"
  },
  {
    "regex": "Postbox(?:[/ ](\\d+[.\\d]+))?",
    "name": "Postbox",
    "version": "$1"
  },
  {
    "regex": "MailBar(?:[/ ](\\d+[.\\d]+))?",
    "name": "MailBar",
    "version": "$1"
  },
  {
    "regex": "The Bat!(?: Voyager)?(?:[/ ](\\d+[.\\d]+))?",
    "name": "The Bat!",
    "version": "$1"
  },
  {
    "regex": "DAVdroid(?:/(\\d+[.\\d]+))?",
    "name": "DAVdroid",
    "version": "$1"
  },
  {
    "regex": "(?:SeaMonkey|Iceape)(?:/(\\d+[.\\d]+))?",
    "name": "SeaMonkey",
    "version": "$1"
  },
  {
    "regex": "Live5ch/(\\d+[.\\d]+)",
    "name": "Live5ch",
    "version": "$1"
  },
  {
    "regex": "JaneView/",
    "name": "JaneView",
    "version": ""
  },
  {
    "regex": "BathyScaphe/",
    "name": "BathyScaphe",
    "version": ""
  },
  {
    "regex": "Raindrop\\.io/(\\d+[.\\d]+)",
    "name": "Raindrop.io",
    "version": "$1"
  },
  {
    "regex": "Franz/(\\d+[.\\d]+)",
    "name": "Franz",
    "version": "$1"
  },
  {
    "regex": "Mailspring/(\\d+[.\\d]+)",
    "name": "Mailspring",
    "version": "$1"
  },
  {
    "regex": "Notion/(\\d+[.\\d]+)",
    "name": "Notion",
    "version": "$1"
  },
  {
    "regex": "Basecamp[0-9]/?(\\d+[.\\d]+)",
    "name": "Basecamp",
    "version": "$1"
  },
  {
    "regex": "Evernote/?(\\d+[.\\d]+)",
    "name": "Evernote",
    "version": "$1"
  },
  {
    "regex": "ramboxpro/(\\d+\\.[.\\d]+)?",
    "name": "Rambox Pro",
    "version": "$1"
  },
  {
    "regex": "Mailbird/(\\d+\\.[.\\d]+)/",
    "name": "Mailbird",
    "version": "$1"
  },
  {
    "regex": "Yahoo%20Mail",
    "name": "Yahoo Mail",
    "version": ""
  },
  {
    "regex": "jp.co.yahoo.ymail/([\\d.]+)",
    "name": "Yahoo! Mail",
    "version": "$1"
  },
  {
    "regex": "eM ?Client/(\\d+\\.[.\\d]+)",
    "name": "eM Client",
    "version": "$1"
  },
  {
    "regex": "NaverMailApp/(\\d+\\.[.\\d]+)",
    "name": "NAVER Mail",
    "version": "$1"
  },
  {
    "regex": "^Mail/([\\d.]+)",
    "name": "Apple Mail",
    "version": "$1"
  },
  {
    "regex": "Foxmail/(\\d+[.\\d]+)",
    "name": "Foxmail",
    "version": "$1"
  },
  {
    "regex": "MailMaster(?:PC|_Android_Mobile)?/(\\d+[.\\d]+)",
    "name": "Mail Master",
    "version": "$1"
  },
  {
    "regex": "BlueMail/(\\d+[.\\d]+)",
    "name": "BlueMail",
    "version": "$1"
  },
  {
    "regex": "mailapp/(\\d+\\.[.\\d]+)",
    "name": "mailapp",
    "version": "$1"
  },
  {
    "regex": "Android-Gmail",
    "name": "Gmail",
    "version": ""
  }
];
