<!doctype html>

<html>
<head>
<style>
  html { background:#202050;
	font-family:CentSchbook Mono BT, Bitstream Vera Sans Mono, monofont, monospace;
  }
body { background:#ddd; width:600px; border:10px solid #fff; margin:2em auto; padding:2em }
h1 {
  font-size:200px;
  line-height:1;
  font-family:"gubblebum-blocky", monospace;
  color:#f00;
  text-align:center;
  padding:0;
  margin:0 auto;
  text-indent:-999em;
  height:202px;
  width:519px;
  background:url(npm.png) center;
}
h2 {
  color:#202050;
  font-size:100%;
}
p, ul, ol { margin:1em 0 0; padding:0 }
li { list-style-position:inside }
a { color:#f00; text-decoration:none; }
a:hover { text-decoration:underline; }
code { background:#fff ; outline: 1px solid #ccc; padding:0 2px; }

@font-face {
	font-family:monofont;
	src: url(http://foohack.com/tpl/fonts/Bitstream-Vera-Sans-Mono/VeraMono.ttf) format("truetype");
}
@font-face {
	font-family:monofont;
	font-style:italic;
	src: url(http://foohack.com/tpl/fonts/Bitstream-Vera-Sans-Mono/VeraMoIt.ttf) format("truetype");
}
@font-face {
	font-family:monofont;
	font-weight:bold;
	src: url(http://foohack.com/tpl/fonts/Bitstream-Vera-Sans-Mono/VeraMoBd.ttf) format("truetype");
}
@font-face {
	font-family:monofont;
	font-style:italic;
	font-weight:bold;
	src: url(http://foohack.com/tpl/fonts/Bitstream-Vera-Sans-Mono/VeraMoBI.ttf) format("truetype");
}

</style>
	<title>npm - JavaScript Package Manager</title>
</head>
<h1>npm</h1>

<p>npm is a package manager for <a href="http://nodejs.org/">node</a>.  You can use it to install
  and publish your node programs.  It manages dependencies and does other cool stuff.</p>

<h2>Easy Zero Line Install</h2>

<p><a href="https://nodejs.org/en/download/">Install Node.js</a> <br>
(npm comes with it.)</p>

<p>Because a one-line install is one too many.</p>

<h2>Fancy Install</h2>

<ol>
  <li><a href="https://github.com/npm/npm">Get the code.</a>
  <li>Do what <a href="doc/README.html">the README</a>
      says to do.
</ol>

<p>There's a pretty thorough install script at
<a href="https://npmjs.org/install.sh">https://npmjs.org/install.sh</a></p>

<p>For maximum security, make sure to thoroughly inspect every
program that you run on your computer!</p>

<h2>Other Cool Stuff</h2>

<ul>
  <li><a href="doc/README.html">README</a>
  <li><a href="doc/">Help Documentation</a>
  <li><a href="https://www.npmjs.com/">Search for Packages</a>
  <li><a href="https://github.com/npm/npm/issues">Bugs</a>
</ul>

</body>
</html>
