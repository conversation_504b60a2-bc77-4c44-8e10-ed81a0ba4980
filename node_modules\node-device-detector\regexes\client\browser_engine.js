module.exports = [
  {
    "regex": "NetFront",
    "name": "NetFront"
  },
  {
    "regex": "Edge/",
    "name": "Edge"
  },
  {
    "regex": "Trident",
    "name": "Trident"
  },
  {
    "regex": "Chr[o0]me/(?!1?\\d\\.|2[0-7]\\.)",
    "name": "Blink"
  },
  {
    "regex": "(?:Apple)?WebKit",
    "name": "WebKit"
  },
  {
    "regex": "Presto",
    "name": "Presto"
  },
  {
    "regex": "Goanna",
    "name": "Goanna"
  },
  {
    "regex": "(?<!like )Clecko",
    "name": "Clecko"
  },
  {
    "regex": "(?<!like )Gecko",
    "name": "Gecko"
  },
  {
    "regex": "KHTML",
    "name": "KHTML"
  },
  {
    "regex": "NetSurf",
    "name": "NetSurf"
  },
  {
    "regex": "<PERSON><PERSON>",
    "name": "<PERSON><PERSON>"
  },
  {
    "regex": "<PERSON><PERSON><PERSON>(?:Flow)?",
    "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"
  },
  {
    "regex": "xC<PERSON>s_<PERSON>",
    "name": "Arachne"
  },
  {
    "regex": "LibWeb\\+LibJs",
    "name": "LibWeb"
  },
  {
    "regex": "Maple (?!III)(\\d+[.\\d]+)|Maple\\d{4}",
    "name": "Maple"
  }
];
