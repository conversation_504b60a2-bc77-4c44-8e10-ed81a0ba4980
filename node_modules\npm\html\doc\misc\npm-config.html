<!doctype html>
<html>
  <title>npm-config</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/misc/npm-config.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../misc/npm-config.html">npm-config</a></h1> <p>More than you probably want to know about npm configuration</p>
<h2 id="description">DESCRIPTION</h2>
<p>npm gets its configuration values from the following sources, sorted by priority:</p>
<h3 id="command-line-flags">Command Line Flags</h3>
<p>Putting <code>--foo bar</code> on the command line sets the <code>foo</code> configuration
parameter to <code>&quot;bar&quot;</code>.  A <code>--</code> argument tells the cli parser to stop
reading flags.  A <code>--flag</code> parameter that is at the <em>end</em> of the
command will be given the value of <code>true</code>.</p>
<h3 id="environment-variables">Environment Variables</h3>
<p>Any environment variables that start with <code>npm_config_</code> will be
interpreted as a configuration parameter.  For example, putting
<code>npm_config_foo=bar</code> in your environment will set the <code>foo</code>
configuration parameter to <code>bar</code>.  Any environment configurations that
are not given a value will be given the value of <code>true</code>.  Config
values are case-insensitive, so <code>NPM_CONFIG_FOO=bar</code> will work the
same.</p>
<h3 id="npmrc-files">npmrc Files</h3>
<p>The four relevant files are:</p>
<ul>
<li>per-project configuration file (<code>/path/to/my/project/.npmrc</code>)</li>
<li>per-user configuration file (defaults to <code>$HOME/.npmrc</code>; configurable via CLI
option <code>--userconfig</code> or environment variable <code>$NPM_CONF_USERCONFIG</code>)</li>
<li>global configuration file (defaults to <code>$PREFIX/etc/npmrc</code>; configurable via
CLI option <code>--globalconfig</code> or environment variable <code>$NPM_CONF_GLOBALCONFIG</code>)</li>
<li>npm&#39;s built-in configuration file (<code>/path/to/npm/npmrc</code>)</li>
</ul>
<p>See <a href="../files/npmrc.html">npmrc(5)</a> for more details.</p>
<h3 id="default-configs">Default Configs</h3>
<p>A set of configuration parameters that are internal to npm, and are
defaults if nothing else is specified.</p>
<h2 id="shorthands-and-other-cli-niceties">Shorthands and Other CLI Niceties</h2>
<p>The following shorthands are parsed on the command-line:</p>
<ul>
<li><code>-v</code>: <code>--version</code></li>
<li><code>-h</code>, <code>-?</code>, <code>--help</code>, <code>-H</code>: <code>--usage</code></li>
<li><code>-s</code>, <code>--silent</code>: <code>--loglevel silent</code></li>
<li><code>-q</code>, <code>--quiet</code>: <code>--loglevel warn</code></li>
<li><code>-d</code>: <code>--loglevel info</code></li>
<li><code>-dd</code>, <code>--verbose</code>: <code>--loglevel verbose</code></li>
<li><code>-ddd</code>: <code>--loglevel silly</code></li>
<li><code>-g</code>: <code>--global</code></li>
<li><code>-C</code>: <code>--prefix</code></li>
<li><code>-l</code>: <code>--long</code></li>
<li><code>-m</code>: <code>--message</code></li>
<li><code>-p</code>, <code>--porcelain</code>: <code>--parseable</code></li>
<li><code>-reg</code>: <code>--registry</code></li>
<li><code>-f</code>: <code>--force</code></li>
<li><code>-desc</code>: <code>--description</code></li>
<li><code>-S</code>: <code>--save</code></li>
<li><code>-D</code>: <code>--save-dev</code></li>
<li><code>-O</code>: <code>--save-optional</code></li>
<li><code>-B</code>: <code>--save-bundle</code></li>
<li><code>-E</code>: <code>--save-exact</code></li>
<li><code>-y</code>: <code>--yes</code></li>
<li><code>-n</code>: <code>--yes false</code></li>
<li><code>ll</code> and <code>la</code> commands: <code>ls --long</code></li>
</ul>
<p>If the specified configuration param resolves unambiguously to a known
configuration parameter, then it is expanded to that configuration
parameter.  For example:</p>
<pre><code>npm ls --par
# same as:
npm ls --parseable
</code></pre><p>If multiple single-character shorthands are strung together, and the
resulting combination is unambiguously not some other configuration
param, then it is expanded to its various component pieces.  For
example:</p>
<pre><code>npm ls -gpld
# same as:
npm ls --global --parseable --long --loglevel info
</code></pre><h2 id="per-package-config-settings">Per-Package Config Settings</h2>
<p>When running scripts (see <code><a href="../misc/npm-scripts.html">npm-scripts(7)</a></code>) the package.json &quot;config&quot;
keys are overwritten in the environment if there is a config param of
<code>&lt;name&gt;[@&lt;version&gt;]:&lt;key&gt;</code>.  For example, if the package.json has
this:</p>
<pre><code>{ &quot;name&quot; : &quot;foo&quot;
, &quot;config&quot; : { &quot;port&quot; : &quot;8080&quot; }
, &quot;scripts&quot; : { &quot;start&quot; : &quot;node server.js&quot; } }
</code></pre><p>and the server.js is this:</p>
<pre><code>http.createServer(...).listen(process.env.npm_package_config_port)
</code></pre><p>then the user could change the behavior by doing:</p>
<pre><code>npm config set foo:port 80
</code></pre><p>See <a href="../files/package.json.html">package.json(5)</a> for more information.</p>
<h2 id="config-settings">Config Settings</h2>
<h3 id="access">access</h3>
<ul>
<li>Default: <code>restricted</code></li>
<li>Type: Access</li>
</ul>
<p>When publishing scoped packages, the access level defaults to <code>restricted</code>.  If
you want your scoped package to be publicly viewable (and installable) set
<code>--access=public</code>. The only valid values for <code>access</code> are <code>public</code> and
<code>restricted</code>. Unscoped packages <em>always</em> have an access level of <code>public</code>.</p>
<h3 id="always-auth">always-auth</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Force npm to always require authentication when accessing the registry,
even for <code>GET</code> requests.</p>
<h3 id="bin-links">bin-links</h3>
<ul>
<li>Default: <code>true</code></li>
<li>Type: Boolean</li>
</ul>
<p>Tells npm to create symlinks (or <code>.cmd</code> shims on Windows) for package
executables.</p>
<p>Set to false to have it not do this.  This can be used to work around
the fact that some file systems don&#39;t support symlinks, even on
ostensibly Unix systems.</p>
<h3 id="browser">browser</h3>
<ul>
<li>Default: OS X: <code>&quot;open&quot;</code>, Windows: <code>&quot;start&quot;</code>, Others: <code>&quot;xdg-open&quot;</code></li>
<li>Type: String</li>
</ul>
<p>The browser that is called by the <code>npm docs</code> command to open websites.</p>
<h3 id="ca">ca</h3>
<ul>
<li>Default: The npm CA certificate</li>
<li>Type: String, Array or null</li>
</ul>
<p>The Certificate Authority signing certificate that is trusted for SSL
connections to the registry. Values should be in PEM format with newlines
replaced by the string &quot;\n&quot;. For example:</p>
<pre><code>ca=&quot;-----BEGIN CERTIFICATE-----\nXXXX\nXXXX\n-----END CERTIFICATE-----&quot;
</code></pre><p>Set to <code>null</code> to only allow &quot;known&quot; registrars, or to a specific CA cert
to trust only that specific signing authority.</p>
<p>Multiple CAs can be trusted by specifying an array of certificates:</p>
<pre><code>ca[]=&quot;...&quot;
ca[]=&quot;...&quot;
</code></pre><p>See also the <code>strict-ssl</code> config.</p>
<h3 id="cafile">cafile</h3>
<ul>
<li>Default: <code>null</code></li>
<li>Type: path</li>
</ul>
<p>A path to a file containing one or multiple Certificate Authority signing
certificates. Similar to the <code>ca</code> setting, but allows for multiple CA&#39;s, as
well as for the CA information to be stored in a file on disk.</p>
<h3 id="cache">cache</h3>
<ul>
<li>Default: Windows: <code>%AppData%\npm-cache</code>, Posix: <code>~/.npm</code></li>
<li>Type: path</li>
</ul>
<p>The location of npm&#39;s cache directory.  See <code><a href="../cli/npm-cache.html">npm-cache(1)</a></code></p>
<h3 id="cache-lock-stale">cache-lock-stale</h3>
<ul>
<li>Default: 60000 (1 minute)</li>
<li>Type: Number</li>
</ul>
<p>The number of ms before cache folder lockfiles are considered stale.</p>
<h3 id="cache-lock-retries">cache-lock-retries</h3>
<ul>
<li>Default: 10</li>
<li>Type: Number</li>
</ul>
<p>Number of times to retry to acquire a lock on cache folder lockfiles.</p>
<h3 id="cache-lock-wait">cache-lock-wait</h3>
<ul>
<li>Default: 10000 (10 seconds)</li>
<li>Type: Number</li>
</ul>
<p>Number of ms to wait for cache lock files to expire.</p>
<h3 id="cache-max">cache-max</h3>
<ul>
<li>Default: Infinity</li>
<li>Type: Number</li>
</ul>
<p>The maximum time (in seconds) to keep items in the registry cache before
re-checking against the registry.</p>
<p>Note that no purging is done unless the <code>npm cache clean</code> command is
explicitly used, and that only GET requests use the cache.</p>
<h3 id="cache-min">cache-min</h3>
<ul>
<li>Default: 10</li>
<li>Type: Number</li>
</ul>
<p>The minimum time (in seconds) to keep items in the registry cache before
re-checking against the registry.</p>
<p>Note that no purging is done unless the <code>npm cache clean</code> command is
explicitly used, and that only GET requests use the cache.</p>
<h3 id="cert">cert</h3>
<ul>
<li>Default: <code>null</code></li>
<li>Type: String</li>
</ul>
<p>A client certificate to pass when accessing the registry.  Values should be in
PEM format with newlines replaced by the string &quot;\n&quot;. For example:</p>
<pre><code>cert=&quot;-----BEGIN CERTIFICATE-----\nXXXX\nXXXX\n-----END CERTIFICATE-----&quot;
</code></pre><p>It is <em>not</em> the path to a certificate file (and there is no &quot;certfile&quot; option).</p>
<h3 id="color">color</h3>
<ul>
<li>Default: true</li>
<li>Type: Boolean or <code>&quot;always&quot;</code></li>
</ul>
<p>If false, never shows colors.  If <code>&quot;always&quot;</code> then always shows colors.
If true, then only prints color codes for tty file descriptors.</p>
<h3 id="depth">depth</h3>
<ul>
<li>Default: Infinity</li>
<li>Type: Number</li>
</ul>
<p>The depth to go when recursing directories for <code>npm ls</code>,
<code>npm cache ls</code>, and <code>npm outdated</code>.</p>
<p>For <code>npm outdated</code>, a setting of <code>Infinity</code> will be treated as <code>0</code>
since that gives more useful information.  To show the outdated status
of all packages and dependents, use a large integer value,
e.g., <code>npm outdated --depth 9999</code></p>
<h3 id="description">description</h3>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>Show the description in <code>npm search</code></p>
<h3 id="dev">dev</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Install <code>dev-dependencies</code> along with packages.</p>
<p>Note that <code>dev-dependencies</code> are also installed if the <code>npat</code> flag is
set.</p>
<h3 id="editor">editor</h3>
<ul>
<li>Default: <code>EDITOR</code> environment variable if set, or <code>&quot;vi&quot;</code> on Posix,
or <code>&quot;notepad&quot;</code> on Windows.</li>
<li>Type: path</li>
</ul>
<p>The command to run for <code>npm edit</code> or <code>npm config edit</code>.</p>
<h3 id="engine-strict">engine-strict</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If set to true, then npm will stubbornly refuse to install (or even
consider installing) any package that claims to not be compatible with
the current Node.js version.</p>
<h3 id="force">force</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Makes various commands more forceful.</p>
<ul>
<li>lifecycle script failure does not block progress.</li>
<li>publishing clobbers previously published versions.</li>
<li>skips cache when requesting from the registry.</li>
<li>prevents checks against clobbering non-npm files.</li>
</ul>
<h3 id="fetch-retries">fetch-retries</h3>
<ul>
<li>Default: 2</li>
<li>Type: Number</li>
</ul>
<p>The &quot;retries&quot; config for the <code>retry</code> module to use when fetching
packages from the registry.</p>
<h3 id="fetch-retry-factor">fetch-retry-factor</h3>
<ul>
<li>Default: 10</li>
<li>Type: Number</li>
</ul>
<p>The &quot;factor&quot; config for the <code>retry</code> module to use when fetching
packages.</p>
<h3 id="fetch-retry-mintimeout">fetch-retry-mintimeout</h3>
<ul>
<li>Default: 10000 (10 seconds)</li>
<li>Type: Number</li>
</ul>
<p>The &quot;minTimeout&quot; config for the <code>retry</code> module to use when fetching
packages.</p>
<h3 id="fetch-retry-maxtimeout">fetch-retry-maxtimeout</h3>
<ul>
<li>Default: 60000 (1 minute)</li>
<li>Type: Number</li>
</ul>
<p>The &quot;maxTimeout&quot; config for the <code>retry</code> module to use when fetching
packages.</p>
<h3 id="git">git</h3>
<ul>
<li>Default: <code>&quot;git&quot;</code></li>
<li>Type: String</li>
</ul>
<p>The command to use for git commands.  If git is installed on the
computer, but is not in the <code>PATH</code>, then set this to the full path to
the git binary.</p>
<h3 id="git-tag-version">git-tag-version</h3>
<ul>
<li>Default: <code>true</code></li>
<li>Type: Boolean</li>
</ul>
<p>Tag the commit when using the <code>npm version</code> command.</p>
<h3 id="global">global</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Operates in &quot;global&quot; mode, so that packages are installed into the
<code>prefix</code> folder instead of the current working directory.  See
<code><a href="../files/npm-folders.html">npm-folders(5)</a></code> for more on the differences in behavior.</p>
<ul>
<li>packages are installed into the <code>{prefix}/lib/node_modules</code> folder, instead of the
current working directory.</li>
<li>bin files are linked to <code>{prefix}/bin</code></li>
<li>man pages are linked to <code>{prefix}/share/man</code></li>
</ul>
<h3 id="globalconfig">globalconfig</h3>
<ul>
<li>Default: {prefix}/etc/npmrc</li>
<li>Type: path</li>
</ul>
<p>The config file to read for global config options.</p>
<h3 id="group">group</h3>
<ul>
<li>Default: GID of the current process</li>
<li>Type: String or Number</li>
</ul>
<p>The group to use when running package scripts in global mode as the root
user.</p>
<h3 id="heading">heading</h3>
<ul>
<li>Default: <code>&quot;npm&quot;</code></li>
<li>Type: String</li>
</ul>
<p>The string that starts all the debugging log output.</p>
<h3 id="https-proxy">https-proxy</h3>
<ul>
<li>Default: null</li>
<li>Type: url</li>
</ul>
<p>A proxy to use for outgoing https requests. If the <code>HTTPS_PROXY</code> or
<code>https_proxy</code> or <code>HTTP_PROXY</code> or <code>http_proxy</code> environment variables are set,
proxy settings will be honored by the underlying <code>request</code> library.</p>
<h3 id="if-present">if-present</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If true, npm will not exit with an error code when <code>run-script</code> is invoked for
a script that isn&#39;t defined in the <code>scripts</code> section of <code>package.json</code>. This
option can be used when it&#39;s desirable to optionally run a script when it&#39;s
present and fail if the script fails. This is useful, for example, when running
scripts that may only apply for some builds in an otherwise generic CI setup.</p>
<h3 id="ignore-scripts">ignore-scripts</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If true, npm does not run scripts specified in package.json files.</p>
<h3 id="init-module">init-module</h3>
<ul>
<li>Default: ~/.npm-init.js</li>
<li>Type: path</li>
</ul>
<p>A module that will be loaded by the <code>npm init</code> command.  See the
documentation for the
<a href="https://github.com/isaacs/init-package-json">init-package-json</a> module
for more information, or <a href="../cli/npm-init.html">npm-init(1)</a>.</p>
<h3 id="init-author-name">init-author-name</h3>
<ul>
<li>Default: &quot;&quot;</li>
<li>Type: String</li>
</ul>
<p>The value <code>npm init</code> should use by default for the package author&#39;s name.</p>
<h3 id="init-author-email">init-author-email</h3>
<ul>
<li>Default: &quot;&quot;</li>
<li>Type: String</li>
</ul>
<p>The value <code>npm init</code> should use by default for the package author&#39;s email.</p>
<h3 id="init-author-url">init-author-url</h3>
<ul>
<li>Default: &quot;&quot;</li>
<li>Type: String</li>
</ul>
<p>The value <code>npm init</code> should use by default for the package author&#39;s homepage.</p>
<h3 id="init-license">init-license</h3>
<ul>
<li>Default: &quot;ISC&quot;</li>
<li>Type: String</li>
</ul>
<p>The value <code>npm init</code> should use by default for the package license.</p>
<h3 id="init-version">init-version</h3>
<ul>
<li>Default: &quot;1.0.0&quot;</li>
<li>Type: semver</li>
</ul>
<p>The value that <code>npm init</code> should use by default for the package
version number, if not already set in package.json.</p>
<h3 id="json">json</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Whether or not to output JSON data, rather than the normal output.</p>
<p>This feature is currently experimental, and the output data structures
for many commands is either not implemented in JSON yet, or subject to
change.  Only the output from <code>npm ls --json</code> is currently valid.</p>
<h3 id="key">key</h3>
<ul>
<li>Default: <code>null</code></li>
<li>Type: String</li>
</ul>
<p>A client key to pass when accessing the registry.  Values should be in PEM
format with newlines replaced by the string &quot;\n&quot;. For example:</p>
<pre><code>key=&quot;-----BEGIN PRIVATE KEY-----\nXXXX\nXXXX\n-----END PRIVATE KEY-----&quot;
</code></pre><p>It is <em>not</em> the path to a key file (and there is no &quot;keyfile&quot; option).</p>
<h3 id="link">link</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If true, then local installs will link if there is a suitable globally
installed package.</p>
<p>Note that this means that local installs can cause things to be
installed into the global space at the same time.  The link is only done
if one of the two conditions are met:</p>
<ul>
<li>The package is not already installed globally, or</li>
<li>the globally installed version is identical to the version that is
being installed locally.</li>
</ul>
<h3 id="local-address">local-address</h3>
<ul>
<li>Default: undefined</li>
<li>Type: IP Address</li>
</ul>
<p>The IP address of the local interface to use when making connections
to the npm registry.  Must be IPv4 in versions of Node prior to 0.12.</p>
<h3 id="loglevel">loglevel</h3>
<ul>
<li>Default: &quot;warn&quot;</li>
<li>Type: String</li>
<li>Values: &quot;silent&quot;, &quot;error&quot;, &quot;warn&quot;, &quot;http&quot;, &quot;info&quot;, &quot;verbose&quot;, &quot;silly&quot;</li>
</ul>
<p>What level of logs to report.  On failure, <em>all</em> logs are written to
<code>npm-debug.log</code> in the current working directory.</p>
<p>Any logs of a higher level than the setting are shown.
The default is &quot;warn&quot;, which shows warn and error output.</p>
<h3 id="logstream">logstream</h3>
<ul>
<li>Default: process.stderr</li>
<li>Type: Stream</li>
</ul>
<p>This is the stream that is passed to the
<a href="https://github.com/npm/npmlog">npmlog</a> module at run time.</p>
<p>It cannot be set from the command line, but if you are using npm
programmatically, you may wish to send logs to somewhere other than
stderr.</p>
<p>If the <code>color</code> config is set to true, then this stream will receive
colored output if it is a TTY.</p>
<h3 id="long">long</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Show extended information in <code>npm ls</code> and <code>npm search</code>.</p>
<h3 id="maxsockets">maxsockets</h3>
<ul>
<li>Default: 50</li>
<li>Type: Number</li>
</ul>
<p>The maximum number of connections to use per origin (protocol/host/port
combination). Passed to the <code>http</code> <code>Agent</code> used to make the request.</p>
<h3 id="message">message</h3>
<ul>
<li>Default: &quot;%s&quot;</li>
<li>Type: String</li>
</ul>
<p>Commit message which is used by <code>npm version</code> when creating version commit.</p>
<p>Any &quot;%s&quot; in the message will be replaced with the version number.</p>
<h3 id="node-version">node-version</h3>
<ul>
<li>Default: process.version</li>
<li>Type: semver or false</li>
</ul>
<p>The node version to use when checking a package&#39;s <code>engines</code> map.</p>
<h3 id="npat">npat</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Run tests on installation.</p>
<h3 id="onload-script">onload-script</h3>
<ul>
<li>Default: false</li>
<li>Type: path</li>
</ul>
<p>A node module to <code>require()</code> when npm loads.  Useful for programmatic
usage.</p>
<h3 id="optional">optional</h3>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>Attempt to install packages in the <code>optionalDependencies</code> object.  Note
that if these packages fail to install, the overall installation
process is not aborted.</p>
<h3 id="parseable">parseable</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Output parseable results from commands that write to
standard output.</p>
<h3 id="prefix">prefix</h3>
<ul>
<li>Default: see <a href="../files/npm-folders.html">npm-folders(5)</a></li>
<li>Type: path</li>
</ul>
<p>The location to install global items.  If set on the command line, then
it forces non-global commands to run in the specified folder.</p>
<h3 id="production">production</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Set to true to run in &quot;production&quot; mode.</p>
<ol>
<li>devDependencies are not installed at the topmost level when running
local <code>npm install</code> without any arguments.</li>
<li>Set the NODE_ENV=&quot;production&quot; for lifecycle scripts.</li>
</ol>
<h3 id="proprietary-attribs">proprietary-attribs</h3>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>Whether or not to include proprietary extended attributes in the
tarballs created by npm.</p>
<p>Unless you are expecting to unpack package tarballs with something other
than npm -- particularly a very outdated tar implementation -- leave
this as true.</p>
<h3 id="proxy">proxy</h3>
<ul>
<li>Default: null</li>
<li>Type: url</li>
</ul>
<p>A proxy to use for outgoing http requests. If the <code>HTTP_PROXY</code> or
<code>http_proxy</code> environment variables are set, proxy settings will be
honored by the underlying <code>request</code> library.</p>
<h3 id="rebuild-bundle">rebuild-bundle</h3>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>Rebuild bundled dependencies after installation.</p>
<h3 id="registry">registry</h3>
<ul>
<li>Default: <a href="https://registry.npmjs.org/">https://registry.npmjs.org/</a></li>
<li>Type: url</li>
</ul>
<p>The base URL of the npm package registry.</p>
<h3 id="rollback">rollback</h3>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>Remove failed installs.</p>
<h3 id="save">save</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Save installed packages to a package.json file as dependencies.</p>
<p>When used with the <code>npm rm</code> command, it removes it from the <code>dependencies</code>
object.</p>
<p>Only works if there is already a package.json file present.</p>
<h3 id="save-bundle">save-bundle</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If a package would be saved at install time by the use of <code>--save</code>,
<code>--save-dev</code>, or <code>--save-optional</code>, then also put it in the
<code>bundleDependencies</code> list.</p>
<p>When used with the <code>npm rm</code> command, it removes it from the
bundledDependencies list.</p>
<h3 id="save-dev">save-dev</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Save installed packages to a package.json file as <code>devDependencies</code>.</p>
<p>When used with the <code>npm rm</code> command, it removes it from the
<code>devDependencies</code> object.</p>
<p>Only works if there is already a package.json file present.</p>
<h3 id="save-exact">save-exact</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Dependencies saved to package.json using <code>--save</code>, <code>--save-dev</code> or
<code>--save-optional</code> will be configured with an exact version rather than
using npm&#39;s default semver range operator.</p>
<h3 id="save-optional">save-optional</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Save installed packages to a package.json file as
optionalDependencies.</p>
<p>When used with the <code>npm rm</code> command, it removes it from the
<code>devDependencies</code> object.</p>
<p>Only works if there is already a package.json file present.</p>
<h3 id="save-prefix">save-prefix</h3>
<ul>
<li>Default: &#39;^&#39;</li>
<li>Type: String</li>
</ul>
<p>Configure how versions of packages installed to a package.json file via
<code>--save</code> or <code>--save-dev</code> get prefixed.</p>
<p>For example if a package has version <code>1.2.3</code>, by default its version is
set to <code>^1.2.3</code> which allows minor upgrades for that package, but after
<code>npm config set save-prefix=&#39;~&#39;</code> it would be set to <code>~1.2.3</code> which only allows
patch upgrades.</p>
<h3 id="scope">scope</h3>
<ul>
<li>Default: &quot;&quot;</li>
<li>Type: String</li>
</ul>
<p>Associate an operation with a scope for a scoped registry. Useful when logging
in to a private registry for the first time:
<code>npm login --scope=@organization --registry=registry.organization.com</code>, which
will cause <code>@organization</code> to be mapped to the registry for future installation
of packages specified according to the pattern <code>@organization/package</code>.</p>
<h3 id="searchopts">searchopts</h3>
<ul>
<li>Default: &quot;&quot;</li>
<li>Type: String</li>
</ul>
<p>Space-separated options that are always passed to search.</p>
<h3 id="searchexclude">searchexclude</h3>
<ul>
<li>Default: &quot;&quot;</li>
<li>Type: String</li>
</ul>
<p>Space-separated options that limit the results from search.</p>
<h3 id="searchsort">searchsort</h3>
<ul>
<li>Default: &quot;name&quot;</li>
<li>Type: String</li>
<li>Values: &quot;name&quot;, &quot;-name&quot;, &quot;date&quot;, &quot;-date&quot;, &quot;description&quot;,
&quot;-description&quot;, &quot;keywords&quot;, &quot;-keywords&quot;</li>
</ul>
<p>Indication of which field to sort search results by.  Prefix with a <code>-</code>
character to indicate reverse sort.</p>
<h3 id="shell">shell</h3>
<ul>
<li>Default: SHELL environment variable, or &quot;bash&quot; on Posix, or &quot;cmd&quot; on
Windows</li>
<li>Type: path</li>
</ul>
<p>The shell to run for the <code>npm explore</code> command.</p>
<h3 id="shrinkwrap">shrinkwrap</h3>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>If set to false, then ignore <code>npm-shrinkwrap.json</code> files when
installing.</p>
<h3 id="sign-git-tag">sign-git-tag</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If set to true, then the <code>npm version</code> command will tag the version
using <code>-s</code> to add a signature.</p>
<p>Note that git requires you to have set up GPG keys in your git configs
for this to work properly.</p>
<h3 id="spin">spin</h3>
<ul>
<li>Default: true</li>
<li>Type: Boolean or <code>&quot;always&quot;</code></li>
</ul>
<p>When set to <code>true</code>, npm will display an ascii spinner while it is doing
things, if <code>process.stderr</code> is a TTY.</p>
<p>Set to <code>false</code> to suppress the spinner, or set to <code>always</code> to output
the spinner even for non-TTY outputs.</p>
<h3 id="strict-ssl">strict-ssl</h3>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>Whether or not to do SSL key validation when making requests to the
registry via https.</p>
<p>See also the <code>ca</code> config.</p>
<h3 id="tag">tag</h3>
<ul>
<li>Default: latest</li>
<li>Type: String</li>
</ul>
<p>If you ask npm to install a package and don&#39;t tell it a specific version, then
it will install the specified tag.</p>
<p>Also the tag that is added to the package@version specified by the <code>npm
tag</code> command, if no explicit tag is given.</p>
<h3 id="tag-version-prefix">tag-version-prefix</h3>
<ul>
<li>Default: <code>&quot;v&quot;</code></li>
<li>Type: String</li>
</ul>
<p>If set, alters the prefix used when tagging a new version when performing a
version increment using  <code>npm-version</code>. To remove the prefix altogether, set it
to the empty string: <code>&quot;&quot;</code>.</p>
<p>Because other tools may rely on the convention that npm version tags look like
<code>v1.0.0</code>, <em>only use this property if it is absolutely necessary</em>. In
particular, use care when overriding this setting for public packages.</p>
<h3 id="tmp">tmp</h3>
<ul>
<li>Default: TMPDIR environment variable, or &quot;/tmp&quot;</li>
<li>Type: path</li>
</ul>
<p>Where to store temporary files and folders.  All temp files are deleted
on success, but left behind on failure for forensic purposes.</p>
<h3 id="unicode">unicode</h3>
<ul>
<li>Default: true</li>
<li>Type: Boolean</li>
</ul>
<p>When set to true, npm uses unicode characters in the tree output.  When
false, it uses ascii characters to draw trees.</p>
<h3 id="unsafe-perm">unsafe-perm</h3>
<ul>
<li>Default: false if running as root, true otherwise</li>
<li>Type: Boolean</li>
</ul>
<p>Set to true to suppress the UID/GID switching when running package
scripts.  If set explicitly to false, then installing as a non-root user
will fail.</p>
<h3 id="usage">usage</h3>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Set to show short usage output (like the -H output)
instead of complete help when doing <code><a href="../cli/npm-help.html">npm-help(1)</a></code>.</p>
<h3 id="user">user</h3>
<ul>
<li>Default: &quot;nobody&quot;</li>
<li>Type: String or Number</li>
</ul>
<p>The UID to set to when running package scripts as root.</p>
<h3 id="userconfig">userconfig</h3>
<ul>
<li>Default: ~/.npmrc</li>
<li>Type: path</li>
</ul>
<p>The location of user-level configuration settings.</p>
<h3 id="umask">umask</h3>
<ul>
<li>Default: 022</li>
<li>Type: Octal numeric string in range 0000..0777 (0..511)</li>
</ul>
<p>The &quot;umask&quot; value to use when setting the file creation mode on files
and folders.</p>
<p>Folders and executables are given a mode which is <code>0777</code> masked against
this value.  Other files are given a mode which is <code>0666</code> masked against
this value.  Thus, the defaults are <code>0755</code> and <code>0644</code> respectively.</p>
<h3 id="user-agent">user-agent</h3>
<ul>
<li>Default: node/{process.version} {process.platform} {process.arch}</li>
<li>Type: String</li>
</ul>
<p>Sets a User-Agent to the request header</p>
<h3 id="version">version</h3>
<ul>
<li>Default: false</li>
<li>Type: boolean</li>
</ul>
<p>If true, output the npm version and exit successfully.</p>
<p>Only relevant when specified explicitly on the command line.</p>
<h3 id="versions">versions</h3>
<ul>
<li>Default: false</li>
<li>Type: boolean</li>
</ul>
<p>If true, output the npm version as well as node&#39;s <code>process.versions</code> map, and
exit successfully.</p>
<p>Only relevant when specified explicitly on the command line.</p>
<h3 id="viewer">viewer</h3>
<ul>
<li>Default: &quot;man&quot; on Posix, &quot;browser&quot; on Windows</li>
<li>Type: path</li>
</ul>
<p>The program to use to view help content.</p>
<p>Set to <code>&quot;browser&quot;</code> to view html help content in the default web browser.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../files/npmrc.html">npmrc(5)</a></li>
<li><a href="../misc/npm-scripts.html">npm-scripts(7)</a></li>
<li><a href="../files/npm-folders.html">npm-folders(5)</a></li>
<li><a href="../cli/npm.html">npm(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-config &mdash; npm@2.15.12</p>

