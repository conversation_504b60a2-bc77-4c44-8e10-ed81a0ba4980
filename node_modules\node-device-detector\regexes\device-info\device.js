module.exports = {
  "360": {
    "1503-a01": "->n4",
    "1509-a00": "->q5 plus",
    "1603-a03": "->n4a",
    "1607-a01": "->n5s",
    "1807-a01": "->n7",
    "n4": "DS=5.5;RS=1080x1920;SZ=75.7x149x8.3;WT=140;RE=2016;OI=1;OV=6.0;RM=4096;CP=194;",
    "n4a": "DS=5.5;RS=1080x1920;SZ=75.6x150x8.2;WT=167;RE=2016;OI=1;OV=6.0;RM=3072;CP=118;",
    "n5s": "DS=5.5;RS=1080x1920;SZ=76.9x153.5x8;WT=170;RE=2017;OI=1;OV=7.1;RM=6144;CP=196;SM=2;",
    "q5 plus": "DS=6;RS=1080x1920;SZ=80x156.5x8.3;WT=175;RE=2016;OI=1;OV=6.0;RM=4096;CP=173;",
    "n7": "DS=5.99;RS=1080x2160;SZ=75.2x155.9x8.35;WT=190;RM=6144;CP=61;RE=2018.05;OI=1;OV=8.1;SM=2;",
    "1803-a01": "->n7 lite",
    "n7 lite": "DS=5.99;RS=1080x2160;SZ=75.2x155.9x8.35;WT=180;RE=2018.08;RM=6144;CP=61;OI=1;OV=8.1;SM=2;",
    "1809-a01": "->n7 pro",
    "n7 pro": "DS=5.99;RS=1080x2160;SZ=73.9x153.5x7.7;WT=182;RE=2018.08;RM=6144;CP=62;OI=1;OV=8.1;SM=2;",
    "1713-a01": "->n6 lite",
    "n6 lite": "DS=5.5;RS=1080x1920;SZ=75.94x152.35x8.25;WT=160;RE=2017.12;RM=4096;CP=19;OI=1;OV=7.1;SM=2;",
    "1707-a01": "->n6",
    "n6": "DS=5.93;RS=1080x2160;SZ=75.9x157.8x8.7;WT=182;RE=2017.12;RM=4096;CP=19;OI=1;OV=7.1;SM=2;",
    "1801-a01": "->n6 pro",
    "n6 pro": "DS=6;RS=1080x2160;SZ=75.64x157.2x8.15;WT=182;RE=2017.11;RM=6144;CP=19;OI=1;OV=7.1;SM=2;",
    "n4s": "DS=5.5;RS=1080x1920;SZ=74.6x151.3x8.35;WT=169;RE=2016;OI=1;OV=6;RM=4096;CP=194;",
    "1505-a02": "->n4s",
    "1505-a01": "->n4s",
    "n5": "DS=5.5;RS=1080x1920;SZ=76x152x8.25;WT=156;RE=2017;OI=1;OV=6;RM=6144;CP=196;",
    "1605-a01": "->n5"
  },
  "8848": {
    "m3": "RE=2016.08;CP=115;RM=4096;WT=220;DS=5;RS=1080x1920;SM=1;",
    "m4": "RE=2017.08;CP=173;RM=6144;DS=5.15;RS=1080x1920;OI=1;OV=7.0;SM=2;",
    "m5": "RE=2018.08;CP=25;RM=6144;WT=228;DS=5.65;RS=1080x2160;OI=1;OV=8.1;SM=2;SZ=73.9x167.5x11.9;",
    "m6": "RM=12288;CP=141;OI=1;OV=10;DS=6.01;RS=1080x2340;"
  },
  "2e": {
    "e450 (2018)": "DS=4.5;RS=480x854;SZ=74.2x133x8.7;WT=133;RM=1024;CP=58;OI=1;OV=8.1;",
    "e450 2018": "->e450 (2018)",
    "e500a (2019)": "DS=5;RS=480x960;SZ=72x142x8.7;WT=149;RM=1024;CP=58;OI=1;OV=8.1;SM=2;",
    "e500a 2019": "->e500a (2019)"
  },
  "3gnet": {
    "t 900": "->t900",
    "t800": "",
    "t900": "",
    "u6s": "",
    "u8": ""
  },
  "malata": {
    "i5": "OI=1;OV=2.3.6;SZ=61.24x115.6x11.95;WT=128"
  },
  "3q": {
    "ac0731b": "->qoo! q-pad",
    "ac0732c": "->qoo! lite",
    "ac1024c": "->qoo! q-pad",
    "ac7803c": "->qoo! lite",
    "bc9710am": "->qoo! q-pad",
    "el72b": "->qoo! q-book",
    "er71b": "->qoo! q-book",
    "lc0720c": "->qoo! q-pad",
    "lc0723b": "->qoo! p-pad",
    "lc0725b": "->qoo! p-pad",
    "lc0804b": "->qoo! p-pad",
    "lc0810c": "->qoo! p-pad",
    "lc0901d": "->qoo! p-pad",
    "lc1016c": "->qoo! q-pad",
    "mt0724b": "->qoo! p-pad",
    "mt0729b": "->qoo! q-pad",
    "mt0729d": "->qoo! q-pad",
    "mt0739d": "->qoo! q-pad",
    "mt0811b": "->qoo! q-pad",
    "mt0812e": "->qoo! meta",
    "mt7801c": "->qoo! surf",
    "oc1020a": "->qoo! surf",
    "qoo! lite": "DS=7.0;RS=1280x800;SZ=190x113x8;WT=320;RM=1024;CP=201;OI=1;OV=4.2;",
    "qoo! meta": "",
    "qoo! q-book": "",
    "qoo! q-pad": "DS=7.0;RS=800x480;RM=512;CP=31;OI=1;OV=4.0;SZ=200x120x11;WT=360;",
    "qoo! surf": "",
    "qs0715c": "->qoo! q-pad;SZ=200x120x11;WT=360;",
    "qs0716d": "->qoo! q-pad;SZ=192x119x11;WT=369;CP=49;",
    "qs0717d": "->qoo! q-pad;RS=1024x600;SZ=195x122x10;WT=406;CP=49;",
    "qs0728c": "->qoo! q-pad;RS=1024x600;SZ=195x122x10;WT=406;",
    "qs0730c": "->qoo! q-pad;RS=1024x600;SZ=196x123x11;WT=320;",
    "qs0815c": "->qoo! q-pad;DS=8.0;RS=1024x768;SZ=205x157x10;WT=533;SM=2;",
    "qs1023h": "->qoo! q-pad;DS=10.1;RS=1280x800;RM=1024;SZ=262x176x10;WT=665;OI=1;OV=4.1;CP=49;",
    "qs9715f": "->qoo! q-pad;DS=9.7;RS=1024x768;SZ=243x188x11;WT=670;RM=1024;CP=202;OI=1;OV=4.1;",
    "qs9718c": "->qoo! surf;DS=9.7;RS=1024x768;SZ=242x188x10;WT=670;RM=512;CP=31;OI=1;OV=4.0;",
    "qs9719d": "->qoo! surf;DS=9.7;RS=1024x768;SZ=242x188x10;WT=670;RM=512;CP=49;OI=1;OV=4.0;",
    "rc0709b": "->qoo! q-pad;DS=7.0;SZ=192x119x10;WT=340;RM=1024;CP=177;",
    "rc0710b": "->qoo! q-pad;SZ=192x118x11;WT=332;CP=177;",
    "rc0718c": "->qoo! q-pad",
    "rc0719h": "->qoo! q-pad",
    "rc0721b": "->qoo! q-pad",
    "rc0722c": "->qoo! q-pad",
    "rc0726b": "->qoo! q-pad",
    "rc0734h": "->qoo! q-pad",
    "rc0743h": "->qoo! q-pad",
    "rc0813c": "->qoo! q-pad",
    "rc0817c": "->qoo! q-pad",
    "rc1018c": "->qoo! q-pad",
    "rc1019g": "->qoo! q-pad",
    "rc1025f": "->qoo! q-pad",
    "rc1301c": "->qoo! q-pad",
    "rc7802f": "->qoo! meta",
    "rc9711b": "->qoo! q-pad",
    "rc9712c": "->qoo! q-pad",
    "rc9716b": "->qoo! q-pad",
    "rc9717b": "->qoo! q-pad",
    "rc9724c": "->qoo! q-pad",
    "rc9726c": "->qoo! q-pad",
    "rc9727f": "->qoo! q-pad",
    "rc9730c": "->qoo! q-pad",
    "rc9731c": "->qoo! q-pad",
    "surf ts1009b": "",
    "ts0807b": "->qoo! q-pad",
    "ts1013b": "->qoo! q-pad",
    "ts9708b": "->surf ts1009b",
    "vm0711a": "->qoo! surf",
    "vm1017a": "->qoo! q-pad"
  },
  "4good": {
    "light a103": "DS=4.5;RS=540x960;SZ=74.2x133x8.7;WT=133;RM=1024;CP=9;RE=2017.02;OI=1;OV=6;SM=2;",
    "light b100": "DS=4.5;RS=540x960;SZ=67x134x8.8;WT=121;RE=2017;OI=1;OV=7.0;RM=1024;CP=86;",
    "people g410": "DS=5;RS=1080x1920;SZ=72x146x8;WT=131;RE=2016.10;OI=1;OV=6.0;RM=1024;CP=5;",
    "people g503": "DS=5.5;RS=720x1280;SZ=77.5x154x10.5;WT=171;RE=2016;OI=1;OV=6.0;RM=1024;CP=5;",
    "people gt300": "DS=10.1;RS=1280x800;SM=2;SZ=241x170x10;WT=530;RE=;OI=1;OV=7.0;RM=1024;CP=93;",
    "people": "DS=5.5;RS=720x1280;SZ=77x156x8.9;WT=162;RE=2016;OI=1;OV=5.1;RM=1024;CP=56;",
    "s450m 4g": "DS=4.5;RS=480x854;SZ=65x130x9;WT=153;SM=2;RE=2016.02;OI=1;OV=5.1;RM=1024;CP=310;",
    "s501m 3g": "DS=5;RS=480x854;SZ=72x145x8;WT=167;SM=2;OI=1;OV=4.4;RM=512;CP=24;",
    "s555m 4g": "->people",
    "light a104": "DS=5;RM=1024;RS=720x1280;OI=1;OV=7;CP=56;SM=2;WT=149;SZ=72x143x9.4;RE=2017.05;",
    "t700i 3g": "DS=7;RS=1024x600;OI=1;OV=4.4;RM=512;WT=269;SZ=188x108x10;CP=311;"
  },
  "4ife": {
    "4k smart tv box": "",
    "4k tv box": "->4k smart tv box"
  },
  "a1": {
    "alpha 20 plus": "",
    "alpha 20+": "->alpha 20 plus",
    "alpha": ""
  },
  "ace": {
    "buzz 1 lite": "DS=4.97;RS=480x960;SZ=75.9x159x9.9;WT=155;RE=2019;",
    "buzz 2 lite": "DS=6.1;RS=720x1440;SZ=76.6x159.6x9.7;WT=170;RE=2020;",
    "buzz 1 plus": "DS=5.47;RS=720x1440;SZ=75.9x159.6x9.9;WT=162;RE=2019;",
    "buzz 2 plus": "DS=6.1;RS=720x1440;SZ=73.6x155.6x9.5;WT=162;RE=2020;",
    "buzz 1": "DS=5;RS=480x960;SZ=75.9x159.6x9.9;WT=155;RE=2019;",
    "buzz 2": "",
    "urban 1": "DS=5.47;RS=720x1440;SZ=75.9x159x9.9;WT=162;RE=2018;",
    "urban 1 pro": "DS=5.7;RS=720x1440;SZ=75.9x159x9.9;WT=162;RE=2019;",
    "clever 1": "DS=6.19;RS=720x1440;SZ=75.9x159x9.9;WT=190;RE=2019;"
  },
  "acer": {
    "a1-810": "->iconia a",
    "a1-811": "->iconia a",
    "a1-830": "->iconia a1",
    "a101": "->vangogh",
    "a200": "->picasso e",
    "a210": "->iconia tab a210",
    "a3-a10": "->iconia a3",
    "a3-a11": "->iconia a3",
    "a3-a30": "->iconia tab 10",
    "a3-a40": "->iconia tab 10",
    "a500": "->picasso",
    "a501": "->picasso",
    "a510": "->iconia tab a510",
    "a511": "->iconia tab a511",
    "a700": "->iconia tab a700",
    "a701": "->iconia tab a701",
    "acer-z110": "->liquid z110",
    "allegro": "DS=3.6;RS=480x800;SZ=59x116x13;WT=126;RE=2011.10;RM=512;CP=49;OI=4;OV=7.5;SM=1;",
    "aspire e5-421g": "DS=14;SZ=345.44x228.6x25.4;RS=1366x768;WT=2299.7;CP=92;RE=2014.10;RM=8192;OS=Windows 8.1;",
    "aspire e5-421g-88jf": "->aspire e5-421g",
    "aspire e5-511": "",
    "aspire v5-121": "",
    "aspire v5-573g": "",
    "aspire xc-704g": "",
    "b1-710": "->iconia b1",
    "b1-711": "->iconia b1",
    "b1-720": "->iconia b1",
    "b1-721": "->iconia b1",
    "b1-723": "->iconia talk 7",
    "b1-730hd": "->iconia one 7",
    "b1-733": "->iconia talk 7",
    "b1-750": "->iconia one 7",
    "b1-760hd": "->iconia one 7",
    "b1-770": "->iconia one 7",
    "b1-780": "->iconia one 7",
    "b1-7a0": "->iconia one 7",
    "b1-820": "->iconia one 8",
    "b1-830": "->iconia one 8",
    "b1-850": "->iconia one 8",
    "b1-860a": "->iconia one 8",
    "b1-870": "->iconia one 8",
    "b1-a71": "->iconia b1",
    "b3-a10": "->iconia one 10",
    "b3-a20": "->iconia one 10",
    "b3-a30": "->iconia one 10",
    "b3-a32": "->iconia one 10",
    "b3-a40": "->iconia one 10",
    "b3-a40fhd": "->iconia one 10",
    "b3-a42": "->iconia one 10",
    "b3-a50": "->iconia one 10",
    "b3-a50fhd": "->iconia one 10",
    "chromebo14 (cb3-431)": "->chromebook 14",
    "chromebook 14": "",
    "chromebook r11": "",
    "chromebook r13": "",
    "da220hql": "",
    "da241hl": "",
    "e310": "->liquid mini",
    "e320-orange": "->liquid express",
    "e320": "->liquid express",
    "e39": "->liquid e700",
    "iconia a": "DS=7.9;RS=1024x768;SZ=209x146x11;WT=410;RE=2013.05;RM=1024;CP=12;OI=1;OV=4.2;",
    "iconia tab a1-810": "->iconia a",
    "iconia one 10": "",
    "iconia one 7": "",
    "iconia tab 10": "",
    "iconia tab 7": "",
    "iconia tab a701": "",
    "iconia talk 7": "",
    "liquid e1 duo": "",
    "liquid e2 duo": "",
    "liquid mini": "",
    "liquid z110": "",
    "liquid z4 duo": "",
    "liquid z5 duo": "",
    "liquid z520": "",
    "liquid zest 4g": "",
    "liquid zest": "",
    "neotouch p400": "DS=3.2;RS=320x480;SZ=59.3x115x12;WT=125;RE=2010.02;RM=256;CP=145;OS=Windows Mobile 6.5.3;",
    "neotouch s200": "DS=3.8;RS=480x800;SZ=63x118.6x12;WT=130;RE=2009.10;RM=256;CP=150;OS=Windows Mobile 6.5;",
    "one 10": "",
    "picasso e": "",
    "picasso": "",
    "predator g9-793": "",
    "pro80": "",
    "s510": "->liquid s1",
    "s520": "->liquid s2",
    "s55": "->liquid jade",
    "t02": "->liquid z530",
    "t03": "->liquid z630",
    "t04": "->liquid z630s",
    "t06": "->liquid zest",
    "t07": "->liquid zest 4g",
    "t08": "->liquid zest plus",
    "td600": "",
    "tm01": "->liquid m330",
    "tpa60w": "",
    "v360": "->liquid e1 duo",
    "v370": "->liquid e2 duo",
    "vangogh": "",
    "z130": "->liquid z3",
    "z150": "->liquid z5 duo",
    "z160": "->liquid z4 duo",
    "z500": "->liquid z500",
    "z520": "->liquid z520"
  },
  "airo wireless": {
    "a25is": "DS=2.8;RS=240x320;SZ=68x142.4x24.4;WT=275;RE=2009.08;RM=102.4;CP=;OS=;"
  },
  "accent": {
    "cameleon a1": "",
    "cameleon c4": "",
    "cameleon c5": "",
    "cameleon c6 plus": "",
    "cameleon c6": "",
    "fast7 3g": "",
    "fast73g": "->fast7 3g",
    "neon": "",
    "pearl a2": "",
    "pearl a4 lite": "",
    "pearl a5": "",
    "pearl a6": "",
    "pearl a7": "",
    "speed a2": "",
    "speed m2": "",
    "speed x2 plus": "",
    "speed x2": "",
    "speed y2": "",
    "speed-m2": "->speed m2",
    "speed-x2+": "->speed x2 plus",
    "speed-x2": "->speed x2",
    "speed-y2": "->speed y2",
    "tank p55": ""
  },
  "acteck": {
    "bleck": ""
  },
  "advan": {
    "5041": "",
    "5059": "",
    "5061": "",
    "i lite i7u": "",
    "i4u": "",
    "i55d": "",
    "i55k": "",
    "i5e": "",
    "i5k": "",
    "i7d": "",
    "i7u": "->i lite i7u",
    "m4": "",
    "s4+": "",
    "s40": "",
    "s45e": "",
    "s4z": "",
    "s50h": "",
    "s5e nxt": "",
    "s5j+": "",
    "s7d": ""
  },
  "advance": {
    "hl4936": "->hollogram hl4936",
    "hl5575": "->hollogram hl5575",
    "hl5667": "->hollogram hl5667",
    "hl5767": "->hollogram hl5767",
    "hl6246": "->hollogram hl6246",
    "hl6575": "->hollogram hl6575",
    "hollogram hi 4934": "",
    "hollogram hl4936": "",
    "hollogram hl5446": "",
    "hollogram hl5575": "",
    "hollogram hl5667": "",
    "hollogram hl5767": "",
    "hollogram hl6246": "",
    "hollogram hl6575": "",
    "intro": "",
    "introtr3544": "->intro",
    "pr5650": "->prime pr5650",
    "pr5950": "->prime pr5950",
    "pr6020": "->prime pr6020",
    "pr6070": "->prime pr6070",
    "pr6145": "->prime pr6145",
    "pr6146": "->prime pr6146",
    "pr6150": "->prime pr6150",
    "prime pr5650": "",
    "prime pr5950": "",
    "prime pr6020": "",
    "prime pr6070": "",
    "prime pr6145": "",
    "prime pr6146": "",
    "prime pr6150": ""
  },
  "afrione": {
    "champion pro": "",
    "championpro": "->champion pro",
    "cygnus x": "",
    "cygnus": "",
    "cygnusx": "->cygnus x",
    "gravity z2": ""
  },
  "agm": {
    "a8 se": "RE=2017.05;DS=5;RS=720x1280;RM=2048;CP=17;SM=2;SZ=83x159x16;WT=247;OI=1;OV=7.0;",
    "a9": "RE=2018.10;DS=5.99;RS=1080x2160;RM=4096;CP=214;WT=251;SZ=81.4x168.0x12.6;OI=1;OV=8.1;",
    "a8": "RE=2017.02;DS=5;RS=720x1280;RM=3072;CP=17;SZ=83x159x16;WT=247;OI=1;OV=7.0;",
    "x1": "RE=2016.10;DS=5.5;RS=1080x1920;RM=4096;CP=65;SZ=79.1x163x11.6;WT=205;OI=1;OV=5.0;",
    "x2": ""
  },
  "ainol": {
    "aurora-ii": "->novo 7 aurora ii",
    "ax10pro": "->numy ax10 pro",
    "novo 10 hero quadcore": "",
    "novo 10 hero": "RE=2012.11;RM=1024;OI=1;OV=4.1;RS=1280x800;DS=10.1;SZ=262x178x10;WT=672;CP=352;",
    "novo10 hero": "->novo 10 hero",
    "novo 10 spark": "",
    "novo 7 aurora ii": "",
    "novo 7 flame": "",
    "novo 7 numy ax1 3g": "",
    "novo 7": "",
    "novo 9 spark": "RE=2013.03;SZ=240.9x185.6x10.6;WT=642.3;DS=9.7;RS=2048x1536;OI=1;OV=4.1;CP=351;RM=2048;",
    "novo9-spark": "->novo 9 spark",
    "novo10 spark": "->novo 10 spark",
    "novo7 flame": "->novo 7 flame",
    "novo7": "->novo 7",
    "numy 3g aw1": "",
    "numy 3g ax10": "",
    "numy 3g ax10t": "->novo 7 numy ax1 3g",
    "numy 3g ax1": "->novo 7 numy ax1 3g",
    "numy 3g ax9": "",
    "numy 3g sword": "",
    "numy 3g talos 2": "",
    "numy 3g talos": "",
    "numy 3g vegas": "",
    "numy 3g ax3": "->numy ax3 sword",
    "numy 3g bw1": "->numy 3g talos 2",
    "numy 3g_sword": "->numy 3g sword",
    "numy ax10 pro": "",
    "numy ax3 sword": "",
    "numy note 9": "",
    "numy note_9": "->numy note 9",
    "numy3g ax10": "->numy 3g ax10",
    "numy3gax9": "->numy 3g ax9",
    "numy3gtalos": "->numy 3g talos"
  },
  "airness": {
    "air99": ""
  },
  "airties": {
    "air7210": ""
  },
  "ais": {
    "iris708": "->lava pro 4.5",
    "kingcomm c500": "->super smart plus x3",
    "lava pro 4.5": "",
    "super smart plus x3": ""
  },
  "lava": {
    "benco y30": "DS=5.71;RS=720x1520;SZ=71.3x147.2x9.6;WT=156;OI=1;OV=9;SM=2;CP=165;RM=1024;",
    "af9030": "->benco y30"
  },
  "aiuto": {
    "at702": ""
  },
  "aiwa": {
    "aw500": "",
    "aw790": "",
    "awm501": "",
    "awm509": "",
    "awm533": "",
    "awm561": "",
    "m300": ""
  },
  "akai": {
    "ak3219nf": "",
    "aktb-703mz": "",
    "eco e2": "",
    "glory g5": "DS=4;RS=480x800;SZ=65.3x125.8x9.5;WT=110;OI=1;OV=4.4.2;SM=1;CP=24;RM=512;RE=2015;",
    "glory l3": "",
    "glory o2": "",
    "ilike": "",
    "k40": "",
    "leon quadra": "",
    "neo": "",
    "si2157lg32": "",
    "tab 7800": "DS=7.0;RS=800x480;RE=2013.11;OI=1;OV=4.2.2;RM=512;CP=195;",
    "tab 7830": "",
    "tab 9800": "",
    "tab 9800q": "",
    "tab-7800": "->tab 7800",
    "tab-7830": "->tab 7830",
    "tab-9800": "->tab 9800",
    "tab-9800q": "->tab 9800q",
    "x6 metal": "->ilike"
  },
  "alba": {
    "57": "->5.7",
    "5.7": ""
  },
  "alcatel": {
    "8063": "->one touch pixi 4 7\" wifi",
    "8067": "->1t 7",
    "8068": "->1t 7",
    "8082": "->1t 10",
    "5002f": "->1a (2020)",
    "1a (2020)": "RE=2020.05;SZ=71.6x146.1x9.9;WT=168;DS=5.5;RS=720x1440;OI=1;OV=10;CP=229;RM=1024;",
    "5002a": "->1b (2020)",
    "5002d": "->1b (2020)",
    "5002h": "->1b (2020)",
    "1b (2020)": "RE=2020.05;SZ=71.6x146.1x9.9;WT=168;DS=5.5;RS=720x1440;OI=1;OV=10;CP=229;RM=2048;",
    "5009a": "->1c",
    "5009d": "->1c",
    "1c": "DS=5.34;RS=480x960;SZ=70.6x146.9x9.2;WT=156;OI=1;OV=7.0;SM=2;RM=1024;CP=58;RE=2019.01;",
    "4034t": "->1e",
    "1e": "DS=4;RS=480x800;SZ=64x122x9.7;WT=110;OI=1;OV=8.1;RM=1024;CP=58;",
    "5028a": "->1s (2020)",
    "5028d": "->1s (2020)",
    "1s (2020)": "RE=2020.05;SZ=74.6x158.7x8.5;WT=165;DS=6.22;RS=720x1520;OI=1;OV=10;CP=164;RM=3072;",
    "5030d": "->1se (2020)",
    "1se (2020)": "RE=2020.10;SZ=75.2x159.2x8.7;WT=175;DS=6.22;RS=720x1520;OI=1;OV=10;CP=109;RM=3072;",
    "9009g": "->1t",
    "1t": "RE=2018.02;SZ=189.5x111x9.2;WT=245;DS=7.0;RS=1024x600;OI=1;OV=8.1;CP=163;RM=1024;",
    "5007a": "->1v (2020)",
    "5007u": "->1v (2020)",
    "1v (2020)": "RE=2020.05;SZ=74.8x158.7x8.9;WT=160;DS=6.22;RS=720x1520;OI=1;OV=10;CP=164;RM=2048;",
    "5029e": "->3 (2020)",
    "3 (2020)": "RE=2020.01;DS=6.22;SZ=75x158x7;WT=165;RS=720x1520;OI=1;OV=10;RM=4096;CP=171;",
    "5006d": "->3c (2019)",
    "3c (2019)": "RE=2019.08;DS=6.7;RS=720x1440;SZ=83.4x173.4x8.1;WT=189.5;OI=1;OV=9;CP=163;RM=2048;",
    "5029d": "->3l (2020)",
    "5029y": "->3l (2020)",
    "3l (2020)": "RE=2020.05;SZ=74.6x158.7x8.5;WT=165;DS=6.22;RS=720x1520;OI=1;OV=10;CP=171;RM=4096;",
    "5039d": "->3l",
    "5039y": "->3l",
    "5034d": "->3l",
    "3l": "RE=2019.01;SZ=69.7x151.1x8;WT=145;DS=5.94;RS=720x1560;OI=1;OV=8.1;CP=8;RM=2048;",
    "8088q": "->3t 10",
    "8088x": "->3t 10",
    "3t 10": "RE=2019.01;SZ=260x156.6x9;WT=440;DS=10;RS=1280x800;OI=1;OV=9;CP=230;RM=2048;",
    "9027f": "->3t 8.0\"",
    "9027t": "->3t 8.0\"",
    "9027w": "->3t 8.0\"",
    "9027x": "->3t 8.0\"",
    "3t 8.0\"": "RE=2018.10;SZ=209.5x125x8.3;WT=279;DS=8.0;RS=1280x800;OI=1;OV=8.1;CP=231;RM=1024;",
    "4003a": "->one touch pixi 3 4\"",
    "4003j": "->one touch pixi 3 4\"",
    "4013e": "->one touch pixi 3 4\"",
    "4013j": "->one touch pixi 3 4\"",
    "4013k": "->one touch pixi 3 4\"",
    "4013m": "->one touch pixi 3 4\"",
    "4013x": "->one touch pixi 3 4\"",
    "4014a": "->one touch pixi 3 4\"",
    "4014e": "->one touch pixi 3 4\"",
    "4014k": "->one touch pixi 3 4\"",
    "4014m": "->one touch pixi 3 4\"",
    "4014x": "->one touch pixi 3 4\"",
    "4114e": "->one touch pixi 3 4\"",
    "one touch pixi 3 4\"": "RS=480x800;DS=4;CP=36;RM=2048;OI=1;OV=4.4;SZ=64.4x121.6x11.6;WT=110;RE=2015.05;",
    "4009a": "->one touch pixi 3 3.5\"",
    "4009f": "->one touch pixi 3 3.5\"",
    "4009i": "->one touch pixi 3 3.5\"",
    "4009k": "->one touch pixi 3 3.5\"",
    "4009m": "->one touch pixi 3 3.5\"",
    "4009s": "->one touch pixi 3 3.5\"",
    "4009x": "->one touch pixi 3 3.5\"",
    "one touch pixi 3 3.5\"": "RS=320x480;DS=3.5;CP=36;RM=2048;OI=1;OV=4.4;SZ=62x112.2x11.9;WT=100;RE=2015.02;",
    "4009d": "->one touch pixi 3 3.5\" dual sim",
    "4009e": "->one touch pixi 3 3.5\" dual sim",
    "one touch pixi 3 3.5\" dual sim": "->one touch pixi 3 3.5\";SM=2;",
    "4015t": "->one touch pop c1",
    "4016a": "->one touch pop c1",
    "one touch 4015a": "->one touch pop c1",
    "4015a": "->one touch pop c1",
    "one touch 4015x": "->one touch pop c1",
    "4015x": "->one touch pop c1",
    "one touch pop c1": "RS=320x480;RE=2013.09;SZ=62x112.5x12;WT=100;DS=3.5;OI=1;OV=4.2;RM=512;",
    "4016d": "->one touch pop c1 dual sim",
    "4016x": "->one touch pop c1 dual sim",
    "one touch 4015d": "->one touch pop c1 dual sim",
    "one touch 4015n": "->one touch pop c1 dual sim",
    "one touch pop c1 dual sim": "->one touch pop c1;SM=2;",
    "4017a": "->one touch pixi 4 3.5\"",
    "4017d": "->one touch pixi 4 3.5\"",
    "4017e": "->one touch pixi 4 3.5\"",
    "4017f": "->one touch pixi 4 3.5\"",
    "4017s": "->one touch pixi 4 3.5\"",
    "4017x": "->one touch pixi 4 3.5\"",
    "one touch pixi 4 3.5\"": "RE=2016.01;SZ=62x116x10;WT=120;DS=3.5;RS=320x480;OI=1;OV=5.1;CP=36;RM=512;",
    "4018a": "->one touch pop d1",
    "4018d": "->one touch pop d1",
    "4018e": "->one touch pop d1",
    "4018f": "->one touch pop d1",
    "4018m": "->one touch pop d1",
    "4018x": "->one touch pop d1",
    "one touch pop d1": "RE=2014.08;SZ=62x112.2x12;WT=101;DS=3.5;RS=320x480;OI=1;OV=4.4;CP=36;RM=512;SM=2;",
    "4024d": "->one touch pixi first",
    "4024e": "->one touch pixi first",
    "4024x": "->one touch pixi first",
    "one touch pixi first": "RE=2015.09;SZ=64.4x122.1x9.7;WT=125;DS=4.0;RS=480x800;OI=1;OV=4.4;CP=88;RM=512;",
    "4027a": "->one touch pixi 3 4.5\"",
    "4027d": "->one touch pixi 3 4.5\"",
    "4027n": "->one touch pixi 3 4.5\"",
    "4027x": "->one touch pixi 3 4.5\"",
    "4028a": "->one touch pixi 3 4.5\"",
    "4028e": "->one touch pixi 3 4.5\"",
    "4028j": "->one touch pixi 3 4.5\"",
    "4028s": "->one touch pixi 3 4.5\"",
    "5019d": "->one touch pixi 3 4.5\"",
    "one touch pixi 3 4.5\"": "RE=2015.01;SZ=65.1x132.2x10;WT=125;DS=4.5;RS=480x854;OI=1;OV=4.4;CP=36;RM=512;",
    "4032a": "->one touch pop c2",
    "4032x": "->one touch pop c2",
    "one touch pop c2": "RE=2014.07;SZ=64.4x122x12;WT=116;DS=4.0;RS=480x800;CP=36;OI=1;OV=4.2;RM=512;",
    "4032d": "->one touch pop c2 dual sim",
    "4032e": "->one touch pop c2 dual sim",
    "one touch pop c2 dual sim": "->one touch pop c2;SM=2;",
    "4034a": "->one touch pixi 4 4\"",
    "4034d": "->one touch pixi 4 4\"",
    "4034e": "->one touch pixi 4 4\"",
    "4034g": "->one touch pixi 4 4\"",
    "4034l": "->one touch pixi 4 4\"",
    "4034x": "->one touch pixi 4 4\"",
    "one touch pixi 4 4\"": "RE=2016.01;SZ=64.4x121.3x9.7;WT=;DS=4.0;RS=480x800;OI=1;OV=6;CP=58;RM=512;",
    "4035a": "->one touch pop d3",
    "4035d": "->one touch pop d3",
    "4035x": "->one touch pop d3",
    "4035y": "->one touch pop d3",
    "one touch pop d3": "DS=4;RS=480x800;SZ=64.4x121.6x12.05;WT=114;RE=2014.08;RM=1024;CP=24;OI=1;OV=4.4;",
    "4045a": "->one touch pop 2 4\"",
    "4045x": "->one touch pop 2 4\"",
    "one touch pop 2 4\"": "RE=2014.09;SZ=64.4x121.5x12.4;WT=125;DS=4.0;RS=480x800;OI=1;OV=5;CP=17;RM=512;SM=1;",
    "4045d": "->one touch pop 2 4\" dual sim",
    "4045e": "->one touch pop 2 4\" dual sim",
    "one touch pop 2 4\" dual sim": "->one touch pop 2 4\";SM=2;",
    "4047a": "->u5 plus",
    "4047d": "->u5 3g",
    "4047f": "->u5 3g",
    "4047g": "->u5 lite",
    "4047n": "->u5 3g",
    "4047x": "->u5 3g",
    "4049d": "->u3",
    "4049e": "->u3",
    "4049g": "->u3",
    "4049m": "->u3",
    "4049x": "->u3",
    "4060s": "->one touch pixi 4 4.5\"",
    "4060w": "->one touch pixi 4 4.5\"",
    "5001a": "->1v",
    "5003a": "->1c (2019)",
    "5003d": "->1c (2019)",
    "5003g": "->1c (2019)",
    "5003u": "->1c (2019)",
    "5008a": "->1x (2019)",
    "5008d": "->1x (2019)",
    "5008t": "->1x (2019)",
    "5008u": "->1x (2019)",
    "5008y": "->1x (2019)",
    "5010e": "->one touch pixi 4 5\" 3g",
    "5010g": "->one touch pixi 4 5\" 3g",
    "5010s": "->one touch pixi 4 5\" 3g",
    "5010u": "->one touch pixi 4 5\" 3g",
    "5010x": "->one touch pixi 4 5\" 3g",
    "5011a": "->a3 plus",
    "5012d": "->one touch pixi 4 5.5\" 3g",
    "5012f": "->one touch pixi 4 5.5\" 3g",
    "5012g": "->one touch pixi 4 5.5\" 3g",
    "5015a": "->one touch pop 3 5\"",
    "5015d": "->one touch pop 3 dual sim",
    "5015e": "->one touch pop 3 5\"",
    "5015x": "->one touch pop 3",
    "5016a": "->one touch pop 3 5\"",
    "5016j": "->one touch pop 3 5\"",
    "5017a": "->one touch pixi 3 4.5\" 4g",
    "5017b": "->one touch pixi 3 4.5\" 4g",
    "5017d": "->one touch pixi 3 4.5\" 4g",
    "5017e": "->one touch pixi 3 4.5\" 4g",
    "5017o": "->one touch pixi 3 4.5\" 4g",
    "5017x": "->one touch pixi 3 4.5\" 4g",
    "5022d": "->one touch pop star",
    "5022e": "->one touch pop star",
    "5023e": "->one touch pixi 4 plus power",
    "5023f": "->one touch pixi 4 plus power",
    "5024a": "->1s",
    "5024d": "->1s",
    "5024f": "->1s",
    "5024j": "->1s",
    "5025d": "->one touch pop 3 5.5\"",
    "5025e": "->one touch pop 3 5.5\"",
    "5026a": "->3c",
    "5026d": "->3c",
    "5026j": "->3c",
    "5027b": "->one touch dawn",
    "5028y": "->1s",
    "5032w": "->3v",
    "5033d": "->1",
    "5033e": "->1",
    "5033f": "->1",
    "5033g": "->1",
    "5033j": "->1",
    "5033m": "->1",
    "5033o": "->1",
    "5033q": "->1",
    "5033s": "->1",
    "5033t": "->1",
    "5033x": "->1",
    "5033y": "->1",
    "5036d": "->one touch pop c5 dual sim",
    "5038a": "->one touch pop d5",
    "5038d": "->one touch pop d5",
    "5038e": "->one touch pop d5",
    "5038x": "->one touch pop d5",
    "5041c": "->tetra",
    "5041d": "->one touch pixi 4 5.0\"",
    "5042d": "->one touch pop 2 4.5\" dual sim",
    "5042e": "->one touch pop 2 4.5\" dual sim",
    "5042f": "->one touch pop 2 4.5\"",
    "5042g": "->one touch pop 2 4.5\"",
    "5042t": "->one touch astro",
    "5042w": "->one touch pop 2 4.5\"",
    "5042x": "->one touch pop 2 4.5\"",
    "5044a": "->u5",
    "5044d": "->u5",
    "5044g": "->u50",
    "5044i": "->u5",
    "5044k": "->u5",
    "5044o": "->u5",
    "5044p": "->u5",
    "5044s": "->u50",
    "5044t": "->u5",
    "5044y": "->u5",
    "5045a": "->one touch pixi 4 5\" 4g",
    "5045f": "->one touch pixi 4 5\" 4g",
    "5045g": "->one touch pixi 4 5\" 4g",
    "5045i": "->one touch pixi 4 5\" 4g",
    "5045j": "->one touch pixi 4 5\" 4g",
    "5045y": "->one touch pixi 4 5\" 4g",
    "5046a": "->a3",
    "5046d": "->a3",
    "5046g": "->a30",
    "5046i": "->a3",
    "5046j": "->a3",
    "5046s": "->a30",
    "5046t": "->a3",
    "5046u": "->a3",
    "5046y": "->a3",
    "5047d": "->u5 hd",
    "5047i": "->u5 hd",
    "5047u": "->u5 hd",
    "5047y": "->u5 hd",
    "5048a": "->3x (2019)",
    "5048i": "->3x (2019)",
    "5048u": "->3x (2019)",
    "5048y": "->3x (2019)",
    "5049e": "->a3 plus",
    "5049g": "->a3 plus",
    "5049s": "->a30 plus",
    "5049w": "->revvl",
    "5049z": "->a30 fierce",
    "5050a": "->one touch pop s3",
    "5050s": "->one touch pop s3",
    "5050x": "->one touch pop s3",
    "5050y": "->one touch pop s3",
    "5051a": "->one touch pop 4",
    "5051d": "->one touch pop 4 dual sim",
    "5051e": "->one touch pop 4",
    "5051j": "->one touch pop 4",
    "5051m": "->one touch pop 4",
    "5051t": "->one touch pop 4",
    "5051w": "->one touch pop 4",
    "5051x": "->one touch pop 4",
    "5052d": "->3",
    "5052y": "->3",
    "5053a": "->3",
    "5053d": "->3",
    "5053k": "->3",
    "5053y": "->3",
    "5054n": "->one touch fierce xl",
    "5054t": "->one touch pop 3 5.5\"",
    "5056a": "->one touch pop 4+",
    "5056d": "->one touch pop 4+",
    "5056e": "->one touch pop 4+",
    "5056g": "->one touch pop 4+",
    "5056i": "->one touch optus x smart",
    "5056j": "->one touch pop 4+",
    "5056m": "->one touch pop 4+",
    "5056n": "->one touch fierce 4",
    "5056t": "->one touch pop 4+",
    "5056u": "->one touch pop 4+",
    "5056w": "->one touch fierce 4",
    "5057m": "->one touch pop mirage",
    "5058a": "->3x",
    "5058i": "->3x",
    "5058y": "->3x",
    "5059a": "->1x",
    "5059d": "->1x",
    "5059i": "->1x",
    "5059j": "->1x",
    "5059s": "->avalon v",
    "5059t": "->1x",
    "5059x": "->1x",
    "5059y": "->1x",
    "5060a": "->5v",
    "5060d": "->5v",
    "5060j": "->5v",
    "5061k": "->3x (2020)",
    "5061u": "->3x (2020)",
    "5065a": "->one touch pop 3 5\"",
    "5065d": "->one touch pop 3 5\"",
    "5065n": "->tru",
    "5065w": "->one touch pop 3 5\"",
    "5065x": "->one touch pop 3",
    "5070d": "->one touch pop star",
    "5080a": "->shine lite",
    "5080d": "->shine lite",
    "5080f": "->shine lite",
    "5080q": "->shine lite",
    "5080u": "->shine lite",
    "5080x": "->shine lite",
    "5085a": "->a5 led",
    "5085b": "->a5",
    "5085d": "->a5 led",
    "5085g": "->a50",
    "5085h": "->a5 led",
    "5085i": "->a5 led",
    "5085j": "->a5 led",
    "5085n": "->a5 max led",
    "5085o": "->a50",
    "5085q": "->a5",
    "5085y": "->a5 led",
    "5086a": "->5",
    "5086d": "->5",
    "5086y": "->5",
    "5090a": "->a7",
    "5090i": "->a7",
    "5090y": "->a7",
    "5095i": "->one touch pop 4s",
    "5095k": "->one touch pop 4s",
    "5095y": "->one touch pop 4s",
    "5098o": "->one touch pixi theatre",
    "5098s": "->one touch pixi 4 6\" 4g",
    "5099a": "->3v",
    "5099d": "->3v",
    "5099i": "->3v",
    "5099u": "->3v",
    "5099y": "->3v",
    "5116j": "->one touch pop 3 5\"",
    "5145a": "->one touch pixi 4 5\" 4g",
    "6016d": "->one touch idol 2 mini dual sim",
    "6016e": "->one touch idol 2 mini dual sim",
    "6016x": "->one touch idol 2 mini",
    "6036a": "->one touch idol 2 mini s",
    "6036x": "->one touch idol 2 mini s",
    "6036y": "->one touch idol 2 mini s",
    "6037b": "->one touch idol 2",
    "6037k": "->one touch idol 2",
    "6037y": "->one touch idol 2",
    "6039h": "->one touch idol 3",
    "6039k": "->one touch idol 3",
    "6039y": "->one touch idol 3",
    "6042d": "->one touch flash",
    "6043a": "->one touch idol x+",
    "6043d": "->one touch idol x+",
    "6044d": "->one touch pop up",
    "6045b": "->one touch idol 3 5.5\"",
    "6045f": "->one touch idol 3 5.5\"",
    "6045i": "->one touch idol 3 5.5\"",
    "6045o": "->one touch idol 3 5.5\"",
    "6045x": "->one touch idol 3 5.5\"",
    "6045y": "->one touch idol 3 5.5\"",
    "6050y": "->one touch idol 2s",
    "6055a": "->one touch idol 4",
    "6055b": "->one touch idol 4",
    "6055d": "->one touch idol 4",
    "6055h": "->one touch idol 4",
    "6055i": "->one touch idol 4",
    "6055k": "->one touch idol 4",
    "6055u": "->one touch idol 4",
    "6055y": "->one touch idol 4",
    "6055z": "->one touch idol 4",
    "6058a": "->one touch idol 5",
    "6058d": "->one touch idol 5",
    "6058x": "->one touch idol 5",
    "6060s": "->one touch idol 5s",
    "6060x": "->one touch idol 5s",
    "6062w": "->7",
    "6070k": "->one touch idol 4s",
    "7040a": "->one touch pop c7",
    "7040d": "->one touch pop c7 dual sim",
    "7040e": "->one touch pop c7 dual sim",
    "7040f": "->one touch pop c7",
    "7040r": "->one touch fierce 2",
    "7040t": "->one touch fierce 2",
    "7042a": "->one touch pop c7",
    "7043a": "->one touch pop 2 5\"",
    "7043e": "->one touch pop 2 5\" dual sim",
    "7043k": "->one touch pop 2 5\" dual sim",
    "7043y": "->one touch pop 2 5\"",
    "7044a": "->one touch pop 2 5\"",
    "7044x": "->one touch pop 2 5\"",
    "7045y": "->one touch pop s7",
    "7048a": "->one touch go play",
    "7048s": "->one touch go play",
    "7048w": "->one touch go play",
    "7048x": "->one touch go play",
    "7053d": "->one touch x1",
    "7055a": "->one touch hero 2c",
    "7070x": "->one touch pop 4 6\"",
    "7071a": "->a7 xl",
    "7071d": "->a7 xl",
    "7071x": "->a7 xl",
    "8030y": "->one touch hero 2",
    "8050d": "->one touch pixi 4 6\" 3g dual sim",
    "8050e": "->one touch pixi 4 6\" 3g dual sim",
    "8050g": "->one touch pixi 4 6\" 3g",
    "8050x": "->one touch pixi 4 6\" 3g",
    "9001d": "->one touch pixi 4 6\" 4g",
    "9001i": "->one touch pixi 4 6\" 4g",
    "9001x": "->one touch pixi 4 6\" 4g",
    "9002x": "->one touch pixi 3 7\"",
    "9003a": "->one touch pixi 4 7\" 3g",
    "9003x": "->one touch pixi 4 7\" 3g",
    "9005x": "->one touch pixi 8",
    "9007a": "->one touch pixi 3 7\"",
    "9007t": "->one touch pixi 3 7\" 4g",
    "9007x": "->one touch pixi 3 7\" 4g",
    "9008a": "->a3 xl",
    "9008d": "->a3 xl",
    "9008i": "->a3 xl",
    "9008j": "->a3 xl",
    "9008n": "->a3 xl",
    "9008t": "->a3 xl",
    "9008u": "->a3 xl",
    "9008x": "->a3 xl",
    "9010x": "->one touch pixi 3 10\"",
    "9020a": "->one touch trek hd",
    "9022x": "->one touch pixi 3 8\"",
    "9024o": "->one touch pixi 5",
    "9026x": "->a3 10\"",
    "9203a": "->a3 7\" 3g",
    "a1": "",
    "a1x": "",
    "a3 10\"": "",
    "a3 7\" 3g": "",
    "a3 plus": "",
    "a464bg": "->one touch pixi glitz",
    "a466bg": "->pixi unite",
    "a5 max led": "",
    "a501dl": "->a1",
    "a503dl": "->a1x",
    "a570bl": "->one touch pixi avion lte",
    "a571vl": "->one touch pixi avion 4g lte",
    "a574bl": "->raven",
    "a577vl": "->zip",
    "a5": "",
    "a621bl": "->one touch pixi glory",
    "i213": "->one touch pixi 7",
    "i216x": "->one touch pixi 7",
    "one touch 4007d": "",
    "one touch 4010a": "",
    "one touch 4012a": "",
    "one touch 4014d": "->one touch pixi 2",
    "one touch 4033e": "",
    "one touch 5020a": "",
    "one touch 5035d": "",
    "one touch 5035x": "",
    "one touch 585": "",
    "one touch 6033x": "",
    "one touch 7040k": "->one touch pop c7",
    "one touch 7041d": "->one touch pop c7 dual sim",
    "one touch 7041x": "->one touch pop c7",
    "one touch 908f orange": "",
    "one touch 918": "",
    "one touch 918a": "",
    "one touch 918s": "",
    "one touch 960c": "",
    "one touch 985": "",
    "one touch 990c+": "",
    "one touch 991": "",
    "one touch 991d": "",
    "one touch 993d": "",
    "one touch 995": "",
    "one touch 997d": "",
    "one touch astro": "",
    "one touch dawn": "",
    "one touch evo7": "",
    "one touch evo7hd": "",
    "one touch evo8hd": "",
    "one touch fierce 2": "",
    "one touch fierce 4": "",
    "one touch fierce xl": "",
    "one touch fierce": "",
    "one touch flash": "",
    "one touch go play": "",
    "one touch hero 2": "",
    "one touch hero 2c": "",
    "one touch idol 2 mini dual sim": "",
    "one touch idol 2 mini s": "",
    "one touch idol 2 mini": "",
    "one touch idol 2": "",
    "one touch idol 2s": "",
    "one touch idol 3 5.5\"": "",
    "one touch idol 3": "",
    "one touch idol 4": "",
    "one touch idol 4s": "",
    "one touch idol 5": "",
    "one touch idol 5s": "",
    "one touch idol x+": "",
    "one touch optus x smart": "",
    "one touch p310x": "->one touch pop 7",
    "one touch p320x": "->one touch pop 8",
    "one touch pixi 2": "",
    "one touch pixi 3 10\"": "",
    "one touch pixi 3 4.5\" 4g": "",
    "one touch pixi 3 7\" 4g": "",
    "one touch pixi 3 7\"": "",
    "one touch pixi 3 8\"": "",
    "one touch pixi 4 4.5\"": "",
    "one touch pixi 4 5\" 3g": "",
    "one touch pixi 4 5\" 4g": "",
    "one touch pixi 4 5.0\"": "",
    "one touch pixi 4 5.5\" 3g": "",
    "one touch pixi 4 6\" 3g dual sim": "",
    "one touch pixi 4 6\" 3g": "",
    "one touch pixi 4 6\" 4g": "",
    "one touch pixi 4 7\" 3g": "",
    "one touch pixi 4 7\" wifi": "",
    "one touch pixi 4 plus power": "",
    "one touch pixi 5": "",
    "one touch pixi 7": "",
    "one touch pixi 8": "",
    "one touch pixi avion 4g lte": "",
    "one touch pixi avion lte": "",
    "one touch pixi glitz": "",
    "one touch pixi glory": "",
    "one touch pixi theatre": "",
    "one touch pop 10": "",
    "one touch pop 2 4.5\" dual sim": "",
    "one touch pop 2 4.5\"": "",
    "one touch pop 2 5\" dual sim": "",
    "one touch pop 2 5\"": "",
    "one touch pop 3 5\"": "",
    "one touch pop 3 5.5\"": "",
    "one touch pop 3 dual sim": "",
    "one touch pop 3": "",
    "one touch pop 4 6\"": "",
    "one touch pop 4 dual sim": "",
    "one touch pop 4+": "",
    "one touch pop 4": "",
    "one touch pop 4s": "",
    "one touch pop 7": "",
    "one touch pop 8": "",
    "one touch pop c5 dual sim": "",
    "one touch pop c7 dual sim": "",
    "one touch pop c7": "",
    "one touch pop d5": "",
    "one touch pop mirage": "",
    "one touch pop s3": "",
    "one touch pop s7": "",
    "one touch pop star": "",
    "one touch pop up": "",
    "one touch t10": "",
    "one touch t20": "",
    "one touch tab 7": "",
    "one touch tab 7hd": "",
    "one touch trek hd": "",
    "one touch x1": "",
    "onetouch4012x": "",
    "onevo7": "->one touch evo7",
    "ot-807d": "",
    "p360x": "->one touch pop 10",
    "pixi unite": "",
    "raven": "",
    "revvl": "",
    "tim xl": "",
    "timxl": "->tim xl",
    "u3": "",
    "u5 3g": "",
    "u5 lite": "",
    "u5 plus": "",
    "u50": "",
    "zip": ""
  },
  "alcor": {
    "access q784c": "DS=7;RS=1024x600;SZ=192x114x10;RM=1024;CP=93;OI=1;OV=7;SM=2;"
  },
  "aldi nord": null,
  "aldi süd": null,
  "alfawise": {
    "a95x r1": "",
    "a9": ""
  },
  "aligator": {
    "rx510": "",
    "rx710": "",
    "rx800": "",
    "s4080": "",
    "s5060": "",
    "s5065": "",
    "s5066": "",
    "s5070": "",
    "s5080": "",
    "s5520": "",
    "s5540": "",
    "s6500": ""
  },
  "allcall": {
    "alpha": "",
    "atom": "",
    "bro": "",
    "heat 3": "",
    "heat 4": "",
    "heat3": "->heat 3",
    "heat4": "->heat 4",
    "hot 1 mini+": "",
    "hot 1 x": "",
    "hot 2 x": "",
    "hot 5 mini": "",
    "hot5 mini": "->hot 5 mini",
    "madrid": "",
    "rio pro": "",
    "rio s": "",
    "rio x": "",
    "rio": "",
    "s1 x": "",
    "s1": ""
  },
  "hotwav": {
    "symbol r60": "DS=6.6;RS=720x1600;OI=1;OV=10;SM=2;RM=3072;CP=9;",
    "pearl k2": "DS=6.26;RS=720x1520;SZ=;WT=;RE=2019;OI=1;OV=9;RM=3072;CP=84;",
    "magic q8": "RS=720x1280;DS=5.5;OI=1;OV=8;SM=2;RM=3072;RE=2018.03;SZ=152x75.5x7.3;",
    "magic 5 plus": "RE=2018.09;SM=2;OI=1;OV=7;DS=5;RM=1024;",
    "magic 13": "DS=5;OI=1;OV=7;RM=2048;RS=720x1280;SM=2;WT=164;CP=9;SZ=72.1x145.3x9;",
    "magic 11": "RS=720x1280;RE=2018.05;RM=3072;DS=6;CP=4;SM=2;OI=1;OV=7;",
    "magic 9": "",
    "magic 8": "",
    "cosmos v23": "RE=2017.05;OI=1;OV=6.0;RM=3072;DS=4.7;RS=720x1280;WT=164;SZ=81.4x163.0x7.7;SM=2;CP=6;",
    "cosmos v22": "RE=2017.08;SZ=81.4x163.0x7.8;WT=160;DS=4.7;RS=720x1280;RM=2048;CP=6;OI=1;OV=6.0;",
    "cosmos v21": "",
    "cosmos v20": "",
    "cosmos u2": "",
    "cosmos v19": "",
    "cosmos v19 plus": "",
    "cosmos v8 lite": "",
    "cosmos v5": "",
    "cosmos v6": "",
    "cosmos v9": "",
    "hot 6": "",
    "venus x1": "",
    "venus x17": "",
    "venus r8 plus": "",
    "venus r10": "",
    "venus r12": "",
    "venus r18": "",
    "venus r2": "",
    "venus r3": "",
    "venus r6": "",
    "venus r7": "",
    "venus r8": "",
    "venus r9": "RS=540x960;DS=5.5;OI=1;OV=6.0;RM=1024;SM=2;RE=2017.09;CP=6;WT=155;",
    "venus x2": "RS=540x960;SZ=68.9x141.1x7.9;DS=5.5;SM=2;OI=1;OV=4.4.2;RM=1024;CP=6;",
    "venus x16": "DS=5.5;RE=2016.08;OI=1;OV=5.1;CP=24;RM=512;RS=360x640;SM=2;",
    "venus x16-1": "->venus x16",
    "symbol x": "DS=5.7;RS=720x1498;SZ=71x146.3x7.8;WT=189;RE=2018;OI=1;OV=8.1;RM=2048;CP=5;",
    "symbol max": "DS=6.26;RS=720x1520;SZ=;WT=;RE=2018;OI=1;OV=8.1;RM=2048;CP=293;",
    "venus x10": "OI=1;OV=6.0;SZ=72x145x8.45;WT=137;RS=720x1280;DS=5;RM=1024;CP=58;",
    "venus x12": "RE=2017;OI=1;OV=5.1;DS=5;WT=137;SZ=72x148.6x9.5;RS=720x1280;RM=1024;CP=;",
    "venus x14": "DS=5;RS=480x854;RM=1024;OI=1;OV=5.1;CP=6;",
    "venus x15": "RE=2017;OI=1;OV=5.1;DS=5;SZ=72x145.9x9.5;WT=147;RS=480x854;RM=1024;CP=;",
    "venus x19": "RE=2017;OI=1;OV=5.1;DS=5;SZ=72x145.6x9;WT=149;RS=720x1280;RM=1024;CP=;"
  },
  "alldocube": {
    "iplay 10 pro 10.1\"": "",
    "iplay 20": "DS=10.1;RS=1920x1200;SZ=245.2x149.4x7.95;WT=450;RM=4096;CP=109;RE=2020.07;OI=1;OV=10;",
    "iplay 40": "DS=10.4;RS=2000x1200;SZ=248.1x157.9x8.2;WT=474;RM=8192;CP=199;RE=2021.01;OI=1;OV=10;",
    "m5": "",
    "m5s": "",
    "m5x": "",
    "m5xs": "",
    "t1001x": "->m5x",
    "t1001xs": "->m5xs",
    "t1006": "->m5",
    "t1006s": "->m5s",
    "u1006": "->iplay 10 pro 10.1\"",
    "u1006h": "->iplay 10 pro 10.1\""
  },
  "infinix": {
    "hot 10": "RE=2020.09;SZ=77.6x171.1x8.9;WT=195;DS=6.78;RS=720x1640;OI=1;OV=10;CP=319;RM=3072;",
    "x682b": "->hot 10",
    "x682c": "->hot 10",
    "hot 10 play": "RE=2021.01;SZ=78x171.8x8.9;WT=207;DS=6.82;RS=720x1640;OI=1;OV=10;CP=284;RM=2048;",
    "x688c": "->hot 10 play",
    "x688b": "->hot 10 play",
    "hot 9": "RE=2020.03;SZ=76.8x165.4x8.8;WT=185;DS=6.6;RS=720x1600;OI=1;OV=10;CP=355;RM=2048;",
    "x655": "->hot 9",
    "x655d": "->hot 9",
    "x655c": "->hot 9",
    "note 11 pro": "RE=2021.10;SZ=78.4x173.1x8.7;WT=;DS=6.95;RS=1080x2460;OI=1;OV=11;CP=350;RM=4096;",
    "x697": "->note 11 pro",
    "note 7": "RE=2020.04;SZ=79x173.4x8.8;WT=206;DS=6.95;RS=720x1640;OI=1;OV=10;CP=319;RM=4096;",
    "x690": "->note 7",
    "x690b": "->note 7",
    "note 7 lite": "RE=2020.04;SZ=76.8x165.4x8.8;WT=185;DS=6.6;RS=720x1600;OI=1;OV=10;CP=319;RM=4096;",
    "x656": "->note 7 lite",
    "note 8": "RE=2020.10;SZ=78.8x175.3x9;WT=;DS=6.95;RS=720x1640;OI=1;OV=10;CP=226;RM=6144;",
    "x692": "->note 8",
    "hot 9 play": "RE=2020.04;SZ=78x171.8x8.9;WT=209;DS=6.82;RS=720x1640;OI=1;OV=9;CP=44;RM=2048;",
    "x680": "->hot 9 play",
    "x680f": "->hot 9 play",
    "x680c": "->hot 9 play;RM=4096;CP=355;",
    "x680b": "->hot 9 play;RM=3072;CP=355;",
    "smart 5": "RE=2020.09;SZ=76.4x165.5x8.8;WT=;DS=6.6;RS=720x1600;OI=1;OV=10;CP=337;RM=2048;",
    "x657": "->smart 5",
    "x657b": "->smart 5",
    "x657c": "->smart 5",
    "x6810": "->zero x neo",
    "x6811": "->zero x pro",
    "zero x pro": "RE=2021.09;SZ=75.7x164.1x7.8;WT=193;DS=6.67;RS=1080x2400;OI=1;OV=11;CP=321;RM=8192;",
    "zero 5": "RE=2018;SZ=82.4x166.4x8;WT=197;DS=5.98;RS=1080x1920;OI=1;OV=7.0;CP=343;RM=6144;",
    "x603": "->zero 5",
    "note 5": "RE=2018;SZ=75x158x8.4;WT=173;DS=6.0;RS=1080x2160;OI=1;OV=8.1;CP=63;RM=3072;",
    "x604": "->note 5",
    "x604b": "->note 5",
    "zero 8i": "RE=2020.12;SZ=76.1x168.7x9.1;WT=210.5;DS=6.85;RS=1080x2460;OI=1;OV=10;CP=325;RM=8192;",
    "x687b": "->zero 8i",
    "zero 8": "RE=2020.08;SZ=76.1x168.7x9.1;WT=205;DS=6.85;RS=1080x2460;OI=1;OV=10;CP=325;RM=8192;",
    "x687": "->zero 8",
    "note 8i": "RE=2020.10;SZ=77.7x171.4x8.9;WT=;DS=6.78;RS=720x1640;OI=1;OV=10;CP=226;RM=4096;AL=X683;",
    "x683": "->note 8i",
    "note 10 pro": "RE=2021.05;SZ=78.3x172.8x7.8;WT=;DS=6.95;RS=1080x2460;OI=1;OV=11;CP=321;RM=6144",
    "x695c": "->note 10 pro",
    "x695": "->note 10 pro",
    "x695d": "->note 10 pro",
    "note 10": "RE=2021.05;SZ=78.7x173.2x8.8;WT=;DS=6.95;RS=1080x2460;OI=1;OV=11;CP=322;RM=4096;",
    "x693": "->note 10",
    "hot 8": "RE=2019.09;SZ=76.3x165x8.7;WT=179;DS=6.52;RS=720x1600;OI=1;OV=9.0;CP=44;RM=2048;",
    "x650": "->hot 8",
    "x650b": "->hot 8;RM=4096;",
    "x650d": "->hot 8;RM=4096;",
    "x650c": "->hot 8;RM=4096;",
    "s5 pro": "RE=2020.03;SZ=76.9x162.5x9;WT=195;DS=6.53;RS=1080x2340;OI=1;OV=10;CP=38;RM=4096;",
    "x660c": "->s5 pro",
    "x660b": "->s5 pro",
    "x660": "->s5 pro",
    "zero 6": "RE=2019.03;SZ=;WT=178;DS=6.18;SM=2;RS=1080x2246;OI=1;OV=8.0;CP=205;RM=6144;",
    "x620b": "->zero 6",
    "zero 6 pro": "RE=2019.03;SZ=;WT=178;DS=6.18;SM=2;RS=1080x2246;OI=1;OV=8.0;CP=205;RM=6144;",
    "x620": "->zero 6 pro"
  },
  "allview": {
    "2 speed quad": "",
    "a10 lite (2019)": "",
    "a10 lite 2019": "->a10 lite (2019)",
    "a10 plus": "",
    "a4 you": "",
    "a4all": "",
    "a4you": "->a4 you",
    "a5 easy": "",
    "a5 easy tm": "->a5 easy",
    "a5 quad plus": "",
    "a5 quad": "",
    "a5 quad plus tm": "->a5 quad plus",
    "a5 ready": "",
    "a5 ready tm": "->a5 ready",
    "a5 smiley": "",
    "a5smiley": "->a5 smiley",
    "a6 duo": "",
    "a6 lite": "",
    "a7 lite": "",
    "a8 lite": "",
    "a9 lite": "",
    "alldro 2 speed duo": "",
    "alldro 3 speed quad": "",
    "alldro p4": "",
    "allview2speedduo": "->alldro 2 speed duo",
    "allview3speedquad": "->alldro 3 speed quad",
    "allviewax2frenzy": "->ax2 frenzy",
    "allviewcity": "->city",
    "allviewcityplus": "->city plus",
    "allviewspeed": "->speed",
    "ax2 frenzy": "",
    "ax4 nano": "",
    "ax4nano": "->ax4 nano",
    "ax5 nano q": "",
    "ax5nanoq": "->ax5 nano q",
    "c6 duo": "",
    "city plus": "",
    "city": "",
    "e2 living": "",
    "e3 jump": "",
    "e3 living": "",
    "e3 sign": "",
    "e4 lite": "",
    "h1003 lte pro": "->viva h1003 lte pro",
    "impera s": "",
    "m9 connect": "",
    "p10 life": "",
    "p10 max": "",
    "p10 style": "",
    "p4 emagic": "",
    "p4 pro": "",
    "p4 quad": "",
    "p41 emagic": "",
    "p41 emagic tm": "->p41 emagic",
    "p43 easy": "",
    "p4i": "->alldro p4",
    "p5 emagic": "",
    "p5 emagic tm": "->p5 emagic",
    "p5 energy": "",
    "p5 life": "",
    "p5 lite": "",
    "p5 pro": "",
    "p5life tm": "->p5 life",
    "p6 energy lite": "",
    "p6 energy mini": "",
    "p6 energy": "",
    "p6 energy tm": "->p6 energy",
    "p6 lite": "",
    "p6 lite tm": "->p6 lite",
    "p6 plus": "",
    "p6 pro": "",
    "p6 qmax": "",
    "p6 quad": "",
    "p6": "",
    "p7 pro": "",
    "p7 pro tm": "->p7 pro",
    "p7 seon": "",
    "p7 xtreme": "",
    "p8 emagic": "",
    "p8 emagic tm": "->p8 emagic",
    "p8 energy mini": "",
    "p8 energy pro": "",
    "p8 energy": "",
    "p8 energy mini tm": "->p8 energy mini",
    "p8 life": "",
    "p8 pro": "",
    "p9 energy lite (2017)": "",
    "p9 energy lite": "",
    "p9 energy mini": "",
    "p9 energy s": "",
    "p9 energy": "",
    "p9 energy lite 2017": "->p9 energy lite (2017)",
    "p9 energy mini tm": "->p9 energy mini",
    "p9 life": "",
    "p9 life tm": "->p9 life",
    "speed i": "",
    "speed": "",
    "speedi": "->speed i",
    "v1 viper i 4g": "",
    "v1 viper i": "",
    "v1 viper": "",
    "v1 viper i4g": "->v1 viper i 4g",
    "v1 viper i4g tm": "->v1 viper i 4g",
    "v1001g": "->viva 1001g",
    "v1003g": "->viva 1003g",
    "v2 viper e": "",
    "v2 viper i 4g": "",
    "v2 viper i": "",
    "v2 viper s": "",
    "v2 viper x plus": "",
    "v2 viper x": "",
    "v2 viper xe": "",
    "v2 viper i4g": "->v2 viper i 4g",
    "v2 viper i tm": "->v2 viper i",
    "v3 viper": "",
    "v4 viper": "",
    "vc701": "->viva c701",
    "vh1001 lte": "->viva h1001 lte",
    "vh1002 lte": "->viva h1002 lte",
    "vh701 lte": "->viva h701 lte",
    "vh801": "->viva h801",
    "vh802 lte": "->viva h802 lte",
    "vh802 lte tm": "->viva h802 lte",
    "vi701g": "->viva i701g",
    "viva 1001g": "",
    "viva 1003g": "",
    "viva c701": "",
    "viva h1001 lte": "",
    "viva h1002 lte": "",
    "viva h1003 lte pro": "",
    "viva h701 lte": "",
    "viva h801": "",
    "viva h802 lte": "",
    "viva i701g": "",
    "x1 soul xtreme": "",
    "x1 soul": "",
    "x2 soul mini": "",
    "x2 soul style plus": "",
    "x2 soul style": "",
    "x2 soul xtreme": "",
    "x2 soul": "",
    "x2 soul mini tm": "->x2 soul mini",
    "x2 soul style tm": "->x2 soul style",
    "x2 twin": "",
    "x3 soul lite": "",
    "x3 soul mini": "",
    "x3 soul plus": "",
    "x3 soul pro": "",
    "x3 soul": "",
    "x3 soul lite tm": "->x3 soul lite",
    "x4 soul infinity l": "",
    "x4 soul infinity n": "",
    "x4 soul infinity s": "",
    "x4 soul infinity z": "",
    "x4 soul lite": "",
    "x4 soul mini s": "",
    "x4 soul mini": "",
    "x4 soul style": "",
    "x4 soul vision": "",
    "x4 soul xtreme": "",
    "x4 soul": "",
    "x4 soul mini s tm": "->x4 soul mini s",
    "x5 soul mini": "",
    "x5 soul pro": "",
    "x5 soul style": "",
    "x5 soul": "",
    "x6 soul mini": "",
    "x6 soul xtreme": ""
  },
  "allwinner": {
    "a33 quad-core y3": "",
    "a64 quad-core p3": "",
    "quad-core a33 y3": "->a33 quad-core y3",
    "quad-core a64 p3": "->a64 quad-core p3",
    "quad-core t3 k2001m": "",
    "ultra octa t8": "",
    "ultraocta-t8": "->ultra octa t8",
    "zy-07b": ""
  },
  "altek": {
    "leo": "DS=3.2;RS=480x800;SZ=56.1x111.9x15.5;WT=140;RE=;RM=;CP=;OI=1;OV=2.1;"
  },
  "altech uec": {
    "pvr9600": ""
  },
  "altice": {
    "s31": "",
    "s43": "",
    "s62": ""
  },
  "altron": {
    "al-555": "",
    "gi-626": "",
    "ob 421": "->ob-421",
    "ob-421": "",
    "ob-627": "",
    "ob-728": ""
  },
  "amazon": {
    "aeobc": "->echo",
    "aeokn": "->echo",
    "afta": "->fire tv cube (gen 1)",
    "aftb": "->fire tv",
    "aftm": "->fire tv stick",
    "aftn": "->fire tv (gen 3)",
    "aftr": "->fire tv cube (gen 2)",
    "afts": "->fire tv stick",
    "aftt": "->fire tv stick",
    "alexa": "",
    "echo": "",
    "fire 7\" (2019)": "",
    "fire 7\"": "",
    "fire hd 10 (2017)": "DS=10.1;RS=1920x1200;SZ=262x159x9.8;WT=500;RE=2017.10;RM=2048;CP=99;OI=1;OV=5.1;",
    "fire hd 10 (2019)": "DS=10.1;RS=1920x1200;SZ=262x159x9.8;WT=504;RE=2019.09;RM=2048;CP=97;OI=1;OV=9;",
    "fire hd 10": "DS=10.1;RS=1280x800;SZ=262x159x7.7;WT=432;RE=2015.09;RM=1024;CP=100;OI=1;OV=5;",
    "fire hd 6": "DS=6.3;RS=1280x800;SZ=104.1x170.2x10.2;WT=290;RE=2014.09;RM=1024;CP=100;OI=1;OV=4.4;",
    "fire hd 7": "DS=7;RS=1280x800;SZ=190.5x127x10.7;WT=337;RE=2014.09;RM=1024;CP=100;OI=1;OV=4.4;",
    "fire hd 8 (2015)": "",
    "fire hd 8 (2016)": "",
    "fire hd 8 (2017)": "",
    "fire hd 8 (2018)": "",
    "fire hd 8 (2020)": "DS=8;RS=1280x800;SZ=202x137x9.7;WT=355;RE=;RM=2048;CP=101;OI=1;OV=9;",
    "fire hdx 8.9 4g": "",
    "fire hdx 8.9": "DS=8.9;RS=2560x1600;SZ=231.1x157.5x7.6;WT=389;RE=2014.09;RM=2048;CP=94;OI=1;OV=4.4;",
    "fire phone": "DS=4.7;RS=720x1280;SZ=66x140x9;WT=160;RE=2014.06;RM=2048;CP=94;OI=1;OV=4.2.2;",
    "fire tv (gen 3)": "",
    "fire tv cube (gen 1)": "",
    "fire tv cube (gen 2)": "",
    "fire tv stick": "",
    "fire tv": "",
    "jem": "->kindle fire hd 8.9\" wifi",
    "kfapwi": "->kindle fire hdx 8.9\" wifi",
    "kfarwi": "->fire hd 6",
    "kfaswi": "->fire hd 7",
    "kfdowi": "->fire hd 8 (2017)",
    "kffowi": "->fire 7\"",
    "kfgiwi": "->fire hd 8 (2016)",
    "kfjwa": "->kindle fire hd 8.9\" 4g",
    "kfjwi": "->kindle fire hd 8.9\" wifi",
    "kfkawi": "->fire hd 8 (2018)",
    "kfmawi": "->fire hd 10 (2019)",
    "kfmewi": "->fire hd 8 (2015)",
    "kfmuwi": "->fire 7\" (2019)",
    "kfonwi": "->fire hd 8 (2020)",
    "kfsawa": "->fire hdx 8.9 4g",
    "kfsawi": "->fire hdx 8.9",
    "kfsuwi": "->fire hd 10 (2017)",
    "kftbwi": "->fire hd 10",
    "kftt": "->kindle fire hd",
    "kindle fire hd 7\" wifi": "",
    "kindle fire hd 8.9\" 4g": "",
    "kindle fire hd 8.9\" wifi": "",
    "kindle fire hd": "DS=7;RS=1280x800;SZ=193x137x10.3;WT=395;RE=2012.09;RM=1024;CP=29;OI=1;OV=4;",
    "kindle fire hdx 7\" wifi": "",
    "kindle fire hdx 8.9\" wifi": "",
    "kindle fire": "DS=7;RS=1024x600;SZ=190x120x11.4;WT=413;RE=2011.09;RM=512;CP=;OI=1;OV=2.3;",
    "kindle": "",
    "sd4930ur": "->fire phone",
    "tate": "->kindle fire hd 7\" wifi",
    "fire hd 8 plus (2020)": "DS=8;RS=800x1280;SZ=137x202x9.7;WT=355;RE=;RM=3072;CP=101;OI=1;OV=9;",
    "fire hd 8": "DS=8;RS=800x1280;SZ=128x214x7.7;WT=311;RE=2015.09;RM=1024;CP=;OI=1;OV=5;",
    "fire": "DS=7;RS=600x1024;SZ=115x191x10.6;WT=313;RE=2015.09;RM=1024;CP=;OI=1;OV=5;",
    "kindle fire 2": "DS=7;RS=1024x600;SZ=189x120x11.5;WT=400;RE=2012.09;RM=1024;CP=190;OI=1;OV=4;",
    "kindle fire hd (2013)": "DS=7;RS=1280x800;SZ=191x128x10.6;WT=345;RE=2013.09;RM=1024;CP=;OI=1;OV=4.2.2;",
    "kindle fire hd 8.9 4g lte": "DS=8.9;RS=1920x1200;SZ=240x164x8.8;WT=575;RE=2012.09;RM=1024;CP=95;OI=1;OV=4;",
    "kindle fire hd 8.9": "DS=8.9;RS=1920x1200;SZ=240x164x8.8;WT=567;RE=2012.09;RM=1024;CP=95;OI=1;OV=4;",
    "kindle fire hdx 7": "DS=7;RS=1920x1200;SZ=186x128x9;WT=311;RE=2013.09;RM=2048;CP=94;OI=1;OV=4.2.2;",
    "kindle fire hdx 8.9": "DS=8.9;RS=2560x1600;SZ=231x158x7.8;WT=384;RE=2013.09;RM=2048;CP=94;OI=1;OV=4.2.2;"
  },
  "amgoo": {
    "a1": "",
    "am350": "->jack pro",
    "am355": "->tigo",
    "am402": "->pronto",
    "am405": "",
    "am407": "->tigo",
    "am410": "->unico",
    "am412": "",
    "am415": "",
    "am450": "->swift",
    "am508": "->fuego",
    "am509": "->uno",
    "am515": "->d1",
    "am518": "->c1",
    "am520": "->pro",
    "am523": "->plus",
    "am527": "->geo",
    "am530": "->a1",
    "am535": "->p1",
    "c1": "RM=1024;DS=5;RS=480x854;OI=1;OV=7;",
    "d1": "",
    "fuego": "",
    "geo": "",
    "jack pro": "",
    "p1": "",
    "plus": "",
    "pro": "",
    "pronto": "",
    "swift": "",
    "tigo": "",
    "unico": "DS=4.5;RS=480x800;RM=1024;OI=1;OV=7;",
    "uno": ""
  },
  "amigoo": {
    "m1 max": "",
    "r300": "",
    "r9 max": "",
    "x15": ""
  },
  "amoi": {
    "a862w": "DS=4.5;RS=540x960;SZ=67.3x134.5x10.6;WT=150;RM=1024;CP=200;OI=1;OV=4.1;SM=2;",
    "ca6": "",
    "clever gem s52": "",
    "clever gleam s45": "",
    "clever joy s40": "",
    "clever lite s41": "",
    "clever pad p7": "",
    "clever touch s46": "",
    "clever wave s50": "",
    "clever-wave s50": "->clever wave s50",
    "clevertouch s46": "->clever touch s46",
    "a100": "DS=1.5;RS=128x128;SZ=45.0x85.5x18;WT=;RE=;RM=;CP=;OS=;",
    "a102": "DS=1.5;RS=128x128;SZ=45.0x85.5x18;WT=75;RE=;RM=;CP=;OS=;",
    "a10": "DS=1.4;RS=128x128;SZ=43x107.5x15.5;WT=;RE=;RM=;CP=;OS=;",
    "a200": "DS=1.5;RS=128x128;SZ=41.0x82.0x15;WT=;RE=;RM=;CP=;OS=;",
    "a203": "DS=1.5;RS=128x128;SZ=46.0x101.0x13.4;WT=72;RE=;RM=;CP=;OS=;",
    "a208": "DS=1.8;RS=128x160;SZ=47.0x92.0x16;WT=85;RE=;RM=;CP=;OS=;",
    "a726w": "DS=4;RS=480x800;SZ=;WT=;RE=;RM=512;CP=;OI=1;OV=4.2;",
    "a900w": "DS=5.5;RS=720x1280;SZ=;WT=;RE=;RM=1024;CP=7;OI=1;OV=4.2;",
    "a920w": "DS=5;RS=720x1280;SZ=70.5x140x8.3;WT=160;RE=;RM=2048;CP=112;OI=1;OV=4.2;",
    "a928w": "DS=5;RS=1080x1920;SZ=71x140x8;WT=140;RE=;RM=2048;CP=2;OI=1;OV=4.4;",
    "a955w": "DS=5.5;RS=1080x1920;SZ=;WT=;RE=;RM=2048;CP=182;OI=1;OV=4.4;",
    "e72": "DS=2;RS=176x220;SZ=44.8x100.0x14.8;WT=85;RE=;RM=;CP=;OS=;",
    "e78": "DS=2.5;RS=320x240;SZ=62.0x115.0x12.7;WT=131;RE=;RM=61.44;CP=;OS=;",
    "f100": "DS=2.2;RS=240x320;SZ=;WT=;RE=;RM=;CP=;OS=;",
    "f106": "DS=2.6;RS=240x320;SZ=54x121x11;WT=96;RE=;RM=;CP=;OS=;",
    "f16": "DS=2.2;RS=176x220;SZ=;WT=;RE=;RM=;CP=;OS=;",
    "f200e": "DS=2.4;RS=240x320;SZ=50.8x116.5x13;WT=91;RE=;RM=;CP=;OS=;",
    "f203": "DS=2.4;RS=240x320;SZ=51x113x15.5;WT=111;RE=;RM=;CP=;OS=;",
    "f205": "DS=3;RS=240x400;SZ=51x109x16.7;WT=110;RE=;RM=;CP=;OS=;",
    "f208": "DS=3.2;RS=240x400;SZ=54.5x112.5x12.6;WT=;RE=;RM=;CP=;OS=;",
    "f209": "DS=2.6;RS=240x320;SZ=55x121x16.1;WT=128;RE=;RM=;CP=;OS=;",
    "f210": "DS=3.2;RS=240x400;SZ=57x110x13;WT=104;RE=;RM=;CP=;OS=;",
    "f30": "DS=2.8;RS=240x400;SZ=49x102x16.5;WT=96;RE=;RM=;CP=;OS=;",
    "f320": "DS=2.8;RS=240x400;SZ=51x105x16;WT=100;RE=;RM=;CP=;OS=;",
    "f36": "DS=2.4;RS=240x320;SZ=51.6x105.6x12.8;WT=100;RE=;RM=;CP=;OS=;",
    "f56": "DS=2.8;RS=240x400;SZ=49x102x16.95;WT=98;RE=;RM=;CP=;OS=;",
    "f60": "DS=2.6;RS=240x400;SZ=48x101x16.4;WT=102;RE=;RM=;CP=;OS=;",
    "gsm6711a": "DS=2.5;RS=320x240;SZ=62.8x115.5x13.2;WT=126;RE=;RM=;CP=;OS=;",
    "m300": "DS=2;RS=144x176;SZ=46.0x104.0x12.9;WT=80;RE=;RM=;CP=;OS=;",
    "m3": "DS=1.8;RS=128x160;SZ=46.0x93.0x16.5;WT=92;RE=;RM=;CP=;OS=;",
    "n310": "DS=4;RS=320x480;SZ=66x120x12.5;WT=136;RE=;RM=;CP=;OS=;",
    "n60": "DS=3.2;RS=240x400;SZ=55.5x110.5x12.5;WT=104;RE=;RM=;CP=;OS=;",
    "n700": "DS=3.5;RS=320x480;SZ=62x115x13;WT=125;RE=;RM=;CP=;OI=1;OV=2.3;",
    "n79": "DS=3.5;RS=320x480;SZ=63x115x12.8;WT=125;RE=;RM=;CP=145;OI=1;OV=2.2;",
    "n800": "DS=2.8;RS=480x640;SZ=59.0x109.0x15.7;WT=135;RE=;RM=;CP=;OS=;",
    "n807": "DS=4;RS=480x800;SZ=64x124x11;WT=140;RE=;RM=;CP=;OI=1;OV=4;",
    "n808": "DS=4.3;RS=480x800;SZ=69x128x12.5;WT=;RE=;RM=;CP=;OI=1;OV=4;",
    "n810": "DS=2.8;RS=240x320;SZ=59.0x109.0x16.1;WT=135;RE=;RM=61.44;CP=;OS=;",
    "n828p": "DS=4.5;RS=540x960;SZ=;WT=;RE=;RM=1024;CP=7;OI=1;OV=4.2;",
    "n89": "DS=4;RS=480x800;SZ=65x125x10.95;WT=140;RE=;RM=512;CP=;OI=1;OV=2.3;",
    "n8i": "DS=2.8;RS=240x320;SZ=59.6x118.8x15.6;WT=138;RE=;RM=61.44;CP=;OS=;",
    "s500": "DS=2.4;RS=240x320;SZ=49.6x118x11.8;WT=100;RE=;RM=;CP=;OS=;",
    "s501": "DS=2.4;RS=240x320;SZ=50.5x118.5x11.77;WT=100;RE=;RM=;CP=;OS=;",
    "s520": "DS=2.6;RS=240x400;SZ=51x106x16;WT=120;RE=;RM=;CP=;OS=;",
    "s525": "DS=2.8;RS=240x400;SZ=51.4x110.7x15.4;WT=128;RE=;RM=;CP=;OS=;",
    "s532": "DS=2.8;RS=240x400;SZ=51x108x16.5;WT=110;RE=;RM=;CP=;OS=;",
    "s58": "DS=2.8;RS=240x400;SZ=50.6x106x12;WT=112;RE=;RM=;CP=;OS=;",
    "s600": "DS=3;RS=240x400;SZ=51x110x16.5;WT=155;RE=;RM=;CP=;OS=;",
    "s660": "DS=3.2;RS=240x400;SZ=53x113x17.9;WT=110;RE=;RM=;CP=;OS=;",
    "wma8505": "DS=2;RS=176x220;SZ=49.0x112.0x12.5;WT=;RE=;RM=;CP=;OS=;",
    "wma8507": "DS=2;RS=176x220;SZ=46.0x95.0x17.1;WT=93;RE=;RM=;CP=;OS=;",
    "wma8508": "DS=2;RS=176x220;SZ=48.0x95.0x15.9;WT=;RE=;RM=;CP=;OS=;",
    "wma8512": "DS=2;RS=176x220;SZ=45.4x100.0x13.9;WT=87;RE=;RM=;CP=;OS=;",
    "wma8515": "DS=2;RS=176x220;SZ=45.4x100.0x13.9;WT=87;RE=;RM=;CP=;OS=;",
    "wma8517": "DS=2.2;RS=176x220;SZ=48.6x98.6x18.2;WT=;RE=;RM=;CP=;OS=;",
    "wma8701a": "DS=2.2;RS=240x320;SZ=51.6x100.6x16;WT=120;RE=;RM=;CP=;OS=;",
    "wma8703": "DS=2.2;RS=240x320;SZ=48.0x109.4x13.8;WT=100;RE=;RM=;CP=;OS=;",
    "wma8709": "DS=2.2;RS=240x320;SZ=45.0x102.7x14;WT=;RE=;RM=;CP=;OS=;",
    "wma8710": "DS=2.2;RS=240x320;SZ=47.6x97.0x14.4;WT=;RE=;RM=;CP=;OS=;",
    "wma9109": "DS=2;RS=176x220;SZ=;WT=;RE=;RM=;CP=;OS=;",
    "wma9110": "DS=2;RS=176x220;SZ=;WT=;RE=;RM=;CP=;OS=;"
  },
  "andowl": {
    "q4": "",
    "q5": ""
  },
  "anry": {
    "rs10": "",
    "s20": "",
    "x20": ""
  },
  "ans": {
    "ul40": ""
  },
  "aoc": {
    "le32s5970-20": "",
    "le32s5970s-20": "",
    "le43s5970-20": "",
    "le43s5970s-20": "",
    "le43s5977-20": "",
    "le55u7970-30": "",
    "s50856": "",
    "u60856": ""
  },
  "aoson": {
    "r101": "->r101 10.1\"",
    "r102": "->r102 10.1\"",
    "r103": "->r103 10.1\"",
    "s7 pro": "->s7 pro 7.0\"",
    "s7": "->s7 7.0\"",
    "s8pro": "->s8 pro 8.0\"",
    "m707tg": "",
    "r101 10.1\"": "",
    "r102 10.1\"": "",
    "r103 10.1\"": "",
    "s7 7.0\"": "",
    "s7 pro 7.0\"": "",
    "s8 pro 8.0\"": ""
  },
  "apple": {
    "iphone 11": "DS=6.1;RS=828x1792;SZ=75.7x150.9x8.3;WT=194;RE=2019.09;RM=4096;CP=59;OS=iOS 13;SM=1;",
    "iphone 11 pro": "DS=5.8;RS=1125x2436;SZ=71.4x144x8.1;WT=188;RE=2019.09;RM=4096;CP=59;OS=iOS 13;SM=1",
    "iphone 11 pro max": "DS=6.5;RS=1242x2688;SZ=77.8x158x8.1;WT=226;RE=2019.09;RM=4096;CP=59;OS=iOS 13;SM=1",
    "apple tv": "",
    "homepod": "WT=2500;",
    "ipad 2": "DS=9.7;RS=1024x768;SZ=241.2x185.7x8.8;WT=613;RE=2011.03;RM=512;CP=105;OS=iOS 9.3.5;",
    "ipad 4": "DS=9.7;RS=2048x1536;SZ=241.2x185.7x9.4;WT=662;RE=2012.10;RM=1024;CP=104;OS=iOS 10.3.4;",
    "ipad 8 10.2\"": "",
    "ipad air 2": "DS=9.7;RS=2048x1536;SZ=240x169.5x6.1;WT=437;RE=2014.10;RM=2048;CP=106;OS=iOS 8.1;",
    "ipad air 3": "",
    "ipad air 4": "",
    "ipad air": "DS=9.7;RS=2048x1536;SZ=240x169.5x7.5;WT=469;RE=2013.10;RM=1024;CP=108;OS=iOS 7;",
    "ipad mini 3": "DS=7.9;RS=2048x1536;SZ=200x134.7x7.5;WT=341;RE=2014.10;RM=1024;CP=108;OS=iOS 8.1;",
    "ipad mini 4": "DS=7.9;RS=2048x1536;SZ=203.2x134.6x6.1;WT=304;RE=2015.09;RM=2048;CP=108;OS=iOS 8.1;",
    "ipad mini 5": "",
    "ipad mini": "DS=7.9;RS=1024x768;SZ=200x134.7x7.2;WT=312;RE=2012.10;RM=512;CP=105;OS=iOS 8.1;",
    "ipad pro 2 11\"": "",
    "ipad pro 2 12.9": "",
    "ipad pro 4 12.9\"": "",
    "ipad": "DS=9.7;RS=1024x768;SZ=243x190x13;WT=730;RE=2010.01;RM=256;CP=123;OS=;",
    "iphone 12 mini": "RE=2020.10;SZ=64.2x131.5x7.4;WT=135;DS=5.4;RS=1080x2340;OS=iO;CP=122;RM=4096;",
    "a2399": "->iphone 12 mini",
    "a2176": "->iphone 12 mini",
    "a2398": "->iphone 12 mini",
    "a2400": "->iphone 12 mini",
    "iphone 12 pro max": "RE=2020.10;SZ=78.1x160.8x7.4;WT=228;DS=6.7;RS=1284x2778;OS=iOS 14;CP=122;RM=6144;",
    "a2411": "->iphone 12 pro max",
    "a2342": "->iphone 12 pro max",
    "a2410": "->iphone 12 pro max",
    "a2412": "->iphone 12 pro max",
    "iphone 12 pro": "RE=2020.10;SZ=71.5x146.7x7.4;WT=189;DS=6.1;RS=1170x2532;OS=iOS 14;CP=122;RM=6144;",
    "a2407": "->iphone 12 pro",
    "a23418": "->iphone 12 pro",
    "a2406": "->iphone 12 pro",
    "a2408": "->iphone 12 pro",
    "iphone 12": "RE=2020.10;SZ=71.5x146.7x7.4;WT=164;DS=6.1;RS=1170x2532;OS=iOS 14;CP=122;RM=4096;",
    "iphone 3gs": "DS=3.5;RS=320x480;SZ=62.1x115.5x12.3;WT=135;RE=2009.06;RM=256;CP=;OS=;",
    "iphone 4s": "RE=2011.10;SZ=58.6x115.2x9.3;WT=140;DS=3.5;RS=640x960;OS=;CP=105;RM=512;",
    "iphone 4 verizon": "DS=3.5;RS=640x960;SZ=58.6x115.2x9.3;WT=137;RE=2011.01;RM=512;CP=123;OS=;",
    "iphone 4": "DS=3.5;RS=640x960;SZ=58.6x114.3x9.3;WT=137;RE=2010.06;RM=512;CP=123;OS=;",
    "iphone 5": "RE=2012.09;SZ=58.6x123.8x7.6;WT=112;DS=4.0;RS=640x1136;OS=;CP=124;RM=1024;",
    "iphone 5c": "RE=2013.09;SZ=59.2x124.4x9;WT=132;DS=4.0;RS=640x1136;OS=;CP=124;RM=1024;",
    "iphone 5s": "RE=2013.09;SZ=58.6x123.8x7.6;WT=112;DS=4.0;RS=640x1136;OS=;CP=108;RM=1024;",
    "iphone 6 plus": "RE=2014.09;SZ=77.8x158.1x7.1;WT=172;DS=5.5;RS=1080x1920;OS=;CP=125;RM=1024;",
    "iphone 6": "RE=2014.09;SZ=67x138.1x6.9;WT=129;DS=4.7;RS=750x1334;OS=iO;CP=125;RM=1024;",
    "iphone 6s plus": "RE=2015.09;SZ=77.9x158.2x7.3;WT=192;DS=5.5;RS=1080x1920;OS=;CP=126;RM=2048;",
    "iphone 6s": "RE=2015.09;SZ=67.1x138.3x7.1;WT=143;DS=4.7;RS=750x1334;OS=;CP=126;RM=2048;",
    "iphone 7 plus": "RE=2016.09;SZ=77.9x158.2x7.3;WT=188;DS=5.5;RS=1080x1920;OS=;CP=127;RM=3072;",
    "iphone 7": "RE=2016.09;SZ=67.1x138.3x7.1;WT=138;DS=4.7;RS=750x1334;OS=;CP=127;RM=2048;",
    "iphone 8 plus": "RE=2017.09;SZ=78.1x158.4x7.5;WT=202;DS=5.5;RS=1080x1920;OS=;CP=128;RM=3072;",
    "iphone 8": "RE=2017.09;SZ=67.3x138.4x7.3;WT=148;DS=4.7;RS=750x1334;OS=;CP=128;RM=2048;",
    "iphone se (2020)": "RE=2020.04;SZ=67.3x138.4x7.3;WT=148;DS=4.7;RS=750x1334;OS=;CP=129;RM=3072;",
    "a2275": "->iphone se (2020)",
    "a2296": "->iphone se (2020)",
    "a2298": "->iphone se (2020)",
    "iphone se": "RE=2016.04;SZ=58.6x123.8x7.6;WT=113;DS=4.0;RS=640x1136;OS=;CP=126;RM=2048;",
    "a1662": "->iphone se",
    "a1723": "->iphone se",
    "a1724": "->iphone se",
    "iphone x": "RE=2017.09;SZ=70.9x143.6x7.7;WT=174;DS=5.8;RS=1125x2436;OS=;CP=128;RM=3072;",
    "a1865": "->iphone x",
    "a1901": "->iphone x",
    "a1902": "->iphone x",
    "a1903": "->iphone x",
    "iphone xr": "RE=2018.09;SZ=75.7x150.9x8.3;WT=194;DS=6.1;RS=828x1792;OS=;CP=130;RM=3072;",
    "a2105": "->iphone xr",
    "a1984": "->iphone xr",
    "a2107": "->iphone xr",
    "a2108": "->iphone xr",
    "a2106": "->iphone xr",
    "iphone xs max": "RE=2018.09;SZ=77.4x157.5x7.7;WT=208;DS=6.5;RS=1242x2688;OS=;CP=130;RM=4096;",
    "a1921": "->iphone xs max",
    "a2101": "->iphone xs max",
    "a2102": "->iphone xs max",
    "a2104": "->iphone xs max",
    "iphone xs": "RE=2018.09;SZ=70.9x143.6x7.7;WT=177;DS=5.8;RS=1125x2436;OS=;CP=130;RM=4096;",
    "a2097": "->iphone xs",
    "a1920": "->iphone xs",
    "a2100": "->iphone xs",
    "a2098": "->iphone xs",
    "iphone": "DS=3.5;RS=320x480;SZ=61x115x11.6;WT=135;RE=2007.01;RM=102.4;CP=;OS=;",
    "ipod5,1": "->ipod touch 5",
    "ipod touch 5": "DS=4;RS=640x1136;SZ=58.6x123.4x6.1;WT=88;RE=2012.09;RM=512;CP=105;OS=;",
    "a1509": "->ipod touch 5",
    "a1421": "->ipod touch 5",
    "ipod touch": "DS=3.5;RS=320x480;SZ=61x109x8;WT=119;RE=2007.09;RM=102.4;CP=;OS=;",
    "mac mini (2018)": "",
    "macbook air (2009)": "",
    "macbook air 11\" (2010)": "",
    "macbook air 11\" (2011)": "",
    "macbook air 11\" (2012)": "",
    "macbook air 11\" (2013-2014)": "",
    "macbook air 11\" (2015)": "",
    "macbook air 13\" (2010)": "",
    "macbook air 13\" (2011)": "",
    "macbook air 13\" (2012)": "",
    "macbook air 13\" (2013-2014)": "",
    "macbook air 13\" (2015-2017)": "",
    "macbook air 13\" (2018)": "",
    "macbook air 13\" (2019)": "",
    "macbook air 13\" (2020)": "",
    "macbook pro (2008)": "",
    "macbook pro 13\" (2009)": "",
    "macbook pro 13\" (2010)": "",
    "macbook pro 13\" (2011)": "",
    "macbook pro 13\" (2012)": "",
    "macbook pro 13\" (2012-2013)": "",
    "macbook pro 13\" (2013-2014)": "",
    "macbook pro 13\" (2015)": "",
    "macbook pro 13\" (2016)": "",
    "macbook pro 13\" (2017)": "",
    "macbook pro 13\" (2018-2019)": "",
    "macbook pro 13\" (2019)": "",
    "macbook pro 13\" (2020)": "",
    "macbook pro 15\" (2006)": "",
    "macbook pro 15\" (2007)": "",
    "macbook pro 15\" (2008)": "",
    "macbook pro 15\" (2009)": "",
    "macbook pro 15\" (2010)": "",
    "macbook pro 15\" (2011)": "",
    "macbook pro 15\" (2012)": "",
    "macbook pro 15\" (2012-2013)": "",
    "macbook pro 15\" (2013-2014)": "",
    "macbook pro 15\" (2015)": "",
    "macbook pro 15\" (2016)": "",
    "macbook pro 15\" (2017)": "",
    "macbook pro 15\" (2018-2019)": "",
    "macbook pro 15\" (2019)": "",
    "macbook pro 16\" (2019)": "",
    "macbook pro 17\" (2006)": "",
    "macbook pro 17\" (2007)": "",
    "macbook pro 17\" (2009)": "",
    "macbook pro 17\" (2010)": "",
    "macbook pro 17\" (2011)": "",
    "watch 38mm": "",
    "watch 42mm": "",
    "watch se 40mm": "DS=1.6;RS=324x394;SZ=;WT=;RE=2020.09;RM=1024;CP=193;OS=;",
    "watch se 44mm": "DS=1.8;RS=368x448;SZ=;WT=;RE=2020.09;RM=1024;CP=193;OS=;",
    "watch se (40mm)": "->watch se 40mm",
    "watch se (44mm)": "->watch se 44mm",
    "watch series 1 38mm": "",
    "watch series 1 42mm": "",
    "watch series 2 38mm": "",
    "watch series 2 42mm": "",
    "watch series 3 38mm": "",
    "watch series 3 42mm": "",
    "watch series 4 40mm": "",
    "watch series 4 44mm": "",
    "watch series 5 40mm": "DS=1.6;RS=324x394;SZ=34x40x10.74;WT=30.8;RE=2019.09;RM=1024;CP=193;OS=;",
    "watch series 5 44mm": "DS=1.8;RS=368x448;SZ=38x44x10.74;WT=36.5;RE=2019.09;RM=1024;CP=193;OS=;",
    "watch series 6 40mm": "DS=1.6;RS=324x394;SZ=;WT=;RE=2020.09;RM=1024;CP=192;OS=;",
    "watch series 6 44mm": "DS=1.8;RS=368x448;SZ=;WT=;RE=2020.09;RM=1024;CP=192;OS=;",
    "watch series 5 (40mm)": "->watch series 5 40mm",
    "watch series 5 (44mm)": "->watch series 5 44mm",
    "watch series 6 (40mm)": "->watch series 6 40mm",
    "watch series 6 (44mm)": "->watch series 6 44mm"
  },
  "archos": {
    "5": "",
    "101 cobalt": "DS=10.1;RS=1024x600;SZ=272.5x161x12.15;WT=690;RM=1024;CP=72;RE=2015.10;OI=1;OV=4.2;",
    "101 neon": "DS=10.1;RS=1024x600;SZ=272x161x12.1;WT=690;RE=2014.10;RM=1024;CP=102;OI=1;OV=4.2;",
    "101 platinum": "DS=10.1;RS=1280x800;SZ=261x161x9.9;WT=530;RM=1024;CP=93;OI=1;OV=7;",
    "101 titanium": "DS=10.1;RS=1280x800;SZ=263x174x10.5;WT=650;RE=2013.01;RM=1024;CP=73;OI=1;OV=4.1;",
    "101 xenon": "DS=10.1;RS=1280x800;SZ=246x175x10;WT=630;RM=1024;CP=107;OI=1;OV=4.2;",
    "101 xs 2": "DS=10.1;RS=1280x800;SZ=273x169x10.1;WT=636;RE=2013.11;RM=2048;CP=80;OI=1;OV=4.2;",
    "101b helium 4g": "DS=10.1;RS=1280x800;SZ=261x161x9.9;WT=560;RM=1024;CP=121;OI=1;OV=6.0;",
    "101g10": "",
    "101g9": "DS=10.1;RS=1280x800;SZ=276x167x12.6;WT=675;RE=;RM=;CP=;OI=1;OV=4;",
    "101 g9": "->101g9",
    "40 titanium": "DS=4;RS=480x800;SZ=65x125x8.5;WT=124;RE=2014.02;RM=512;CP=24;OI=1;OV=4.2.2;",
    "45 platinum": "RE=2013;SZ=71.8x142.5x9.2;WT=150;DS=4.5;RS=540x960;OI=1;OV=4.1.2;CP=110;RM=1024;",
    "50 helium 4g": "RE=2014;SZ=71.5x145x9;WT=160;DS=5.0;RS=720x1280;OI=1;OV=4.3;CP=111;RM=1024;",
    "50 oxygen": "RE=2013;SZ=70.5x143x9.9;WT=130;DS=5.0;RS=1080x1920;OI=1;OV=4.2.2;CP=112;RM=1024;",
    "50 platinum": "RE=2013;SZ=72.4x143.4x8.9;WT=160.2;DS=5.0;RS=540x960;OI=1;OV=4.1.2;CP=110;RM=1024;",
    "50 power": "RE=2016.07;SZ=72.2x145x9.2;WT=148;DS=5.0;RS=720x1280;OI=1;OV=5.0;RM=2048;CP=56;",
    "50 titanium 4g": "",
    "50 titanium": "DS=5;RS=540x960;SZ=73.7x144.5x9.9;WT=160;RE=2013;RM=512;CP=24;OI=1;OV=4.2.2;",
    "53 titanium": "DS=5.3;RS=480x854;SZ=76.6x152.7x10.1;WT=200;RE=2013;RM=512;CP=24;OI=1;OV=4.2.2;",
    "55 platinum": "DS=5.5;RS=720x1280;SZ=72.6x144x8.7;WT=164;RM=1024;CP=88;OI=1;OV=5.1;SM=2;",
    "70 cobalt": "",
    "70 titanium": "DS=7;RS=1024x600;SZ=198x115x8.6;WT=280;RE=2013;RM=1024;CP=73;OI=1;OV=4.1;",
    "70 xenon": "",
    "70b titanium": "CP=73;",
    "70it2": "",
    "79 platinum": "",
    "80 carbon": "",
    "80 childpad": "",
    "80 cobalt": "DS=8;RS=1024x768;SZ=203x153.5x11.5;WT=470;RE=2012.11;RM=1024;CP=73;OI=1;OV=4;",
    "80 platinum": "",
    "80 titanium": "DS=8;RS=1024x768;SZ=200x154x9.9;WT=440;RE=2013.01;RM=1024;CP=73;OI=1;OV=4.1;",
    "80 xenon": "",
    "80b platinum": "",
    "80g9": "",
    "80xsk": "",
    "97 carbon": "DS=9.7;RS=1024x768;SZ=239x184x11.5;WT=620;RE=2012;RM=1024;CP=73;OI=1;OV=4;",
    "97 cobalt": "",
    "97 titaniumhd": "",
    "97 xenon": "DS=9.7;RS=1024x768;SZ=248x188x11.5;WT=650;RE=2012;RM=512;CP=73;OI=1;OV=4;",
    "97b platinum": "",
    "97b titanium": "",
    "a50ti": "->50 titanium 4g",
    "ac101bhe": "->101b helium 4g",
    "archos5": "->5",
    "chefpad": "",
    "familypad 2": "",
    "gamepad 2": "",
    "gamepad2": "->gamepad 2",
    "gamepad": "DS=7;RS=1024x600;SZ=229.8x118.7x15.4;WT=330;RE=2012.08;RM=1024;CP=73;OI=1;OV=4.1;",
    "a70gp": "->gamepad",
    "oxygen 63": "RE=2019.02;SZ=75.8x159.2x9.3;WT=162;DS=6.26;RS=720x1520;OI=1;OV=9.0;CP=109;RM=4096;",
    "yl-50 power": "->50 power",
    "oxygen 57": "RE=2019.02;SZ=70.7x147.8x9.3;WT=150;DS=5.71;RS=720x1520;OI=1;OV=9.0;CP=109;RM=3072;",
    "50 graphite": "RE=2017.02;SZ=72.7x144x8.9;WT=160;DS=5.0;RS=720x1280;OI=1;OV=7.0;CP=9;RM=1024;",
    "oxygen 68xl": "RE=2019.02;SZ=81.6x173.5x9;WT=214;DS=6.85;RS=640x1352;OI=1;OV=9.0;CP=47;RM=3072;",
    "saphir 50x": "RE=2018.04;SZ=76x146.6x13;WT=210;DS=5.0;RS=720x1280;OI=1;OV=7.0 ;CP=113;RM=2048;",
    "diamond omega": "DS=5.73;RS=1080x2040;SZ=72.7x147.5x8.5;WT=170;RE=2017.10;RM=8192;CP=25;OI=1;OV=7.1;",
    "diamond": "RE=2019.02;SZ=74.1x158.7x8.6;WT=166;DS=6.39;RS=1080x2340;OI=1;OV=9.0;CP=114;RM=4096;",
    "diamond alpha +": "RE=2017.08;SZ=72.5x146.7x7.5;WT=155;DS=5.2;RS=1080x1920;OI=1;OV=6.0;CP=115;RM=6144;",
    "diamond tab": "RE=2017.08;SZ=255x163x8;WT=510;DS=10.1;RS=2560x1600;OI=1;OV=7.0;CP=116;RM=4096;",
    "55 graphite": "RE=2017.02;SZ=77.2x157.8x7.8;WT=;DS=5.5;RS=720x1280;OI=1;OV=7.0;CP=9;RM=2048;",
    "sense 55s": "RE=2017.06;SZ=74.2x142.5x8;WT=130;DS=5.2;RS=1080x1920;OI=1;OV=7.0;RM=2048;CP=69;",
    "sense 50x": "RE=2017.06;SZ=78.9x153x12.6;WT=223;DS=5.0;RS=1080x1920;OI=1;OV=7.0;CP=69;RM=3072;",
    "diamond alpha": "RE=2017.06;SZ=72.5x146.7x7.5;WT=155;DS=5.2;RS=1080x1920;OI=1;OV=6.0;CP=115;RM=4096;",
    "diamond gamma": "RE=2017.06;SZ=77.0x155.6x8.0;WT=162;DS=5.5;RS=720x1280;OI=1;OV=7;CP=35;RM=3072;SM=2;",
    "50 saphir": "RE=2017.02;SZ=75.9x146.6x13.9;WT=;DS=5.0;RS=720x1280;OI=1;OV=6.0;CP=9;RM=2048;",
    "55b cobalt": "RE=2017.01;SZ=79x155x0;WT=188;DS=5.5;RS=720x1280;OI=1;OV=6.0;CP=10;RM=1024;",
    "50b cobalt": "RE=2017.01;SZ=72x144x0;WT=160;DS=5.0;RS=720x1280;OI=1;OV=6.0;CP=10;RM=1024;",
    "diamond 2 plus": "RE=2016.05;SZ=73.8x148.3x8.3;WT=193;DS=5.5;RS=1080x1920;OI=1;OV=6.0;CP=117;RM=4096;",
    "55 cobalt plus": "RE=2016.01;SZ=77x152.5x8.3;WT=130;DS=5.5;RS=720x1280;OI=1;OV=5.1.1;CP=56;RM=2048;",
    "50 cobalt": "RE=2016.01;SZ=71.5x141.5x9.2;WT=135;DS=5.0;RS=720x1280;OI=1;OV=5.1.1;CP=56;RM=1024;",
    "diamond plus": "RE=2015.09;SZ=76.4x152.6x8.4;WT=160;DS=5.5;RS=1080x1920;OI=1;OV=5.1.1;CP=118;RM=2048;",
    "diamond s": "RE=2015.09;SZ=71.5x145x6.5;WT=118;DS=5.0;RS=720x1280;OI=1;OV=5.1.1;CP=118;RM=2048;",
    "50d helium 4g": "RE=2015.07;SZ=72.2x146x8.4;WT=154;DS=5.0;RS=720x1280;OI=1;OV=5.1;CP=17;RM=1024;",
    "50b helium 4g": "RE=2014.10;SZ=71.5x145x9;WT=160;DS=5.0;RS=720x1280;OI=1;OV=4.4.4;CP=17;RM=1024;",
    "50 diamond": "RE=2014.10;SZ=70.4x146x8;WT=142;DS=5.0;RS=1080x1920;OI=1;OV=4.4.4;CP=22;RM=2048;",
    "40 cesium": "RE=2014.09;SZ=;WT=;DS=4.0;RS=480x800;OI=4;OV=8.1;CP=119;RM=512;",
    "45c platinum": "RE=2014.08;SZ=67.5x133x8.9;WT=138;DS=4.5;RS=480x854;OI=1;OV=4.4.2;CP=7;RM=512;",
    "40c titanium": "RE=2014.02;SZ=65x126x10;WT=110;DS=4.0;RS=480x800;OI=1;OV=4.4;CP=24;RM=512;",
    "50b platinum": "RE=2014.08;SZ=73x146.8x8.3;WT=160;DS=5.0;RS=540x960;OI=1;OV=4.4.2;CP=7;RM=512;",
    "80 helium 4g": "DS=8;RS=1024x768;SZ=213x155x11.3;WT=430;RE=2014.02;RM=1024;CP=111;OI=1;OV=4.3;",
    "64 xenon": "RE=2014.02;SZ=90.6x170.7x9.3;WT=232;DS=6.4;RS=720x1280;OI=1;OV=4.2.2;CP=7;RM=1024;",
    "50c oxygen": "RE=2014.02;SZ=69.8x145x7.64;WT=140;DS=5.0;RS=720x1280;OI=1;OV=4.2.2;CP=2;RM=1024;",
    "40b titanium": "RE=2014.02;SZ=61x125x8.5;WT=115;DS=4.0;RS=480x800;OI=1;OV=4.2.2;RM=512;CP=24;",
    "45 helium 4g": "RE=2014;SZ=67x135x9.8;WT=140;DS=4.5;RS=480x800;OI=1;OV=4.3;CP=111;RM=1024;",
    "53 platinum": "RE=2013;SZ=78x153.6x9.3;WT=189.9;DS=5.3;RS=540x960;OI=1;OV=4.1.2;CP=110;RM=1024;",
    "101 oxygen": "DS=10.1;RS=1920x1200;SZ=240x172x10;WT=510;RE=2015.08;RM=2048;CP=82;OI=1;OV=4.4.4;",
    "101 helium 4g": "DS=10.1;RS=1280x800;SZ=250x174x10;WT=538;RE=;RM=1024;CP=120;OI=1;OV=4.4;",
    "101 internet tablet": "DS=10.1;RS=1024x600;SZ=270x150x12;WT=480;RE=;RM=;CP=;OI=1;OV=2.2;",
    "101 saphir": "DS=10.1;RS=1280x800;SZ=181x265.4x13.4;WT=600;RE=;RM=1024;CP=144;OI=1;OV=7;",
    "101 xs": "DS=10.1;RS=1280x800;SZ=273x170x8;WT=600;RE=;RM=1024;CP=95;OI=1;OV=4.0.3;",
    "28 internet tablet": "DS=2.8;RS=240x320;SZ=54x100x9;WT=68;RE=;RM=;CP=;OI=1;OV=2.2;",
    "32 internet tablet": "DS=3.2;RS=240x400;SZ=55x105x9;WT=72;RE=;RM=;CP=;OI=1;OV=2.2;",
    "43 internet tablet": "DS=4.3;RS=854x480;SZ=65x135x9;WT=130;RE=;RM=;CP=;OI=1;OV=2.2;",
    "45 titanium": "DS=4.5;RS=480x854;SZ=68.2x133.8x10;WT=152;RE=;RM=512;CP=24;OI=1;OV=4.2.2;",
    "45b helium 4g": "DS=4.5;RS=480x854;SZ=67x135x9.75;WT=140;RE=;RM=512;CP=17;OI=1;OV=4.4.4;",
    "5 internet tablet": "DS=4.8;RS=480x800;SZ=;WT=286;RE=;RM=256;CP=;OI=1;OV=1.6;",
    "50 cesium": "DS=5;RS=720x1280;SZ=72.5x147x8.5;WT=150;RE=2015.08;RM=1024;CP=10;OS=;",
    "50 helium plus": "DS=5;RS=720x1280;SZ=71x143x9.3;WT=161;RE=2015.06;RM=1024;CP=56;OI=1;OV=5.1;",
    "50c helium 4g": "DS=5;RS=480x854;SZ=73.5x146x9.1;WT=;RE=;RM=1024;CP=154;OI=1;OV=4.4.4;",
    "50e helium 4g": "DS=5;RS=720x1280;SZ=72.5x147x8.5;WT=150;RE=;RM=1024;CP=10;OI=1;OV=5.1;",
    "55 helium plus": "DS=5.5;RS=720x1280;SZ=78x155x8.3;WT=168;RE=2015.06;RM=1024;CP=56;OI=1;OV=5.1;",
    "7 home tablet": "DS=7;RS=480x800;SZ=107x203x12;WT=388;RE=;RM=;CP=;OI=1;OV=2.1;",
    "70 helium 4g": "DS=7;RS=1024x600;SZ=190x108.5x9.4;WT=260;RE=;RM=1024;CP=120;OI=1;OV=4.4;",
    "70 internet tablet": "DS=7;RS=800x480;SZ=201x114x14;WT=400;RE=;RM=;CP=;OI=1;OV=2.2;",
    "70b internet tablet": "DS=7;RS=1024x600;SZ=201x115x11.5;WT=330;RE=2011.12;RM=;CP=;OI=1;OV=3.2;",
    "8 home tablet": "DS=8;RS=800x600;SZ=220x263x12;WT=800;RE=;RM=;CP=;OS=;",
    "80 cesium": "DS=8;RS=800x1280;SZ=123x215x8.6;WT=360;RE=;RM=1024;CP=155;OS=;",
    "80 g9": "DS=8;RS=1024x768;SZ=226x155x11.7;WT=482;RE=;RM=;CP=;OI=1;OV=4;",
    "80b helium 4g": "DS=8;RS=1280x800;SZ=210x122x9.3;WT=320;RE=;RM=1024;CP=120;OI=1;OV=4.4;",
    "90 neon": "DS=9;RS=800x480;SZ=239x148x11.6;WT=480;RE=;RM=1024;CP=;OI=1;OV=4.2;",
    "97 neon": "DS=9.7;RS=1024x768;SZ=241x186x11.5;WT=648;RE=;RM=;CP=;OI=1;OV=4.2;",
    "97 titanium hd": "DS=9.7;RS=2048x1536;SZ=240x184x9;WT=640;RE=;RM=1024;CP=;OI=1;OV=4.1;",
    "arnova 7": "DS=7;RS=800x480;SZ=120x193.3x12.6;WT=340;RE=;RM=;CP=;OI=1;OV=2.2;",
    "childpad": "DS=7;RS=800x480;SZ=223x142x12.2;WT=380;RE=;RM=1024;CP=;OI=1;OV=4;",
    "diamond 2 note": "DS=6;RS=1440x2560;SZ=82.7x158.35x8.7;WT=150;RE=2016.02;RM=3072;CP=85;OI=1;OV=6;"
  },
  "arian space": {
    "70": "DS=7.0;RS=1024x600;RM=512;OI=1;OV=5.1;SZ=191x110x10;WT=260;CP=331;",
    "71": "DS=7.1;RS=1024x600;RM=512;OI=1;OV=7.0;SM=2;SZ=191x110x10;WT=260;CP=330;",
    "80": "DS=8;RS=1024x600;RM=512;OI=1;OV=7.0;SM=2;SZ=208x123x10;WT=312;CP=287;",
    "100": "DS=10.1;RS=1024x600;RM=512;OI=1;OV=7.0;SM=2;SZ=250x159x10;WT=508;CP=330;",
    "100 st1004pg": "->100",
    "71 st7002pg": "->71"
  },
  "ark": {
    "benefit-i1": "->benefit i1",
    "benefa1": "->benefit a1",
    "benefa2": "->benefit a2",
    "benefa3": "->benefit a3",
    "benefi2": "->benefit i2",
    "benefi3": "->benefit i3",
    "benefit a1": "RS=480x800;DS=4;RM=512;CP=17;OI=1;OV=4.4;SZ=63.9x123.6x10;WT=125;SM=2;",
    "benefit a2": "",
    "benefit a3": "",
    "benefit i1": "",
    "benefit i2": "",
    "benefit i3": "",
    "benefit m1 3g": "",
    "benefit m1": "",
    "benefit m1s": "",
    "benefit m2": "",
    "benefit m2c": "",
    "benefit m3s": "",
    "benefit m4": "",
    "benefit m5 plus": "",
    "benefit m501": "",
    "benefit m502": "",
    "benefit m503": "",
    "benefit m505": "",
    "benefit m506": "",
    "benefit m5": "",
    "benefit m6": "",
    "benefit m7": "",
    "benefit m8": "",
    "benefit note 1": "",
    "benefit s401": "",
    "benefit s402": "",
    "benefit s403": "",
    "benefit s404": "",
    "benefit s451": "",
    "benefit s453": "",
    "benefit s501": "",
    "benefit s502 plus": "",
    "benefit s503 max": "",
    "benefit s503": "",
    "benefit s505": "",
    "benefm1 3g": "->benefit m1 3g",
    "benefm1": "->benefit m1",
    "benefm1s": "->benefit m1s",
    "benefm2": "->benefit m2",
    "benefm2c": "->benefit m2c",
    "benefm3s": "->benefit m3s",
    "benefm4": "->benefit m4",
    "benefm5 plus": "->benefit m5 plus",
    "benefm501": "->benefit m501",
    "benefm502": "->benefit m502",
    "benefm503": "->benefit m503",
    "benefm505": "->benefit m505",
    "benefm506": "->benefit m506",
    "benefm5": "->benefit m5",
    "benefm6": "->benefit m6",
    "benefm7": "->benefit m7",
    "benefm8": "->benefit m8",
    "benefnote 1": "->benefit note 1",
    "benefs401": "->benefit s401",
    "benefs402": "->benefit s402",
    "benefs403": "->benefit s403",
    "benefs404": "->benefit s404",
    "benefs451": "->benefit s451",
    "benefs453": "->benefit s453",
    "benefs501": "->benefit s501",
    "benefs502 plus": "->benefit s502 plus",
    "benefs503 max": "->benefit s503 max",
    "benefs503": "->benefit s503",
    "benefs505": "->benefit s505",
    "edge a5hd": "",
    "elf s8": "",
    "icon r40+": "",
    "icon r45": "",
    "impulse p1 plus": "",
    "impulse p1+": "->impulse p1 plus",
    "impulse p1": "",
    "impulse p2": "",
    "wizard 1": "",
    "wizard 2": ""
  },
  "armphone": {
    "tsd octa a0520p": "",
    "tsd quadra a0505p": "",
    "tsd quadra a0509p": ""
  },
  "arnova": {
    "8": "",
    "10": "",
    "10 g2": "",
    "101 g4": "",
    "10b g2": "",
    "10b g3": "",
    "10c g3": "",
    "10d g3": "",
    "7 g2": "",
    "7 g3": "",
    "7b g3": "",
    "7c g2": "",
    "7c g3": "",
    "7d g3": "",
    "7f g3": "",
    "7h g3": "",
    "8 g2": "",
    "8 g3": "",
    "8b g3": "",
    "8c g3": "",
    "9 g2": "",
    "9 g3": "",
    "90 g3": "",
    "90 g4": "",
    "90g3": "->90 g3",
    "97 g4": "",
    "97g4": "->97 g4",
    "9i g2": "",
    "a101b2-lz": "->10 g2",
    "a101b": "->10",
    "a101c": "->10 g2",
    "a80ksc": "->8",
    "an10bg2": "->10b g2",
    "an10bg2dt": "->10b g2",
    "an10bg2i": "->10b g2",
    "an10bg3-lz": "->10b g3",
    "an10bg3": "->10b g3",
    "an10cg3": "->10c g3",
    "an10dg3": "->10d g3",
    "an10g2-lz": "->10 g2",
    "an10g2": "->10 g2",
    "an7bg3": "->7b g3",
    "an7cg2": "->7c g2",
    "an7cg3": "->7c g3",
    "an7dg3-cp": "->childpad",
    "an7dg3": "->7d g3",
    "an7dg3b": "->7d g3",
    "an7fg3": "->7f g3",
    "an7g2": "->7 g2",
    "an7g2dte": "->7 g2",
    "an7g2i": "->7 g2",
    "an7g3": "->7 g3",
    "an7hg3": "->7h g3",
    "an8bg3-lz": "->8b g3",
    "an8bg3": "->8b g3",
    "an8cg3": "->8c g3",
    "an8g2i": "->8 g2",
    "an8g3": "->8 g3",
    "an9g2": "->9 g2",
    "an9g2i": "->9i g2",
    "an9g3": "->9 g3",
    "archm901": "->m901",
    "childpad": "",
    "m901": ""
  },
  "arris": {
    "fs-ars-01b": "",
    "ipc1100": "",
    "ipc1100p2": "",
    "vms1100": ""
  },
  "artel": {
    "air": "",
    "choice": "",
    "connect": "",
    "f5": "",
    "gap yo q": "",
    "gap yoq": "->gap yo q",
    "komi 3": "",
    "komi3": "->komi 3",
    "m5": "",
    "nova": "",
    "premium": "",
    "quadro pro": "",
    "quadro": "",
    "sentinel x 8g": "",
    "sentinel x d5": "",
    "sentinel x": "",
    "star": "",
    "style": "",
    "tesla": "",
    "tomchi": "",
    "u3 4g": "",
    "v5": "",
    "x4": "",
    "z5": ""
  },
  "artizlee": {
    "atl-16": "",
    "atl-21 plus": "",
    "atl-21": "",
    "atl-21plus": "->atl-21 plus",
    "atl-21t": "",
    "atl-21x": "",
    "atl-26": "",
    "atl-31": "",
    "s9": ""
  },
  "asano": {
    "32lf7130s 32.0\"": "",
    "32lf7130s": "->32lf7130s 32.0\""
  },
  "asanzo": {
    "a2": "DS=5.0;RS=480x960;SZ=66.2x143x9.75;WT=160;RE=2019.03;RM=1024;CP=58;OI=1;OV=8.1;",
    "s2": "",
    "s3 plus": "",
    "s3": "",
    "s5": "",
    "z5": ""
  },
  "ask": {
    "791sp 3g": "",
    "sp581 hd": "",
    "sp618": ""
  },
  "assistant": {
    "agio": "OI=1;OV=6.0;SM=2;WT=136;SZ=71x142x8.7;RS=720x1280;DS=5;CP=5;RM=1024;RE=2017;",
    "ap-106 force": "",
    "ap-106": "->ap-106 force",
    "ap-107g": "",
    "ap-108": "->cetus",
    "ap-109": "",
    "ap-110n": "",
    "ap-115g": "",
    "ap-719": "",
    "ap-721n force": "",
    "ap-721n": "->ap-721n force",
    "ap-727g": "",
    "ap-753g": "",
    "ap-757g": "",
    "ap-941": "",
    "as 401l": "->asper",
    "as 501": "->club",
    "as 601l": "->as-601l",
    "as-4411": "->unami",
    "as-4421": "->unami",
    "as-502": "->shot",
    "as-503": "->target",
    "as-5411 max": "->max ritm",
    "as-5412 max": "",
    "as-5421": "->surf",
    "as-5432": "->agio",
    "as-5433 max secret": "->max secret",
    "as-5433 secret": "->secret",
    "as-5434": "->club",
    "as-5435": "->shine",
    "as-5436 grid": "->grid",
    "as-601l": "",
    "as-6431": "->rider",
    "asper": "",
    "cetus": "DS=10;RS=1920x1200;SZ=241x171x9.5;WT=;RE=2018;OI=1;OV=8.0;RM=2048;CP=40;",
    "club": "",
    "grid": "",
    "max ritm": "",
    "max secret": "",
    "prima": "",
    "rider": "",
    "secret": "",
    "shine": "",
    "shot": "",
    "surf": "",
    "target": "",
    "unami": ""
  },
  "asus": {
    "zenpad 10": "DS=10.1;RS=1920x1200;SZ=251.77x172.17x8.95;WT=490;RE=2017.05;RM=2048;CP=74;OI=1;OV=7;",
    "p00c": "->zenpad 10",
    "z300m": "->zenpad 10",
    "p021": "->zenpad 10",
    "p023": "->zenpad 10",
    "p028": "->zenpad 10",
    "p01t 1": "->zenpad 10",
    "p00l": "->zenpad 10",
    "zenfone zoom": "RE=2015.09;SZ=78.8x158.9x12;WT=185;DS=5.5;RS=1080x1920;OI=1;OV=5.0;CP=209;RM=4096;",
    "zx551ml": "->zenfone zoom",
    "z00xs": "->zenfone zoom",
    "zenfone v live": "RE=2017.09;SZ=72.6x146.6x7.6;WT=147.1;DS=5.2;RS=1080x1920;OI=1;OV=7;CP=173;RM=4096;",
    "a009": "->zenfone v live",
    "v520kl": "->zenfone v live",
    "zenfone v": "->zenfone v live",
    "a006": "->zenfone v live",
    "zenfone selfie": "RE=2015.06;SZ=77.2x156.5x10.8;WT=170;DS=5.5;RS=1080x1920;OI=1;OV=5.0;CP=22;RM=2048;",
    "z00ud": "->zenfone selfie",
    "z00udh": "->zenfone selfie",
    "z00udc": "->zenfone selfie",
    "z00udb": "->zenfone selfie",
    "zd551kl": "->zenfone selfie",
    "zenfone pegasus": "RE=2014.12;SZ=73x146x9.9;WT=140;DS=5.0;RS=720x1280;OI=1;OV=4.4;CP=154;RM=2048;",
    "x002": "->zenfone pegasus",
    "x003": "->zenfone pegasus",
    "zenfone pegasus 5000": "CP=118;RE=2016;RM=2048;OI=1;OV=5.1;SZ=76.4x155x9;RS=1080x1920;DS=5.5;SM=2;",
    "x005": "->zenfone pegasus 5000",
    "zenfone pegasus 3s max": "RE=2017.01;SZ=73.7x149.5x8.9;WT=175;DS=5.2;RS=720x1280;OI=1;OV=7;CP=40;RM=3072;",
    "zc521tl": "->zenfone pegasus 3s max",
    "x00gd": "->zenfone pegasus 3s max",
    "zenfone pegasus 2 plus": "RE=2015.07;SZ=75.5x151.8x8.1;WT=156;DS=5.5;RS=1080x1920;OI=1;OV=5.1.1;CP=22;RM=3072;",
    "x550": "->zenfone pegasus 2 plus",
    "zenfone max": "RE=2016.05;SZ=77.5x156x10.6;WT=202;DS=5.5;RS=720x1280;OI=1;OV=6.0.1;CP=22;RM=2048;",
    "zc550kl": "->zenfone max",
    "z010d": "->zenfone max",
    "zenfone max pro": "->zenfone max pro m1",
    "zb602kl": "->zenfone max pro",
    "zenfone max pro m2": "RE=2018.12;SZ=75.5x157.9x8.5;WT=175;DS=6.26;RS=1080x2280;OI=1;OV=8.1;CP=61;RM=3072;",
    "zb631kl": "->zenfone max pro m2",
    "x01bda": "->zenfone max pro m2",
    "zenfone max pro m1": "RE=2018.04;SZ=76x159x8.5;WT=180;DS=5.99;RS=1080x2160;OI=1;OV=8.1;CP=205;RM=3072;",
    "zb601kl": "->zenfone max pro m1",
    "x00t": "->zenfone max pro m1",
    "x00td": "->zenfone max pro m1",
    "x00tdb": "->zenfone max pro m1",
    "x00tda": "->zenfone max pro m1",
    "x00tde": "->zenfone max pro m1",
    "zenfone max plus m1": "RE=2017.11;SZ=73x152.6x8.8;WT=160;DS=5.7;RS=1080x2160;OI=1;OV=7;CP=162;RM=2048;",
    "x018d": "->zenfone max plus m1",
    "zb570tl": "->zenfone max plus m1",
    "zenfone max m2": "RE=2018.12;SZ=76.3x158.4x7.7;WT=160;DS=6.26;RS=720x1520;OI=1;OV=8.1;CP=64;RM=3072;",
    "zb633kl": "->zenfone max m2",
    "x01ad": "->zenfone max m2",
    "x01bd": "->zenfone max m2",
    "zenfone max m1": "RE=2018.02;SZ=70.9x147.3x8.7;WT=150;DS=5.5;RS=720x1440;OI=1;OV=8;CP=20;RM=2048",
    "zb555kl": "->zenfone max m1",
    "x00pd": "->zenfone max m1",
    "x00ps": "->zenfone max m1",
    "zenfone live": "RE=2017.02;SZ=71.7x141.2x8;WT=120;DS=5.0;RS=720x1280;OI=1;OV=6.0;CP=17;RM=2048;",
    "a007": "->zenfone live",
    "z00yd": "->zenfone live",
    "zb501kl": "->zenfone live",
    "zb553kl": "->zenfone live;RE=2017.10;SZ=75.9x155.7x7.9;WT=144;DS=5.5;RS=720x1280;OI=1;OV=7;CP=20;RM=2048;",
    "zenfone live l1": "RE=2018.05;SZ=71.8x147.3x8.2;WT=140;DS=5.5;RS=720x1440;OI=1;OV=8.0;CP=20;SM=2;RM=2048;",
    "x00rd": "->zenfone live l1;RM=1024;",
    "g552kl": "->zenfone live l1;RM=1024;OI=1;OV=7.0;",
    "za550kl": "->zenfone live l1",
    "zenfone lite l1": "RE=2018.10;SZ=71.8x147.3x8.2;WT=140;DS=5.45;RS=720x1440;OI=1;OV=8.0;CP=43;RM=2048;",
    "za551kl": "->zenfone lite l1",
    "g553kl": "->zenfone lite l1",
    "zenfone go": "RE=2015.12;SZ=67x135x10.1;WT=133.5;DS=4.5;RS=480x854;OI=1;OV=5.1;CP=58;RM=1024;",
    "zc451tg": "->zenfone go",
    "x009da": "->zb450kl",
    "zb450kl": "->zenfone go;RE=2016.07;CP=17;OI=1;OV=6;DS=4.5;WT=135;SZ=66.7x136.5x11.2;",
    "x007d": "->zenfone go",
    "l001": "->zenfone go",
    "x013d": "->zb551kl",
    "x013dc": "->zb551kl",
    "x013da": "->zb551kl",
    "x013db": "->zb551kl",
    "zb551kl": "->zenfone go;RE=2016.04;SZ=76.9x151x10.7;WT=160;DS=5.5;RS=720x1280;OI=1;OV=5.1;CP=78;RM=2048;",
    "x00bd": "->zb500kg",
    "zb500kg": "->zenfone go",
    "zb500kl": "->zenfone go",
    "z00vd": "->zenfone go",
    "z00sd": "->zc451tg",
    "zenfone go tv": "DS=5.5;RS=720x1280;SZ=76.9x151x10.7;WT=160;CP=111;RM=2048;SM=2;OI=1;OV=5.1;RE=2016.05;",
    "g550kl": "->zenfone go tv",
    "zenfone go plus": "RE=2016.04;SZ=66.7x136.5x11.2;WT=125;DS=4.5;RS=480x854;OI=1;OV=5.1;CP=33;RM=1024;",
    "zb452kg": "->zenfone go plus",
    "x014d": "->zenfone go plus",
    "zenfone c": "DS=4.5;RS=480x854;SZ=67x136.5x10.9;WT=150;RE=2015.01;RM=1024;CP=206;OI=1;OV=4.4.2;",
    "z007": "->zenfone c",
    "zc451cg": "->zenfone c",
    "a002": "->zenfone ar",
    "a002a": "->zenfone ar",
    "v570kl": "->zenfone ar",
    "zs571kl": "->zenfone ar",
    "zenfone ar": "DS=5.7;RS=1440x2560;SZ=77.7x158.67x8.95;WT=170;RE=2017.01;RM=6144;CP=208;OI=1;OV=7;",
    "zs671ks": "->zenfone 7 pro",
    "i002dd": "->zenfone 7 pro",
    "zenfone 7 pro": "DS=6.7;RS=1080x2400;SZ=77.28x165.08x9.6;WT=230;RE=2020.08;RM=8192;CP=207;OI=1;OV=11;",
    "i002d": "->zenfone 7",
    "zs670ks": "->zenfone 7",
    "zenfone 7": "RE=2020.08;SZ=77.3x165.1x9.6;WT=230;DS=6.67;RS=1080x2400;OI=1;OV=10;CP=207;RM=6144;",
    "zenfone 6": "RE=2014.01;SZ=84.3x166.9x9.9;WT=196;DS=6.0;RS=720x1280;OI=1;OV=4.3 ;CP=131;RM=2048;",
    "t00g": "->zenfone 6",
    "z002": "->zenfone 6",
    "i01wd": "->zs630kl",
    "i01wdx": "->zs630kl",
    "zs630kl": "->zenfone 6;RE=2019.05;SZ=75.4x159.1x9.2;WT=190;DS=6.4;RS=1080x2340;OI=1;OV=9.0;CP=197;RM=6144;",
    "zenfone 5z": "RE=2018.02;SZ=75.7x153x7.9;WT=165;DS=6.2;RS=1080x2246;OI=1;OV=8.0;CP=198;RM=4096;",
    "z01rd": "->zenfone 5z",
    "zs620kl": "->zenfone 5z",
    "zenfone 5": "DS=5;RS=720x1280;SZ=72.8x148.2x10.3;WT=145;RE=2014.01;RM=2048;CP=131;OI=1;OV=4.3;",
    "t00j": "->zenfone 5",
    "t00f": "->a501cg",
    "a502cg": "->zenfone 5",
    "a500cg": "->zenfone 5",
    "a501cg": "->zenfone 5;RE=2015.01;CP=206;OI=1;OV=4.3;",
    "x00qda": "->zenfone 5",
    "x00qd": "->ze620kl",
    "x00qsa": "->ze620kl",
    "zf620kl": "->ze620kl",
    "ze620kl": "->>zenfone 5;DS=6.2;RS=1080x2246;SZ=75.65x153x7.7;WT=165;RE=2018.02;RM=6144;CP=205;OI=1;OV=9;",
    "a500kl": "->zenfone 5 lte",
    "zenfone 5 lte": "RE=2014.06;SZ=72.8x148.2x10.3;WT=145;DS=5.0;RS=720x1280;OI=1;OV=4.4.2;CP=111;RM=1024;",
    "t00p": "->zenfone 5 lte",
    "zenfone 5 lite": "RE=2018.09;SZ=76.2x160.6x7.8;WT=168;DS=6.0;RS=1080x2160;OI=1;OV=7.1.1;CP=19;RM=3072;",
    "zc600kl": "->zenfone 5 lite",
    "x017d": "->zenfone 5 lite",
    "x017da": "->zenfone 5 lite",
    "t00k": "->zenfone 5 lite",
    "x00lda": "->zenfone 4 selfie",
    "zd553kl": "->zenfone 4 selfie",
    "zenfone 4 selfie": "DS=5.5;RS=720x1280;SZ=76.2x155.7x7.9;WT=144;RE=2017.08;RM=4096;CP=43;OI=1;OV=8.1;",
    "zenfone 4 selfie pro": "RE=2017.08;SZ=74.8x154x6.9;WT=147;DS=5.5;RS=1080x1920;OI=1;OV=7 ;CP=204;RM=4096;",
    "zd552kl": "->zenfone 4 selfie pro",
    "z01md": "->zenfone 4 selfie pro",
    "z01mda": "->zenfone 4 selfie pro",
    "zenfone 4 pro": "RE=2017.08;SZ=75.6x156.9x7.6;WT=175;DS=5.5;RS=1080x1920;OI=1;OV=7.1.1;CP=25;RM=6144;",
    "zs551kl": "->zenfone 4 pro",
    "z01gd": "->zenfone 4 pro",
    "z01gs": "->zenfone 4 pro",
    "zc554kl": "->zenfone 4 max",
    "x015d": "->zenfone 4 max plus",
    "zenfone 4 max plus": "DS=5.5;RS=720x1280;SZ=76.9x154x8.9;WT=181;RE=2017.07;OI=1;OV=7;CP=20;RM=3072;",
    "z012s": "->zenfone 3",
    "z012db": "->zenfone 3",
    "z012d": "->zenfone 3",
    "z012da": "->zenfone 3",
    "z012dc": "->zenfone 3",
    "z012de": "->zenfone 3",
    "z017db": "->zenfone 3",
    "z017d": "->zenfone 3",
    "z017da": "->zenfone 3",
    "ze552kl": "->zenfone 3",
    "zenfone 3": "DS=5.5;RS=1080x1920;SZ=77.38x152.59x7.69;WT=155;RE=2016.05;RM=4096;CP=204;OI=1;OV=6.0.1;",
    "z01hd": "->zenfone 3 zoom",
    "z01hda": "->zenfone 3 zoom",
    "ze553kl": "->zenfone 3 zoom",
    "zenfone 3 zoom": "DS=5.5;RS=1080x1920;SZ=77x154.3x7.99;WT=170;RE=2017.02;RM=4096;CP=204;OI=1;OV=6.1;",
    "a001": "->zenfone 3 ultra",
    "zenfone 3 ultra": "DS=6.8;RS=1080x1920;SZ=93.9x186.4x6.8;WT=233;RE=2016.05;RM=4096;CP=115;OI=1;OV=6;",
    "x00dd": "->zc553kl",
    "zc553kl": "->zenfone 3 max;DS=5.5;RS=1080x1920;SZ=76.24x151.4x8.3;WT=175;RE=2016;OI=1;OV=6.0;RM=2048;CP=43;",
    "x00ds": "->zenfone 3 max",
    "x008dc": "->zenfone 3 max",
    "x008db": "->zenfone 3 max",
    "zenfone 3 max": "DS=5.2;RS=720x1280;SZ=73.7x149.5x8.55;WT=148;RE=2016;RM=3072;CP=86;OI=1;OV=7;",
    "zenfone 3 laser": "DS=5.5;RS=1080x1920;SZ=76x149x7.9;WT=150;RE=2016.07;RM=4096;CP=43;OI=1;OV=7.1;",
    "zc551kl": "->zenfone 3 laser",
    "z01bd": "->zenfone 3 laser",
    "z01bs": "->zenfone 3 laser",
    "z01bda": "->zenfone 3 laser",
    "ze551ml": "->zenfone 2;WT=170;OI=1;OV=5.0.1;",
    "ux360cak": "->zenbook flip",
    "zenbook flip": "DS=13.3;RS=1920x1080;SZ=322.8x219.9x13.7;WT=1290;RM=8192;CP=203;RE=2019.04;OS=Windows 10;",
    "ze500cl": "->zenfone 2;DS=5;RS=720x1280;SZ=71.5x148.1x10.9;WT=155;CP=18;OI=1;OV=5;",
    "zenfone 2": "DS=5.5;RS=1080x1920;SZ=77.2x152.5x10.9;WT=170;RE=2015.03;RM=2048;CP=16;OI=1;OV=6;SM=2;",
    "ze500kl": "->zenfone 2 laser",
    "zenfone 2 laser": "DS=5;RS=720x1280;SZ=71.5x143.7x10.5;WT=140;RE=2015.08;RM=2048;CP=17;OI=1;OV=5;SM=2;",
    "zenfone 2e": "DS=5;RS=720x1280;SZ=71.4x148.1x10.9;WT=155;RE=2015.07;RM=1024;CP=18;OI=1;OV=5;",
    "ze554kl": "->zenfone 4",
    "z01kda": "->zenfone 4",
    "z01ks": "->zenfone 4",
    "x00ld": "->zenfone 4",
    "z01kd": "->zenfone 4",
    "zenfone 4": "DS=5.5;RS=1080x1920;SZ=75.2x155.4x7.7;WT=165;RE=2017.08;RM=4096;CP=19;OI=1;OV=7.1;SM=2;TT=71200;",
    "zc520kl": "->zenfone 4 max",
    "x00hd": "->zenfone 4 max",
    "x00id": "->zenfone 4 max",
    "x00kd": "->zenfone 4 max",
    "zenfone 4 max": "DS=5.2;RS=720x1280;SZ=73.3x150.5x8.73;WT=156;RE=2017.10;RM=2048;CP=20;OI=1;OV=7;SM=2;",
    "chromebook flip c100pa": "",
    "chromebook flip c101pa": "",
    "chromebook flip c302": "",
    "eee pad memo 171": "",
    "eee pad slider sl101": "",
    "fonepad 7 dual sim": "",
    "fonepad 7": "",
    "fonepad 8": "DS=8;RS=800x1280;SZ=120x214x8.9;WT=328;RE=2014.06;RM=2048;CP=16;OI=1;OV=4.4;",
    "fonepad note 6": "DS=6;RS=1080x1920;SZ=88.8x164.8x10.3;WT=210;RE=2013.10;RM=2048;CP=131;OI=1;OV=4.4;",
    "fonepad": "DS=7;RS=1280x800;SZ=120.1x196.4x10.4;WT=340;RE=2013.02;RM=1024;CP=132;OI=1;OV=4.1;",
    "galaxy6": "",
    "k00c": "->transformer pad tf701t",
    "k00e": "->fonepad 7",
    "k00g": "->fonepad note 6",
    "k00s": "->memo pad hd 7 dual sim",
    "k00z": "->fonepad 7 dual sim",
    "k012": "->fonepad 7",
    "k014": "->memo pad 8.9",
    "k016": "->fonepad 8",
    "k018": "->transformer pad tf103cg",
    "k01b": "->transformer pad tf303k",
    "k01e": "->memo pad 10 me103k",
    "k50in": "",
    "k54l": "",
    "me171": "->eee pad memo 171",
    "me172v": "->memo pad",
    "me301t": "->memo pad smart 10",
    "me302kl": "->memo pad fhd 10 lte",
    "me371mg": "->fonepad",
    "memo pad 10 me103k": "",
    "memo pad 10": "DS=10.1;RS=1280x800;SZ=256x174x9.9;WT=525;RE=2014.10;RM=1024;CP=134;OI=1;OV=4.4.2;",
    "k00f": "->memo pad 10",
    "memo pad 7": "DS=7;RS=1280x800;SZ=189.3x113.7x9.6;WT=295;RE=;RM=1024;CP=135;OI=1;OV=4.4.2;",
    "k01a": "->memo pad 7",
    "k013": "->memo pad 7",
    "k00r": "->memo pad 7",
    "k007": "->memo pad 7",
    "memo pad 8.9": "",
    "memo pad 8": "DS=8;RS=1920x1200;SZ=213x123x7.45;WT=299;RE=2014.06;RM=2048;CP=136;OI=1;OV=4.4;",
    "k00l": "->memo pad 8",
    "k011": "->memo pad 8",
    "memo pad fhd 10 lte": "",
    "memo pad fhd 10": "DS=10;RS=1920x1200;SZ=264.6x182.4x9.5;WT=580;RE=2013.06;RM=2048;CP=18;OI=1;OV=4.2;",
    "me302c": "->memo pad fhd 10",
    "memo pad hd 7 dual sim": "",
    "memo pad hd 7": "DS=7;RS=1280x800;SZ=196.8x120.6x10.8;WT=302;RE=2013.07;RM=1024;CP=12;OI=1;OV=4.2.1;",
    "k00u": "->memo pad hd 7",
    "me173x": "->memo pad hd 7",
    "memo pad smart 10": "DS=10.1;RS=1280x800;SZ=263x180.8x9.9;WT=580;RE=2013.02;RM=1024;CP=137;OI=1;OV=4.1;",
    "memo pad": "DS=7;RS=1024x600;SZ=196x119x11;WT=370;RE=2013.01;RM=1024;CP=133;OI=1;OV=4.1;",
    "zenpad z10": "RE=2016.10;SZ=242.3x166.1x7.1;WT=490;DS=9.7;RS=2048x1536;OI=1;OV=6.0;CP=328;RM=3072;",
    "p001": "->zenpad z10",
    "zenpad z8": "RE=2016.06;SZ=205.2x136.4x7.62;WT=320.1;DS=7.9;RS=2048x1536;OI=1;OV=6.0;CP=328;RM=2048;",
    "p008": "->zenpad z8",
    "zenpad 7.0": "RE=2015.08;SZ=189x110.9x8.7;WT=272;DS=7.0;RS=1280x800;OI=1;OV=5.0;CP=211;RM=2048;",
    "p01v": "->zenpad 7.0",
    "p01w": "->zenpad 7.0",
    "zenpad c 7.0": "RE=2015.06;SZ=189x108x8.4;WT=265;DS=7.0;RS=1024x600;OI=1;OV=5.0;CP=329;RM=1024;",
    "p01y s": "->zenpad c 7.0",
    "p01y": "->zenpad c 7.0",
    "p01z": "->zenpad c 7.0",
    "p00a": "->zenpad 8.0",
    "p00i": "->zenpad 3s 10 lte",
    "p00j": "->zenpad z8s",
    "p01m": "->zenpad s 8.0",
    "p01ma": "->zenpad s 8.0",
    "p024": "->zenpad 8.0",
    "p027": "->zenpad 3s 10",
    "pad tf300t": "->transformer pad tf300t",
    "padfone 2": "DS=4.7;RS=720x1280;SZ=68.9x137.9x9;WT=135;RE=2012.10;RM=2048;CP=134;OI=1;OV=4.4;",
    "padfone infinity": "DS=5;RS=1080x1920;SZ=72.8x143.5x8.9;WT=145;RE=2013.02;RM=2048;CP=138;OI=1;OV=4.4;",
    "padfone mini": "DS=4;RS=480x800;SZ=119.43x199.85x13.91;WT=260;RE=2014.01;RM=1024;CP=18;OI=1;OV=4.3;",
    "padfone s": "DS=5;RS=1080x1920;SZ=72.5x143.4x10;WT=150;RE=2014.03;CP=139;RM=2048;OI=1;OV=4.4.2;",
    "padfone t004": "",
    "padfone x mini": "DS=4.5;RS=854x480;SZ=66.3x132.1x12.7;WT=148.8;RE=2014.10;CP=18;RM=1024;",
    "padfone": "DS=4.3;RS=540x960;SZ=65.4x128x9.2;WT=129;RE=2011.06;RM=1024;CP=140;OI=1;OV=4.0.3;",
    "rog phone 2": "DS=6.59;RS=1080x2340;SZ=77.6x171x9.5;WT=240;RM=8192;CP=197;RE=2019.07;OI=1;OV=9;SM=2;",
    "i001d": "->rog phone 2",
    "i001da": "->rog phone 2",
    "i001db": "->rog phone 2",
    "i001dc": "->rog phone 2",
    "i001de": "->rog phone 2",
    "rog phone": "DS=6.0;RS=1080x2160;SZ=76.2x158.8x8.6;WT=200;RE=2018.06;RM=8192;CP=198;OI=1;OV=9;SM=2;",
    "rog phone 3": "DS=6.59;RS=1080x2340;SZ=78x171x9.85;WT=240;RE=2020.07;RM=8192;CP=141;OI=1;OV=10;",
    "i003d": "->rog phone 3",
    "i003dd": "->rog phone 3",
    "i005dc": "->rog phone 5",
    "i005da": "->rog phone 5",
    "zs673ks": "->rog phone 5",
    "rog phone 5": "RE=2021.05;SZ=77.3x172.8x10.3;WT=238;DS=6.78;RS=1080x2448;OI=1;OV=11;CP=305;RM=8192;",
    "zenfone 8": "RE=2021.05;SZ=68.5x148x8.9;WT=169;DS=5.9;RS=1080x2400;OI=1;OV=11;CP=305;RM=6144;",
    "zs590ks": "->zenfone 8",
    "i006d": "->zenfone 8",
    "slider sl101": "->eee pad slider sl101",
    "t00e": "->padfone mini",
    "t00i": "->zenfone 4",
    "t00n": "->padfone s",
    "t00q": "->zenfone 4",
    "t00t": "->padfone x mini",
    "tab a8": "",
    "tablet p1801-t": "->transformer aio p1801 18.4\"",
    "tf300t": "->transformer pad tf300t",
    "transformer aio p1801 18.4\"": "",
    "transformer book": "",
    "transformer mini": "",
    "transformer pad tf103c": "DS=10.1;RS=1280x800;SZ=257.4x178.4x19.8;WT=1100;RE=;RM=1024;CP=135;OI=1;OV=4.4;",
    "k010": "->transformer pad tf103c",
    "transformer pad tf103cg": "",
    "transformer pad tf300t": "",
    "transformer pad tf300tg": "",
    "transformer pad tf300tl": "",
    "transformer pad tf303k": "",
    "transformer pad tf700kl": "",
    "transformer pad tf700t": "",
    "transformer pad tf701t": "",
    "transformer prime tf201": "",
    "transformer tf101": "",
    "transformer tf101g": "",
    "x pad 10 lte": "",
    "x00is": "->zenfone 4 max",
    "x550lb": "",
    "x553ma": "",
    "x555ln": "",
    "x556uqk": "",
    "z008": "->zenfone 2",
    "z008d": "->zenfone 2",
    "z00a": "->zenfone 2",
    "z00ad": "->zenfone 2",
    "z00ada": "->zenfone 2",
    "z00adb": "->zenfone 2",
    "z00d": "->zenfone 2",
    "z00ed": "->zenfone 2 laser",
    "z00md": "->zenfone 2 laser",
    "z00rd": "->zenfone 2 laser",
    "z00td": "->zenfone 2 laser",
    "z00wd": "->zenfone 2 laser",
    "z011d": "->zenfone 2 laser",
    "z016d": "->zenfone 3 deluxe",
    "z01fd": "->zenfone 3 deluxe",
    "z01qd": "->rog phone",
    "z101": "",
    "z906 10.1\"": "",
    "z906": "->z906 10.1\""
  },
  "at&t": {
    "axia": "DS=5.5;RS=480x854;SZ=78.5x154.9x8.9;WT=178;RE=2019.01;OI=1;OV=8.1.0;RM=1024;CP=10;",
    "qs5509a": "->axia",
    "radiant core": "",
    "u304aa": "->radiant core"
  },
  "atvio": {
    "55d1620": ""
  },
  "atom": {
    "108am": "",
    "216am": "",
    "216rk": "",
    "atom-108am": "->108am",
    "atom-216am": "->216am",
    "atom-216rk": "->216rk"
  },
  "audiovox": null,
  "avenzo": {
    "av115": "->mob 4 4g",
    "mob 4 4g": "DS=5.0;RS=720x1280;WT=148;SZ=71.5x145x8.3;OI=1;OV=6.0;RM=1024;CP=118;",
    "mob 4 pro 4g": "",
    "mob4 4g": "->mob 4 4g",
    "mob4pro 4g": "->mob 4 pro 4g"
  },
  "avh": {
    "excer 10 pro": "DS=10;RS=1280x800;OI=1;OV=7.0;RM=1024;RS=2018.09;WT=320;",
    "excer 8": "",
    "excer g5.3": "",
    "excer g5": ""
  },
  "avvio": {
    "765": "RE=2013.12;WT=110;DS=3.5;RS=320x480;SZ=63x113x13.3;RM=512;OI=1;OV=4.2;",
    "774": "DS=4;RS=480x800;SZ=65.6x124.55x9.6;WT=180;RE=2016;OI=1;OV=4.4.2;RM=512;CP=36;",
    "775": "",
    "786": "",
    "765 765": "->765",
    "chivas 55": "",
    "colombia (2018)": "DS=5.0;RS=720x1280;SZ=73.1x144x9.5;WT=150;RM=1024;CP=5;OI=1;OV=7;",
    "colombia 2018": "->colombia (2018)",
    "mint m353": "",
    "pad": ""
  },
  "axxion": {
    "atab-701": "",
    "atab-902": ""
  },
  "azumi mobile": {
    "a35c lite": "",
    "a35clite": "",
    "a40c": "",
    "a50c+": "",
    "a50tq": "",
    "iro a4 q": "",
    "speed 5.5\"": "",
    "speed55": "->speed 5.5\""
  },
  "bellphone": {
    "bp 100 x-plus": "DS=5.7;RM=1024;OI=1;OV=8;SM=2;",
    "bp100 x-plus": "->bp 100 x-plus"
  },
  "benq": {
    "cf61": "",
    "f52 09": "->f52",
    "f52": ""
  },
  "bitel": {
    "b8407": "RE=2014.11;DS=4;RS=480x800;SZ=63.6x125x10.3;WT=;CP=36;RM=512;SM=2;OI=1;OV=4.4;",
    "b8408": "RE=2015.06;DS=4;RS=480x800;SZ=;WT=;CP=36;RM=512;SM=1;OI=1;OV=4.4;",
    "b8409": "RE=2015.09;DS=4;RS=480x800;SZ=;WT=;CP=24;RM=512;SM=1;OI=1;OV=4.4;",
    "b8410": "",
    "b8411": "",
    "b8413": "",
    "b8414": "",
    "b8415": "",
    "b8416": "",
    "b8502": "",
    "b8503": "",
    "b8504": "",
    "b8506": "",
    "b8601": "",
    "b8604": "",
    "b8606": "",
    "b9401": "",
    "b9501": "",
    "b9502": "",
    "b9503": "",
    "b9504": "",
    "b9505": "DS=5;RS=480x854;SZ=72x140.1x8.7;WT=;RE=2016.08;OI=1;OV=7;RM=1024;CP=86;SM=2;",
    "bitel-b8413": "->b8413",
    "s8402": "",
    "s8402l": "",
    "s8501": "",
    "s8501l": ""
  },
  "bitmore": {
    "mobitab 10c 3g": "DS=10.1;RS=1024x720;SZ=241.6x170.5x10.6;WT=500;RE=2016.09;CP=163;RM=1024;OI=1;OV=5.1;",
    "mobitab10c-3g": "->mobitab 10c 3g",
    "tab1011q ii": "DS=10.1;RS=1024x600;SZ=;WT=509;RE=2015.06;CP=163;RM=1024;OI=1;OV=4.4.2;"
  },
  "bkav": {
    "bphone b1114": "",
    "bphone b1115": "DS=5;RS=1080x1920;RE=2015.07;RM=3072",
    "bphone b2017": ""
  },
  "black bear": {
    "b6 master": "DS=5;RS=480x960;SZ=66.2x137.7x9.6;WT=144;CP=109;RM=1024;RE=2018.12;OI=1;OV=8.1;SM=2;",
    "b6 note x": "DS=5.5;RS=720x1280;SZ=70.6x147.9x9.4;WT=147.9;CP=109;RM=2048;RE=2018.11;OI=1;OV=8.1;SM=2;"
  },
  "uz mobile": {
    "uzbekistan": "DS=5.9;RS=720x1520;RM=4096;OI=1;OV=9;CP=40;"
  },
  "kurio": {
    "phone c14500": "OI=1;OV=4.2;RM=1024;DS=4.0;RS=480x800;SZ=63.5x121x10.5;WT=400;SM=1;RE=2015.04;CP=210;",
    "kuriophone": "->phone c14500",
    "10s": "OI=1;OV=4.2;RM=1024;DS=10.1;RS=1280x800;WT=725;SZ=275.1x183.9x13;CP=316;",
    "kurio10s": "->10s",
    "7s": "DS=7;RS=1024x600;WT=400;SZ=200x127x12;CP=317;OI=1;OV=4.2;",
    "kurio7s": "->7s"
  },
  "blackview": {
    "a60 pro": "DS=6.1;RS=600x1280;SZ=74x156.8x9.8;WT=170;RE=2019.05;OI=1;OV=9.0;RM=3072;CP=44;",
    "a60": "DS=6.1;SZ=74x156.8x9.8;WT=171;RS=600x1280;RE=2019.05;CP=4;RM=1024;OI=1;OV=8.1;SM=2;",
    "a60pro": "->a60 pro",
    "a7": "DS=5;RS=720x1280;SZ=71x143x9.5;WT=176;RE=2017;OI=1;OV=7.0;RM=1024;CP=4;",
    "a80 pro": "DS=6.49;RS=720x1560;SZ=77x162.75x8.8;WT=180;RE=2019;OI=1;OV=9.0;RM=4096;CP=232",
    "a80pro": "->a80 pro",
    "a8": "",
    "alife p1": "",
    "alife s1": "",
    "bv4000 pro": "DS=4.7;RS=720x1280;SZ=74.2x143.9x13.25;WT=205;RE=2017;OI=1;OV=7.0;RM=2048;CP=4;",
    "bv4000pro": "->bv4000 pro",
    "bv4900": "DS=5.7;RS=720x1440;SZ=78.1x157.5x13.7;WT=261;RE=2020;OI=1;OV=10;RM=3072;CP=44;",
    "bv5500": "DS=5.5;RS=720x1440;SZ=75.5x152.2x14;WT=225;RE=2019;OI=1;OV=8.1;RM=2048;CP=5;",
    "bv5800 pro": "DS=5.5;RS=720x1440;SZ=78.5x156.9x15.5;WT=253;RE=2018;OI=1;OV=8.1;RM=2048;CP=8;",
    "bv5900": "DS=5.7;RS=720x1520;SZ=80x159x15;WT=268;RE=2019;OI=1;OV=9.0;RM=3072;CP=44;",
    "bv6000": "DS=4.7;RS=720x1280;SZ=81x152.3x16.6;WT=247;RE=2016;OI=1;OV=6.0;RM=3072;CP=117;",
    "bv6000s": "DS=4.7;RS=720x1280;SZ=81x152.3x16.6;WT=247;RE=2016;OI=1;OV=6.0;RM=2048;CP=79;",
    "bv6100": "DS=6.88;RS=640x1352;SZ=89.7x181x11.5;WT=315;RE=2019;OI=1;OV=9.0;RM=3072;CP=44;",
    "bv6300 pro": "DS=5.7;RS=720x1440;SZ=78.2x159.6x12.8;WT=230;RE=2020;OI=1;OV=10;RM=6144;CP=114;",
    "bv6300": "DS=5.7;RS=720x1440;SZ=78.2x159.6x12.8;WT=230;RE=2020;OI=1;OV=10;RM=3072;CP=171;",
    "bv6300pro": "->bv6300 pro",
    "bv6800 pro": "DS=5.7;RS=1080x2160;SZ=81x162.2x14.8;WT=275;RE=2018;OI=1;OV=8.0;RM=4096;CP=162;",
    "bv6800pro": "->bv6800 pro",
    "bv6900": "",
    "bv7000 pro": "",
    "bv8000 pro": "",
    "bv8000pro": "->bv8000 pro",
    "bv9000 pro f": "",
    "bv9000pro-f": "->bv9000 pro f",
    "bv9100": "",
    "bv9500": "",
    "bv9600 pro": "",
    "bv9600": "",
    "bv9600pro": "->bv9600 pro",
    "bv9700 pro": "",
    "bv9700pro": "->bv9700 pro",
    "bv9800": "",
    "bv9900": "",
    "dm550": "",
    "e7s": "",
    "omega pro": "",
    "p10000 pro": "",
    "bv6600": "RE=2021.03;SZ=79.4x159x18;WT=325;DS=5.7;RS=720x1440;OI=1;OV=10;CP=355;RM=4096;"
  },
  "bravis": {
    "a501 bright": "",
    "a506": "->crystal",
    "a510 je4g": "->a510 jeans 4g",
    "a553": "->discovery",
    "biz": "",
    "n1 550_cruiser": "->n1-550 cruiser",
    "n1-570 space": "->space",
    "nb105": "",
    "nb106m": "",
    "nb107": "",
    "nb108": "",
    "nb74": "",
    "nb751": "",
    "nb76": "",
    "nb851": "DS=8;RS=1280x800;SZ=206.5x122.8x7.7;WT=332;RE=2018;OI=1;OV=8.1;RM=1024;CP=163;",
    "nb871": "DS=8;RS=1280x800;SZ=206.5x122.8x7.7;WT=332;RE=2018;OI=1;OV=8.1;RM=1024;CP=74;",
    "nb961": "DS=9.6;RS=1280x800;SZ=226x160x9.5;WT=490;RE=2017;OI=1;OV=6.0;RM=2048;CP=163;SM=2;",
    "neo": "->a401 neo",
    "np 103": "->np103",
    "np 104 3g": "->np104 3g",
    "np 844": "->np844",
    "np101": "",
    "np103": "",
    "np104 3g": "",
    "np844": "",
    "power": "",
    "slim 3g": "",
    "spark": "",
    "x500": "->trace pro",
    "delta": "DS=5;RS=540x960;SZ=69x138x8.9;WT=125;RE=2015;RM=512;CP=7;SM=2;OI=1;OV=5.1;",
    "crystal": "DS=5;RS=720x1280;SZ=71.6x141.9x8.6;WT=160;RE=2018;RM=1024;CP=5;SM=2;",
    "discovery": "DS=5.5;RS=720x1280;SZ=77x153.8x7.9;WT=177;RE=2017;RM=1024;CP=5;OI=1;OV=5.1;SM=2;",
    "tau": "DS=5;RS=720x1280;SZ=70.8x142.5x6.5;WT=169;RE=2014;RM=1024;CP=7;OI=1;OV=4.2;SM=2;",
    "trace pro": "DS=5;RS=720x1280;SZ=71.3x141.7x9.65;WT=182;RE=2017;RM=2048;CP=5;OI=1;OV=6;SM=2;",
    "trend": "DS=5;RS=720x1280;SZ=71.5x145x8.3;WT=146;RE=2015;RM=1024;CP=6;OI=1;OV=5.1;SM=2;",
    "b501 easy": "->easy",
    "b501": "->easy",
    "easy": "DS=5;RS=480x854;SZ=72.80x144.80x9.80;WT=146;RE=2016;RM=512;CP=6;OI=1;OV=5.1;SM=2;",
    "n1-570": "->space",
    "space": "DS=5.72;RS=720x1440;SZ=73x152x9.2;WT=174;RE=2018;RM=2048;CP=8;OI=1;OV=8.1;SM=2;",
    "a554": "->a554 grand",
    "a554 grand": "DS=5.5;RS=720x1280;SZ=78x156x10.5;WT=198;RE=2017;RM=2048;CP=4;OI=1;OV=7;SM=2;",
    "s500": "->s500 diamond",
    "s500 diamond": "DS=5;RS=720x1280;SZ=68.3x140x8.5;WT=164;RE=2017;RM=2048;CP=4;OI=1;OV=6;",
    "a505 joy plus": "DS=5;RS=720x1280;SZ=70x142x8.2;WT=152;RE=2017;RM=1024;CP=5;OI=1;OV=6;SM=2;",
    "a552 joy max": "DS=5.5;RS=720x1280;SZ=77x153x8.3;WT=184;RE=2017;RM=1024;CP=5;OI=1;OV=6;SM=2;",
    "atlas a551": "->a551 atlas",
    "a551 atlas": "DS=5.5;RS=720x1280;SZ=78.3x157.5x8.8;WT=206;RE=2016;RM=1024;CP=5;OI=1;OV=5.1;SM=2;",
    "a503 joy": "DS=5;RS=720x1280;SZ=71x141x8.8;WT=155;RE=2016;RM=1024;CP=5;OI=1;OV=6;SM=2;",
    "a504 trace": "DS=5;RS=720x1280;SZ=71.3x141x8.7;WT=178;RE=2016;RM=1024;CP=5;OI=1;OV=6;SM=2;",
    "a512 harmony pro": "DS=5;RS=720x1280;SZ=72x146x10;WT=143;RE=2018.12;RM=2048;CP=4;OI=1;OV=6;SM=2;",
    "a510 jeans 4g": "DS=5;RS=720x1280;SZ=70x142x8.5;WT=155;RE=2018;RM=1024;CP=9;OI=1;OV=8.1;SM=2;",
    "a509 jeans": "DS=5;RS=720x1280;SZ=70x142x8.5;WT=155;RE=2018;RM=1024;CP=5;OI=1;OV=8.1;SM=2;",
    "a401 neo": "DS=4;RS=480x800;SZ=63x122.5x9.5;WT=103;RE=2016;RM=512;CP=5;OI=1;OV=5.1;",
    "n1 550 cruiser": "->n1-550 cruiser",
    "n1-550 cruiser": "DS=5.5;RS=640x1280;SZ=70x146.5x9.5;WT=212;RE=2018.11;RM=2048;CP=4;OI=1;OV=8.1;",
    "omega": "DS=5;RS=720x1280;SZ=76.6x145.3x8.4;WT=146;RE=2015;RM=1024;CP=1;OI=1;OV=4.4;"
  },
  "bb mobile": {
    "techno 10.0\" lte tq060x": "DS=10.1;RS=1024x600;SZ=243x165.8x10.5;WT=550;RM=1024;CP=74;OI=1;OV=5.1;SM=2;",
    "techno 10.1 lte tq060x": "->techno 10.0\" lte tq060x",
    "techno 10.1\" mozg i101bi": "DS=10.1;RS=1024x600;SZ=243x166x10;WT=535;OI=1;OV=5.1;RM=1024;CP=211;SM=2;",
    "techno 7.0 lte tq763i": "->techno 7.0\" kalash lte tq763i",
    "techno 7.0\" kalash lte tq763i": "",
    "techno 7.0\" mozg lte i700aj": "",
    "techno 7.0\" pioneer lte s700bf": "",
    "techno 7.85\" mozg i785ap": "",
    "techno 8.0 3g": "->techno 8.0\" 3g",
    "techno 8.0\" 3g": "",
    "techno 8.0\" poplar lte tq863q": "",
    "techno 9.7 3g": "->techno 9.7\" 3g",
    "techno 9.7\" 3g": "",
    "techno i101bi": "->techno 10.1\" mozg i101bi",
    "techno i700aj": "->techno 7.0\" mozg lte i700aj",
    "techno s700bf": "->techno 7.0\" pioneer lte s700bf",
    "techno spark 3g x595bt": "",
    "techno tq863q": "->techno 8.0\" poplar lte tq863q",
    "techno x595bt": "->techno spark 3g x595bt",
    "techno-i785ap": "->techno 7.85\" mozg i785ap"
  },
  "beyond": {
    "surve 1 pro": "",
    "surve 10": "",
    "surve 2 pro": "",
    "surve 2": "",
    "surve 6": "",
    "surve 7": "",
    "surve 8": "",
    "surve1 pro": "->surve 1 pro",
    "surve10": "->surve 10",
    "surve2 pro": "->surve 2 pro",
    "surve2": "->surve 2",
    "surve6": "->surve 6",
    "surve7": "->surve 7",
    "surve8": "->surve 8"
  },
  "bangolufsen": {
    "beovision": ""
  },
  "barnes & noble": {
    "bntv400": "->nook bntv400",
    "bntv600": "->nook bntv600",
    "nobntv250": "->nook bntv250",
    "nobntv250a": "->nook bntv250a",
    "nook bnrv200": "DS=7.0;RS=1024x600;",
    "nook bntv250": "",
    "nook bntv250a": "",
    "nook bntv400": "",
    "nook bntv600": "",
    "nook color": "",
    "nook tablet": "",
    "nookcolor": "->nook color",
    "notablet": "->nook tablet"
  },
  "beeline": {
    "e700": "SM=2;OI=1;OV=4.1;WT=120;SZ=61.7x116x12.6;RS=480x800;DS=4;",
    "fast 2": "SM=1;OI=1;OV=5.1;WT=160;SZ=71.5x143.7x9.4;RS=480x854;DS=5;RM=1024;CP=56;RE=2015.12;",
    "fast": "SM=1;OI=1;OV=5.1;WT=125;SZ=72.6x145.5x8.8;RS=480x854;DS=5;RM=1024;CP=56;RE=2015.09;",
    "pro 2": "SM=1;OI=1;OV=5.0;WT=154;SZ=67.4x134.5x10.3;RS=480x854;DS=4.5;RM=1024;CP=1;RE=2015.12;",
    "pro 3": "SM=1;OI=1;OV=5.1;WSZ=72.6x144x8.7;WT=152;DS=5;RS=480x854;RM=1024;CP=6;",
    "pro 4": "SM=2;RE=2015;WT=130;OI=1;OV=5.1;SZ=66.0x136.0x9.3;DS=4.5;RS=480x854;RM=1024;CP=1;",
    "pro 6": "RE=2017.04;SZ=67x133x9.8;RM=1024;WT=130;DS=4.5;RS=480x854;CP=86;OI=1;OV=7.0;",
    "smart 2": "SM=1;OI=1;OV=4.4;WT=124;SZ=64.7x126x10.7;DS=4;RS=480x800;CP=119;RM=512;",
    "smart 3": "SM=1;OI=1;OV=4.4;WT=122;SZ=64x125.5x9.7;DS=4;RS=480x800;CP=210;RM=512;",
    "smart 4": "SM=1;OI=1;OV=5.0;WT=237;SZ=66x136x9.3;DS=4.5;RS=480x854;CP=1;RM=1024;",
    "smart dual": "SM=2;OI=1;OV=4.4.2;RE=2015.07;CP=24;RM=512;DS=4;RS=480x800;",
    "smart2": "->smart 2",
    "tab 2": "",
    "tab fast": "",
    "tab pro": "",
    "tab": ""
  },
  "bird": {
    "doeasy e700": "DS=4.5;RS=480x854;RE=2014.03;SM=2;RM=512;",
    "i600": "",
    "i7": "",
    "t900": "",
    "t9108": "",
    "v8": "",
    "w5": ""
  },
  "billion": {
    "capture plus": "DS=5.5;RS=1080x1920;SZ=76.5x153x8.5;WT=176;RM=3072;CP=81;RE=2017.09;OI=1;OV=7.1;SM=2;",
    "capture+": "->capture plus"
  },
  "bezkam": {
    "bk-ram2": "DS=5.5;RS=720x1280;SZ=72.2x146.4x9.2;WT=170;RM=2048;CP=79;RE=2017.07;SM=2;OI=1;OV=5.1;"
  },
  "bigben": {
    "gametab-one": "DS=7;RS=1024x600;SZ=187x110x12;WT=346;CP=80;RM=2048;OI=1;OV=4.2;"
  },
  "bihee": {
    "a11": "WT=174.4;SZ=72x144.2x9.2;RS=480x854;DS=5;CP=10;RM=2048;RE=2017.09;OI=1;OV=7.1.2;",
    "a12": "DS=5.5;RS=720x1280;SZ=70x148x8.2;WT=142.5;RE=2017.09;OI=1;OV=6;RM=2048;CP=9;",
    "a5": "",
    "a6": "",
    "a7+": "",
    "a8+": "",
    "zbh-a5": "->a5"
  },
  "black fox": {
    "b3+": "WT=175;DS=5;RS=720x1280;SZ=71x143x9.5;RM=2048;CP=9;OI=1;OV=7;",
    "b3": "DS=5;RS=720x1280;SZ=71x143x9.5;WT=175;RE=2017;OI=1;OV=7.0;RM=1024;CP=4;",
    "bmm531b": "->b4 mini nfc;",
    "b4 mini (2019)": "->b4 mini nfc;",
    "b4 mini nfc": "DS=4.95;RS=480x960;SZ=65.7x138x9.6;WT=136;RE=2019;OI=1;OV=8.1;RM=1024;CP=8;",
    "b4 mini": "DS=4.95;RS=480x960;SZ=65.7x138x9.6;WT=136;RE=2019;OI=1;OV=8.1;RM=1024;CP=5;",
    "b4": "DS=4.95;RS=480x960;SZ=65.7x138x9.6;WT=136;RE=2018;OI=1;OV=8.1;RM=2048;CP=8;",
    "b5+": "DS=5.45;RS=720x1440;SZ=70.7x146.7x9.6;WT=170;RE=2018;OI=1;OV=8.1;RM=2048;CP=8;",
    "b5": "DS=5.45;RS=480x960;SZ=70.7x146.7x9.6;WT=170;RE=2018;OI=1;OV=8.1;RM=1024;CP=58;",
    "b6": "DS=5.45;RS=480x960;SZ=72x149x9;WT=170;RE=2019;OI=1;OV=8.1;RM=1024;CP=58;",
    "b7 fox+": "DS=5.45;RS=720x1440;SZ=70x149x8;WT=163;RE=2019;OI=1;OV=8.1;RM=2048;CP=8;",
    "b7": "DS=5.45;RS=720x1440;SZ=70x149x8;WT=163;RE=2019;OI=1;OV=8.1;RM=1024;CP=8;",
    "b7r fox": "DS=5.5;RS=720x1440;SZ=71.4x147.7x8.5;WT=;RE=2020;OI=1;OV=9.0;RM=2048;CP=84;",
    "b8 fox+": "DS=5.5;RS=720x1280;SZ=76.8x153.5x8.75;WT=150;RE=2017;OI=1;OV=6.0;RM=1024;CP=9;",
    "b8 fox": "DS=5;RS=720x1280;SZ=72.6x145x8.3;WT=153;RE=2017;OI=1;OV=5.0;RM=1024;CP=56;",
    "bmm 431d": "DS=4.5;RS=540x960;SZ=66x129.9x9.5;WT=;RE=2016;OI=1;OV=6.0;RM=1024;CP=5;",
    "bmm 532 s": "->bmm 532s",
    "bmm 532s": "DS=5;RS=720x1280;SZ=73.9x144.9x9.3;WT=160;RE=2016;OI=1;OV=5.1;RM=1024;CP=4;",
    "bmm 533d": "DS=5;RS=720x1280;SM=2;OI=1;OV=5.1;SZ=73.9x144.9x9.3;WT=126;RM=1024;CP=5;",
    "bmm 541s": "DS=5;RS=720x1280;SZ=72.6x145x8.3;WT=153;RE=2017;OI=1;OV=5.0;RM=1024;CP=56;",
    "bmm 542s": "DS=5.5;RS=720x1280;SZ=73.9x144.9x9.3;WT=126;OI=1;OV=5.1;SM=2;CP=5;RM=1024;RE=2017;",
    "bmm441s": "->b8 fox",
    "bmm442s": "->b8 fox+",
    "bmm443d": "->b7 fox+",
    "bmm531a": "->b5",
    "bmm541d": "->b3",
    "bmm541w": "->b7r fox",
    "bmm542d": "->b3+",
    "bmm543d": "->b4",
    "bmm543s": "->b4"
  },
  "öwn": {
    "9": "->smart 9",
    "fun 5(4g)": "->fun 5 4g",
    "fun 5 4g": "DS=5;RS=480x854;WT=130;SZ=72x146x9;RM=1024;CP=86;OI=1;OV=7;",
    "one": "",
    "smart 9": "",
    "smart 8": "",
    "fun 6": "",
    "fun 7": "",
    "one plus": "",
    "s3000d": "",
    "s3010": "",
    "s3020d": "",
    "s4010": "",
    "s4025": "",
    "s4035 3g": "",
    "s4035 4g": ""
  },
  "colors": {
    "pride 5c": "DS=5;RS=480x960;RM=1024;SM=2;OI=1;OV=8;RE=2018.08;",
    "p52 pride5c": "->pride 5c",
    "pride 5x": "DS=5.5;",
    "p90 pride5x": "->pride 5x",
    "s9": "DS=5.2;RS=720x1280;SM=2;OI=1;OV=7.0;CP=9;RM=2048;",
    "s11": "DS=5.2;RS=720x1280;SZ=72.9x148.4x7.85;SM=2;OI=1;OV=7.0;CP=9;RM=3072;",
    "s1": "DS=5.5;RS=720x1280;OI=1;OV=7.0;CP=4;RM=1024;SM=2;",
    "p70": "DS=5;RS=720x1280;OI=1;OV=7.0;RM=2048;CP=9;",
    "p65": "",
    "p50 plus": "",
    "p45": "",
    "pride 1a": "DS=4;WT=183;RM=512;",
    "e15": "",
    "p55": "",
    "p32": ""
  },
  "huawei": {
    "y6 pro": "RE=2015.10;SZ=71.8x143.1x9.7;WT=160;DS=5.0;RS=720x1280;OI=1;OV=5.1.1;CP=56;RM=2048;",
    "tit-u02": "->y6 pro",
    "y5 pro (2017)": "RE=2017.04;SZ=72x143.8x8.4;WT=150;DS=5.0;RS=720x1280;OI=1;OV=6.0;CP=69;RM=2048;",
    "mya-l13": "->y5 pro (2017)",
    "4afrika": "DS=4;RS=480x800;SZ=63.7x124.5x10.5;WT=130;RE=2013.02;RM=512;CP=212;OI=4;OV=8;",
    "ale-l21": "->p8 lite (2015)",
    "ale-tl00": "->p8 lite (2015)",
    "ale-ul00": "->p8 lite (2015)",
    "503hw": "->p8 lite (2015)",
    "ale-l02": "->p8 lite (2015)",
    "ale-l23": "->p8 lite (2015)",
    "p8 lite (2015)": "DS=5;RS=720x1280;SZ=70.6x143x7.7;WT=131;RE=2015.04;OI=1;OV=5.0.2;RM=2048;CP=3;SM=2;",
    "608hw": "->nova lite",
    "nova lite": "DS=5.2;RS=1080x1920;SZ=72.94x147.2x7.6;WT=147;RE=2017.10;OI=1;OV=7.0;RM=3072;CP=48;SM=2;",
    "701hw": "->mediapad m3 lite",
    "bach-l09": "->mediapad m3 lite;DS=10.1;RE=2017.05;SZ=241.3x171.5x7.1;",
    "bach-w09": "->mediapad m3 lite;DS=10.1;RE=2017.05;SZ=241.3x171.5x7.1;",
    "bah-w09": "->mediapad m3 lite;DS=10.1;RE=2017.05;SZ=241.3x171.5x7.1;",
    "cpn-al00": "->mediapad m3 lite",
    "cpn-l09": "->mediapad m3 lite",
    "cpn-w09": "->mediapad m3 lite",
    "mediapad m3 lite": "RE=2017.06;SZ=213.3x123.3x7.5;WT=310;DS=8.0;RS=1920x1200;OI=1;OV=7.0;CP=35;RM=4096;",
    "pot-tl00a": "->9s",
    "pot-al00a": "->9s",
    "enjoy 9s": "->9s",
    "9s": "DS=6.21;RS=1080x2340;SZ=73.4x155.2x8;WT=160;RE=2019.03;OI=1;OV=9;SM=2;RM=4096;CP=28;",
    "704hw": "->nova 2 lite",
    "nova 2 lite": "DS=5.99;RS=720x1440;SZ=76.7x158.3x7.8;WT=155;RE=2018.03;OI=1;OV=8.0;RM=3072;CP=43;",
    "ags-l03": "->mediapad t3 10",
    "ags-l09": "->mediapad t3 10",
    "ags-w09": "->mediapad t3 10",
    "mediapad t3 10": "RE=2017.05;SZ=229.8x159.8x8;WT=460;DS=9.6;RS=1280x800;OI=1;OV=7.0;CP=20;RM=2048;",
    "ags2-al00": "->enjoy tablet 10.1",
    "enjoy tablet 10.1": "DS=10.1;RS=1920x1200;WT=460;SZ=243x164x8;RM=4096;CP=42;OI=1;OV=8.0;SM=1;RE=2018.11;",
    "ags2-al00hn": "->honor tab 5",
    "jdn2-w09hn": "->honor tab 5",
    "honor tab 5": "RE=2019.05;SZ=204.2x122.2x8.2;WT=310;DS=8.0;RS=1920x1200;OI=1;OV=9.0;CP=28;RM=3072;",
    "ags2-l03": "->mediapad t5 10",
    "ags2-l09": "->mediapad t5 10",
    "ags2-w09": "->mediapad t5 10",
    "ags2-w19": "->mediapad t5 10",
    "mediapad t5 10": "RE=2018.08;SZ=243x164x7.8;WT=460;DS=10.1;RS=1920x1200;OI=1;OV=8.0;CP=42;RM=2048;",
    "ags3-l09": "->matepad t10s",
    "ags3-w09": "->matepad t10s",
    "matepad t 10s": "->matepad t10s",
    "matepad t10s": "RE=2020.09;SZ=240.2x159x7.9;WT=450;DS=10.1;RS=1920x1200;OI=1;OV=10;CP=213;RM=2048;",
    "aka-al10": "->honor play 4t",
    "aka-tl10": "->honor play 4t",
    "honor play 4t": "RE=2020.04;SZ=76.1x159.8x8.1;WT=176;DS=6.39;RS=720x1560;OI=1;OV=10;CP=213;RM=6144;",
    "aka-l29": "->honor 9c",
    "honor 9c": "RE=2020.04;SZ=76.1x159.8x8.1;WT=176;DS=6.39;RS=720x1560;OI=1;OV=10;CP=213;RM=4096;",
    "alp-al00": "->mate 10",
    "alp-tl00": "->mate 10",
    "alp-l29": "->mate 10",
    "alp-l09": "->mate 10",
    "mate 10": "RE=2017.10;SZ=77.8x150.5x8.2;WT=186;DS=5.9;RS=1440x2560;OI=1;OV=8.0;CP=50;RM=4096;",
    "amn-lx1": "->y5 (2019)",
    "amn-lx2": "->y5 (2019)",
    "amn-lx3": "->y5 (2019)",
    "amn-lx9": "->y5 (2019)",
    "y5 (2019)": "RE=2019.04;SZ=70.8x147.1x8.5;WT=146;DS=5.71;RS=720x1520;OI=1;OV=9.0;CP=44;RM=2048;",
    "ana-an00": "->honor p40",
    "ana-nx9": "->honor p40",
    "ana-tn00": "->honor p40",
    "ana-lx4": "->p40",
    "honor p40": "->p40",
    "p40": "RE=2020.03;SZ=71.1x148.9x8.5;WT=175;DS=6.1;RS=1080x2340;OI=1;OV=10;CP=54;RM=6144;",
    "ane-al00": "->nova 3e",
    "nova 3e": "WT=145;SZ=71.2x148.6x7.4;DS=5.84;RS=1080x2280;RM=4096;CP=42;RE=2018.03;SM=2;OI=1;OV=8.0;",
    "ane-lx1": "->p20 lite",
    "ane-lx2j": "->p20 lite",
    "ane-lx2": "->p20 lite",
    "ane-lx3": "->p20 lite",
    "ane-tl00": "->p20 lite",
    "hwv32": "->p20 lite",
    "p20 lite": "RE=2018.03;SZ=71.2x148.6x7.4;WT=145;DS=5.84;RS=1080x2280;OI=1;OV=8.0;CP=42;RM=4096;SM=2;",
    "aqm-al00": "->enjoy 10s",
    "aqm-tl00": "->enjoy 10s",
    "enjoy 10s": "DS=6.3;RS=1080x2400;SZ=73.2x157.4x7.8;WT=163;RM=6144;CP=37;RE=2019.09;OI=1;OV=9;",
    "aqm-al10": "->honor 4t pro",
    "aqm-tl10": "->honor 4t pro",
    "honor 4t pro": "DS=6.3;RS=1080x2400;SZ=73.2x157.4x7.8;WT=165;RE=2020.04;RM=6144;CP=57;OI=1;OV=10;SM=2;",
    "are-al00": "->honor 8x max",
    "are-al10": "->honor 8x max",
    "are-l22": "->honor 8x max",
    "are-l22hn": "->honor 8x max",
    "are-tl00": "->honor 8x max",
    "honor 8x max": "RE=2018.09;SZ=86.3x177.6x8.1;WT=210;DS=7.12;RS=1080x2244;OI=1;OV=8.1;CP=61;RM=4096;",
    "aqm-lx1": "->y8p",
    "y8p": "RE=2020.05;SZ=73.2x157.4x7.8;WT=163;DS=6.3;RS=1080x2400;OI=1;OV=10;CP=37;RM=4096;",
    "ars-al00": "->y max",
    "ars-l22": "->y max",
    "ars-tl00": "->y max",
    "y max": "RE=2018.11;SZ=86.2x177.6x8.5;WT=210;DS=7.12;RS=1080x2244;OI=1;OV=8.1;CP=61;RM=4096;",
    "art-al00m": "->enjoy 10",
    "art-al00x": "->enjoy 10",
    "art-tl00x": "->enjoy 10",
    "art-tl00m": "->enjoy 10",
    "enjoy 10": "DS=6.39;RS=720x1560;SZ=76.1x159.8x8.1;WT=176;RM=4096;CP=37;RE=2019.11;OI=1;OV=9;SM=2;",
    "art-l28": "->y7p",
    "y7p": "RE=2020.02;SZ=76.1x159.8x8.1;WT=176;DS=6.39;RS=720x1560;OI=1;OV=9;CP=37;RM=4096;",
    "art-l29": "->p40 lite e",
    "art-l29n": "->p40 lite e nfc",
    "p40 lite e nfc": "RE=2020.03;SZ=76.1x159.8x8.1;WT=176;DS=6.39;RS=720x1560;OI=1;OV=9.0;CP=37;RM=4096;",
    "p40 lite e": "RE=2020.03;SZ=76.1x159.8x8.1;WT=176;DS=6.39;RS=720x1560;OI=1;OV=9.0;CP=37;RM=4096;",
    "ascend d1": "DS=4.5;RS=720x1280;SZ=64x129x8.9;WT=132;RE=2012.07;OI=1;OV=4;RM=1024;CP=29;",
    "p40 lite": "RE=2020.02;SZ=76.3x159.2x8.7;WT=183;DS=6.4;RS=1080x2310;OI=1;OV=10;CP=57;RM=6144;",
    "jny-lx1": "->p40 lite",
    "z100-tl10": "->ascend d3",
    "z100-cl00": "->ascend d3",
    "z100-ul00": "->ascend d3",
    "ascend d3": "DS=6.1;RS=1080x1920;OI=1;OV=4.4;RE=2014;RM=2048;CP=30;",
    "u8818": "->ascend g300",
    "ascend g300": "DS=4;RS=480x800;SZ=63x122.5x10.5;RE=2012.04;WT=140;OI=1;OV=2.3;RM=512;CP=31;",
    "g527-u081": "->ascend g527",
    "ascend g527": "DS=4.5;RS=540x960;SZ=67.5x134.5x9.9;RE=2013.08;OI=1;OV=4.1;WT=150;RM=1024;CP=32;",
    "c8817d": "->ascend g620s",
    "g620s-l01": "->ascend g620s",
    "g620s-l03": "->ascend g620s",
    "ascend g620s": "DS=5;RS=720x1280;SZ=72.05x142.95x8.5;RM=1024;WT=160;SM=1;OI=1;OV=4.4;SP=17;RE=2015.07;",
    "g630-u251": "->ascend g630",
    "ascend g630": "DS=5;RS=720x1280;SZ=71.7x143x7.8;RM=1024;WT=165;OI=1;OV=4.3;RE=2014.07;CP=33;",
    "g730-c00": "->ascend g730",
    "ascend g730": "DS=5.5;RS=540x960;SZ=78.3x151.5x9.6;WT=180;OI=1;OV=4.3;RE=2014.04;RM=1024;CP=7;SM=2;",
    "g7-l01": "->ascend g7",
    "g7-tl00": "->ascend g7",
    "ascend g7": "DS=5.5;RS=720x1280;SZ=77.3x153.5x7.6;WT=165;OI=1;OV=4.4;RE=2014.10;RM=2048;CP=17;SM=1;",
    "ascend mate 2": "RS=1080x1920;DS=6.1;CP=78;RM=2048;OI=1;OV=4.2.2;SZ=84.9x161x9.54;WT=202;RE=2014.02;",
    "mt2l03": "->ascend mate 2",
    "mt2-l03": "->ascend mate 2",
    "u9200": "->ascend p1",
    "ascend p1": "DS=4.3;RS=540x960;RE=2012.05;WT=110;SZ=64.8x127.4x7.7;SM=1;OI=1;OV=4;RM=1024;CP=14;",
    "p7-l10": "->ascend p7",
    "ascend p7": "DS=5;RS=1080x1920;SZ=68.8x139.8x6.5;WT=124;OI=1;OV=4.4;RE=2014.05;RM=2048;CP=34;SM=1;",
    "h1711": "->ascend xt2",
    "ascend xt2": "DS=5.5;RS=720x1280;SZ=76.2x153.4x8.4;WT=165.00;RM=2048;CP=35;OI=1;OV=7;SM=1;RE=2017.10;",
    "y221-u12": "->ascend y221",
    "y221-u22": "->ascend y221",
    "y221-u03": "->ascend y221",
    "y221-u33": "->ascend y221",
    "y221-u43": "->ascend y221",
    "y221-u53": "->ascend y221",
    "ascend y221": "DS=3.5;RS=320x480;RE=2014.12;WT=130;SZ=60.6x116.5x12.3;OI=1;OV=4.4;CP=36;RM=512;",
    "y320-u10": "->ascend y320",
    "y320-t00": "->ascend y320",
    "y320-u05": "->ascend y320",
    "ascend y320": "DS=4;SZ=63.2x123.3x11.1;RS=480x800;OI=1;OV=4.2;RE=2013.12;WT=123;RM=512;CP=24;",
    "mt7-l09": "->ascend mate 7",
    "mt7-tl00": "->ascend mate 7",
    "mt7-tl10": "->ascend mate 7",
    "mt7-cl00": "->ascend mate 7",
    "ascend mate 7": "RE=2014.09;SZ=81x157x7.9;WT=185;DS=6.0;RS=1080x1920;OI=1;OV=4.4.2;CP=70;RM=2048;",
    "bucare y330-u05": "->ascend y330",
    "y330-u05": "->ascend y330",
    "ascend y330": "DS=4;SZ=63.5x122.1x11.3;RS=480x800;WT=126;SM=2;OI=1;OV=4.2;RM=512;CP=24;RE=2014.03;",
    "541-u02": "->ascend y5c",
    "ascend y5c": "DS=4.5;RS=480x800;SZ=66.7x134.3x10;WT=141;SM=2;RM=1024;OI=1;OV=4.4;CP=6;RE=2017.08;",
    "ask-al00x": "->honor play 3",
    "honor play 3": "RE=2019.09;SZ=76.1x159.8x8.1;WT=176;DS=6.39;RS=720x1560;OI=1;OV=9;CP=37;RM=4096;",
    "ath-al00": "->honor 7i",
    "ath-cl00": "->honor 7i",
    "ath-tl00": "->honor 7i",
    "ath-tl00h": "->honor 7i",
    "ath-ul00": "->honor 7i",
    "honor 7i": "DS=5.2;RS=1080x1920;SZ=71.2x141.6x7.8;WT=160;RE=2015.08;RM=3072;CP=22;OI=1;OV=5.1;",
    "ath-ul01": "->shotx",
    "ath-ul06": "->shotx",
    "shotx": "DS=5.2;RS=1080x1920;SZ=71.2x141.6x7.8;WT=160;RE=2015.08;RM=2048;CP=22;OI=1;OV=5.1;",
    "atu-al10": "->enjoy 8e",
    "atu-tl10": "->enjoy 8e",
    "enjoy 8e": "DS=5.7;RS=720x1440;SZ=73x152.4x7.8;WT=150;RM=3072;CP=43;RE=2018.03;OI=1;OV=8;SM=1;",
    "atu-l21": "->y6 (2018)",
    "atu-l22": "->y6 (2018)",
    "atu-lx3": "->y6 (2018)",
    "atu-l11": "->y6 (2018)",
    "y6 (2018)": "RE=2018.04;SZ=73x152.4x7.8;WT=150;DS=5.7;RS=720x1440;OI=1;OV=8.0;CP=20;RM=2048;",
    "atu-l31": "->y6 prime (2018)",
    "atu-l42": "->y6 prime (2018)",
    "y6 prime (2018)": "RE=2018.04;SZ=73x152.4x7.8;WT=150;DS=5.7;RS=720x1440;OI=1;OV=8.0;CP=20;RM=2048;",
    "aum-al00": "->honor 7a",
    "aum-al20": "->honor 7a",
    "aum-l33": "->honor 7a",
    "aum-tl20": "->honor 7a",
    "honor 7a": "DS=5.7;RS=720x1440;SZ=73x152.4x7.8;WT=150;RE=2018.04;RM=2048;CP=43;OI=1;OV=8;",
    "aum-al00in": "->honor 7a",
    "aum-l29": "->honor 7a pro",
    "bac-l01": "->nova 2 plus",
    "bac-l03": "->nova 2 plus",
    "bac-al00": "->nova 2 plus",
    "bac-tl00": "->nova 2 plus",
    "bac-l21": "->nova 2 plus dual sim",
    "bac-l22": "->nova 2 plus dual sim",
    "bac-l23": "->nova 2 plus dual sim",
    "nova 2 plus dual sim": "->nova 2 plus;SM=2;",
    "nova 2 plus": "RE=2017.05;SZ=74.9x153.9x6.9;WT=169;DS=5.5;RS=1080x1920;OI=1;OV=7.0;CP=42;RM=4096;SM=1;",
    "aum-l41": "->honor 7c",
    "lnd-al30": "->honor 7c",
    "lnd-l29": "->honor 7c",
    "honor 7c": "DS=6;RS=720x1440;SZ=76.7x158.3x7.8;WT=164;RE=2018.04;RM=3072;CP=214;OI=1;OV=8;",
    "bah-al00": "->mediapad m3 lite 10",
    "bah-l09": "->mediapad m3 lite 10",
    "mediapad m3 lite 10": "DS=10.1;RS=1920x1200;SZ=241.3x171.5x7.1;WT=310.0;RE=2017.05;RM=4096;CP=35;OI=1;OV=7.0;",
    "bah2-al00": "->mediapad m5 lite",
    "bah2-al10": "->mediapad m5 lite",
    "bah2-l09": "->mediapad m5 lite",
    "bah2-w19": "->mediapad m5 lite",
    "bah2-w09": "->mediapad m5 lite",
    "jdn2-w09": "->mediapad m5 lite;DS=8;RE=2019.03;OI=1;OV=9.0;SM=1;WT=310;SZ=204.2x122.2x8.2;RM=3072;CP=28;SM=1;",
    "jdn2-al00": "->mediapad m5 lite;DS=8;RE=2019.03;OI=1;OV=9.0;SM=1;WT=310;SZ=204.2x122.2x8.2;RM=3072;CP=28;SM=1;",
    "jdn2-al50": "->mediapad m5 lite;DS=8;RE=2019.03;OI=1;OV=9.0;SM=1;WT=310;SZ=204.2x122.2x8.2;RM=3072;CP=28;SM=1;",
    "jdn2-l09": "->mediapad m5 lite;DS=8;RE=2019.03;OI=1;OV=9.0;SM=1;WT=310;SZ=204.2x122.2x8.2;RM=3072;CP=28;SM=1;",
    "mediapad m5 lite": "RE=2018.09;SZ=243.4x162.2x7.7;WT=475;DS=10.1;RS=1920x1200;OI=1;OV=8.0;CP=42;RM=4096;",
    "bah3-al00": "->matepad 10.4\"",
    "bah3-l09": "->matepad 10.4\"",
    "bah3-w09": "->matepad 10.4\"",
    "matepad 10.4\"": "RE=2020.04;SZ=245.2x155x7.4;WT=450;DS=10.4;RS=2000x1200;OI=1;OV=10;CP=57;RM=3072;",
    "bg2-u01": "->mediapad t3 7",
    "bg2-u03": "->mediapad t3 7",
    "bg2-w09": "->mediapad t3 7",
    "mediapad t3 7": "RE=2017.04;SZ=179x103.7x8.6;WT=250;DS=7.0;RS=1024x600;OI=1;OV=6.0;CP=184;RM=1024;",
    "mediapad t2 7.0 pro": "RE=2016.08;SZ=187.4x105.8x8.2;WT=250;DS=7.0;RS=1920x1080;OI=1;OV=5.1 ;CP=22;RM=2048;",
    "ple-701l": "->mediapad t2 7.0 pro",
    "ple-703l": "->mediapad t2 7.0 pro",
    "bgo-dl09": "->mediapad t2 7.0 pro",
    "bgo-l03": "->mediapad t2 7.0",
    "mediapad t2 7.0": "RE=2016.08;SZ=191.8x107x8.5;WT=278;DS=7.0;RS=1024x600;OI=1;OV=6;CP=215;RM=1024;",
    "bkk-al00": "->honor 8c",
    "bkk-al10": "->honor 8c",
    "bkk-l21": "->honor 8c",
    "bkk-l22": "->honor 8c",
    "bkk-lx2": "->honor 8c",
    "bkk-tl00": "->honor 8c",
    "honor 8c": "DS=6.3;RS=720x1520;SZ=75.94x158.72x7.98;WT=167;RE=2018.11;RM=4096;CP=64;OI=1;OV=8.1;",
    "honor view 10": "DS=5.99;RS=1080x2160;SZ=74.98x157x6.97;WT=172;RE=2017.12;RM=6144;CP=50;OI=1;OV=9;",
    "bkl-l04": "->honor view 10",
    "bkl-al20": "->honor v10",
    "bkl-l09": "->honor v10",
    "honor v10": "RS=1080x2160;DS=5.99;CP=50;RM=6144;OI=1;OV=8.0;SZ=74.98x157x6.97;WT=172;RE=2017.12;",
    "bla-l09": "->mate 10 pro",
    "bla-l29": "->mate 10 pro",
    "bla-tl00": "->mate 10 pro",
    "bla-al00": "->mate 10 pro",
    "bla-a09": "->mate 10 pro",
    "mate 10 pro": "RE=2017.10;SZ=74.5x154.2x7.9;WT=178;DS=6.0;RS=1080x2160;OI=1;OV=8.0;CP=50;RM=4096;",
    "bll-l22": "->gr5 (2017)",
    "bll-l21": "->gr5 (2017)",
    "gr5 (2017)": "DS=5.5;RS=1080x1920;SZ=76.3x151.3x8.15;;WT=162;RE=2017;RM=3072;CP=48;OI=1;OV=6.0;SM=2;",
    "bll-l23": "->mate 9 lite",
    "mate 9 lite": "DS=5.5;RS=1080x1920;SZ=76.3x151.3x8.15;;WT=162;RE=2017;RM=3072;CP=48;OI=1;OV=6.0;SM=2;",
    "bln-l21": "->honor 6x",
    "bln-l22": "->honor 6x",
    "bln-l24": "->honor 6x",
    "bln-al10": "->honor 6x",
    "bln-al20": "->honor 6x",
    "bln-al30": "->honor 6x",
    "bln-al40": "->honor 6x",
    "bln-tl00": "->honor 6x",
    "bln-tl10": "->honor 6x",
    "honor 6x": "DS=5.5;RS=1080x1920;SZ=76.2x150.9x8.2;WT=162;RE=2016.10;RM=3072;CP=48;OI=1;OV=6;SM=2;",
    "bnd-al00": "->honor 7x",
    "bnd-al10": "->honor 7x",
    "bnd-l21": "->honor 7x",
    "bnd-l24": "->honor 7x",
    "bnd-l31": "->honor 7x",
    "bnd-tl10": "->honor 7x",
    "bnd-l22": "->honor 7x",
    "bnd-l2": "->honor 7x",
    "honor 7x": "RE=2017.12;SZ=75.3x156.5x7.6;WT=165;DS=5.93;RS=1080x2160;OI=1;OV=7.0;CP=42;RM=3072;",
    "bnd-l34": "->mate se",
    "mate se": "RS=1080x2160;DS=5.93;CP=42;RN=4096;OI=1;OV=7.0;SZ=75.3x156.5x7.6;WT=165;RE=2018.03;",
    "btv-dl09": "->mediapad m3",
    "btv-w09": "->mediapad m3 8",
    "mediapad m3 8": "RE=2016.09;SZ=215.5x124.2x7.3;WT=326;DS=8.4;RS=2560x1600;OI=1;OV=6.0;216;RM=4096;",
    "mediapad m3": "->mediapad m3 8",
    "bzk-l00": "->mediapad t3 8",
    "bzk-w00": "->mediapad t3 8",
    "kob-l09": "->mediapad t3 8",
    "kob-w09": "->mediapad t3 8",
    "mediapad t3 8": "RS=1280x800;DS=8;CP=20;RM=2048;OI=1;OV=7.0;SZ=211.1x124.7x8;WT=350;RE=2017.04;",
    "bza-w00": "->c3 9.6",
    "c3 9.6": "",
    "bzt-al00": "->mediapad c5 10.1",
    "bzt-al10": "->mediapad c5 10.1",
    "c5 10 (2020)": "->mediapad c5 10.1",
    "mediapad c5 10.1": "RM=4096;RE=2020.07;DS=10.4;SZ=245.2x154.96x7.35;WT=450;RS=2000x1200;CP=57;SM=1;OI=1;OV=10;",
    "c8500s": "RS=240x320;DS=2.8;CP=217;RM=256;OI=1;OV=2.2;SZ=56x104x13.8;WT=110;RE=2011.07;",
    "c8812": "RS=480x800;DS=4;CP=218;RM=1024;OI=1;OV=4.0;SZ=62.66x122.3x11.39;WT=135;RE=2012.05;",
    "c8813": "RS=480x854;DS=4.5;CP=219;RM=512;OI=1;OV=4.1;SZ=67x132.5x9.3;WT=130;RE=2012.05;",
    "c8813q": "RS=480x854;DS=4.5;CP=220;RM=512;OI=1;OV=4.1;SZ=67x132.5x9.3;WT=156;RE=2012.05;",
    "c8815": "RS=540x960;DS=5;CP=220;RM=1024;OI=1;OV=4.1;SZ=73.6x142x9.7;WT=170;RE=2013.08;",
    "cam-al00": "->honor 5a",
    "cam-tl00": "->honor 5a",
    "cam-tl00h": "->honor 5a",
    "honor 5a": "DS=5.5;RS=720x1280;SZ=77.1x154.3x8.5;WT=168;RE=2016.07;RM=2048;CP=65;OI=1;OV=6;SM=2;",
    "cag-l22": "->y3 (2018)",
    "cag-l02": "->y3 (2018)",
    "y3 (2018)": "RE=2018.05;SZ=73.7x145.1x9.5;WT=175;DS=5.0;RS=480x854;OI=1;OV=8.0;CP=86;RM=1024;",
    "cag-l03": "->y5 lite (2018)",
    "cag-l23": "->y5 lite (2018)",
    "y5 lite (2018)": "RE=2018.12;SZ=70.9x146.5x8.3;WT=142;DS=5.45;RS=720x1440;OI=1;OV=8.1;CP=8;RM=1024;",
    "cam-l03": "->y6ii",
    "cam-l21": "->y6ii",
    "cam-l32": "->y6ii",
    "y6ii": "RE=2016.09;SZ=72x143.8x8.9;WT=140;DS=5.0;RS=720x1280;OI=1;OV=5.1;CP=79;RM=1024;",
    "can-l01": "->nova",
    "can-l02": "->nova",
    "can-l03": "->nova",
    "can-l11": "->nova",
    "can-l12": "->nova",
    "can-l13": "->nova",
    "caz-al00": "->nova",
    "caz-al10": "->nova",
    "caz-tl10": "->nova",
    "caz-tl20": "->nova",
    "nova": "RE=2016.09;SZ=69.1x141.2x7.1;WT=146;DS=5.0;RS=1080x1920;OI=1;OV=6.0.1;CP=204;RM=3072;",
    "cdy-an00": "->nova 7 se 5g",
    "cdy-nx9b": "->nova 7 se 5g",
    "cdy-tn00": "->nova 7 se 5g",
    "nova 7 se": "RE=2020.04;SZ=75x162.3x8.6;WT=189;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=55;RM=6144;",
    "cdy-an90": "->honor 30s",
    "cdy-nx9a": "->honor 30s",
    "honor 30s": "DS=6.5;RS=1080x2400;SZ=75x162.3x8.6;WT=190;RE=2020.05;RM=6144;CP=55;OI=1;OV=10;SM=2;",
    "chc-u01": "->g play mini",
    "chc-u03": "->g play mini",
    "chc-u23": "->g play mini",
    "g play mini": "DS=5;RS=720x1280;SZ=71.9x143.3x8.8;WT=162;RE=2015.04;RM=2048;CP=3;OI=1;OV=4.4;SM=2;",
    "che-tl00h": "->honor 4x",
    "che1-cl10": "->honor 4x",
    "che1-cl20": "->honor 4x",
    "che1-l04": "->honor 4x",
    "che2-l11": "->honor 4x",
    "che2-l12": "->honor 4x",
    "che2-l23": "->honor 4x",
    "che2-tl00": "->honor 4x",
    "che2-tl00h": "->honor 4x",
    "che2-tl00m": "->honor 4x",
    "che2-ul00": "->honor 4x",
    "che-tl00": "->honor 4x",
    "honor 4x": "DS=5.5;RS=720x1280;SZ=77.2x152.9x8.7;WT=170;RE=2014.10;RM=2048;CP=17;OI=1;OV=4.4;SM=2;",
    "chm-tl00": "->honor play 4c",
    "chm-tl00h": "->honor play 4c",
    "chm-ul00": "->honor play 4c",
    "honor play 4c": "RS=720x1280;DS=5;CP=3;RM=2048;OI=1;OV=4.4.2;SZ=71.9x143.3x8.8;WT=162;RE=2015.04;",
    "chm-u01": "->honor 4c",
    "honor 4c": "DS=5;RS=720x1280;SZ=71.9x143.3x8.8;WT=162;RE=2015.04;RM=2048;CP=3;OI=1;OV=4.4;SM=2;",
    "clt-al00": "->p20 pro",
    "clt-al01": "->p20 pro",
    "clt-l09": "->p20 pro",
    "clt-l29": "->p20 pro",
    "clt-tl00": "->p20 pro",
    "clt-tl01": "->p20 pro",
    "hw-01k": "->p20 pro",
    "clt-l29c": "->p20 pro",
    "clt-l09c": "->p20 pro",
    "clt-al00l": "->p20 pro",
    "clt-l04": "->p20 pro",
    "p20 pro": "RE=2018.05;SZ=73.9x155x7.8;WT=180;DS=6.1;RS=1080x2240;OI=1;OV=8.1;CP=50;RM=6144;",
    "cmr-al19": "->mediapad m5 10.8",
    "cmr-al09": "->mediapad m5 10.8",
    "cmr-w09": "->mediapad m5 10.8",
    "mediapad m5 10.8": "RE=2018.04;SZ=258.7x171.8x7.3;WT=498;DS=10.8;RS=2560x1600;OI=1;OV=8.0;CP=222;RM=4096;",
    "cmr-w19": "->mediapad m5 pro 10.8",
    "mediapad m5 pro 10.8": "RE=2018.02;SZ=258.7x171.8x7.3;WT=498;DS=10.8;RS=2560x1600;OI=1;OV=8.0;CP=221;RM=4096;",
    "sht-al09": "->mediapad m5 8.4",
    "sht-w09": "->mediapad m5 8.4",
    "mediapad m5 8.4": "RE=2018.02;SZ=212.6x124.8x7.3;WT=316;DS=8.4;RS=2560x1600;OI=1;OV=8.0;CP=222;RM=4096;",
    "col-l29": "->honor 10",
    "col-al00": "->honor 10",
    "col-al10": "->honor 10",
    "col-tl10": "->honor 10",
    "col-tl00": "->honor 10",
    "honor 10": "DS=5.84;RS=1080x2280;SZ=71.2x149.6x7.7;WT=153;RE=2018.04;RM=4096;CP=50;OI=1;OV=8.1;SM=2;",
    "cor-al00": "->honor play",
    "cor-al10": "->honor play",
    "cor-tl10": "->honor play",
    "cor-l29": "->honor play",
    "cor-l09": "->honor play",
    "honor play": "RE=2018.07;SZ=74.3x157.9x7.5;WT=176;DS=6.3;RS=1080x2340;OI=1;OV=8.1;CP=50;RM=4096;",
    "cro-u00": "->y3 (2017)",
    "cro-l02": "->y3 (2017)",
    "cro-l22": "->y3 (2017)",
    "y3 (2017)": "RE=2017.05;SZ=73.7x145.1x9.5;WT=175;DS=5.0;RS=480x854;OI=1;OV=6.0;CP=58;RM=1024;",
    "cro-l03": "->y5 lite (2017)",
    "cro-l23": "->y5 lite (2017)",
    "y5 lite (2017)": "RE=2017.04;SZ=72x143.8x8.4;WT=150;DS=5.0;RS=720x1280;OI=1;OV=6.0;CP=69;RM=2048;",
    "crr-l09": "->mate s",
    "crr-ul00": "->mate s",
    "crr-ul20": "->mate s",
    "mate s": "E=2015.09;SZ=75.3x149.8x7.2;WT=156;DS=5.5;RS=1080x1920;OI=1;OV=5.1.1;CP=223;RM=3072;",
    "cun-al00": "->honor 5 play",
    "cun-tl00": "->honor 5 play",
    "honor 5 play": "DS=5;RS=720x1280;SZ=72x143x8.9;WT=138;RE=;RM=2048;CP=56;OI=1;OV=5.1;SM=1;",
    "cun-l01": "->y5ii",
    "cun-l02": "->y5ii",
    "cun-l03": "->y5ii",
    "cun-l21": "->y5ii",
    "cun-l22": "->y5ii",
    "cun-l23": "->y5ii",
    "cun-l33": "->y5ii",
    "cun-u29": "->y5ii",
    "y5ii": "RE=2016.04;SZ=72x143.8x8.9;WT=135;DS=5.0;RS=720x1280;OI=1;OV=5.1;CP=56;RM=1024;",
    "d-02h": "->d tab compact",
    "d-02k": "->d tab compact",
    "d-01j": "->d tab compact",
    "d tab compact": "DS=8.4;RS=2560x1600;SZ=215.5x124.2x7.3;WT=326;SM=1;RM=3072;CP=216;RE=2016.10;OI=1;OV=6.0;",
    "d-01k": "->dtab d-01k",
    "dtab d-01k": "DS=10.1;RS=1920x1200;SZ=248x173x7.8;WT=475;RM=3072;CP=42;OI=1;OV=7.0;RE=2017.10;SM=1;",
    "dig-tl10": "->enjoy 6s",
    "dig-al00": "->enjoy 6s",
    "enjoy 6s": "DS=5;RS=720x1280;SZ=69.9x143.5x7.6;WT=138;RM=3072;CP=35;RE=2017.01;OI=1;OV=6;SM=2;",
    "dig-l01": "->nova smart",
    "nova smart": "RE=2016.12;SZ=69.9x143.5x7.6;WT=138;DS=5.0;RS=720x1280;OI=1;OV=6;CP=35;",
    "dig-l21": "->gr3 (2017)",
    "dig-l22": "gr3 (2017)",
    "gr3 (2017)": "DS=5.2;RS=1080x1920;SZ=72.94x147.2x7.6;WT=147;RE=2017;RM=3072;CP=48;OI=1;OV=7;SM=2;",
    "dig-l21hn": "->honor 6c",
    "honor 6c": "DS=5;RS=720x1280;SZ=69.9x143x7.3;WT=138;RE=2017.04;RM=3072;CP=35;OI=1;OV=6;SM=2",
    "dig-l23": "->p9 lite smart",
    "p9 lite smart": "DS=5;RS=720x1280;SZ=69.9x143.5x7.6;WT=138;RM=3072;CP=35;RE=2017.01;OI=1;OV=6;SM=2;",
    "dli-al10": "->honor 6a",
    "dli-tl20": "->honor 6a",
    "dli-l22": "->honor 6a",
    "dli-l42": "->honor 6a",
    "honor 6a": "DS=5;RS=720x1280;SZ=70.95x143.7x8.2;WT=143;RE=2017.07;RM=2048;CP=43;OI=1;OV=7;SM=2;",
    "dnn-lx9": "->honor x10 lite",
    "honor x10 lite": "DS=6.67;RS=1080x2400;SZ=76.88x165.65x9.26;WT=206;RM=4096;CP=213;OI=1;OV=10;SM=2;RE=2020.11;",
    "dra-al00": "->y5 prime (2018)",
    "dra-l01": "->y5 (2018)",
    "dra-l21": "->y5 (2018)",
    "dra-lx3": "->y5 (2018)",
    "y5 (2018)": "RE=2018.05;SZ=70.9x146.5x8.3;WT=142;DS=5.45;RS=720x1440;OI=1;OV=8.1;CP=8;RM=2048;",
    "dua-lx2": "y5 (2018)",
    "dua-l21": "y5 (2018)",
    "dra-tl00": "->y5 prime (2018)",
    "dra-lx2": "->y5 prime (2018)",
    "y5 prime (2018)": "->y5 (2018)",
    "dra-lx5": "->y5 lite",
    "y5 lite": "RE=2018.12;SZ=70.9x146.5x8.3;WT=142;DS=5.45;RS=720x1440;OI=1;OV=8.1;CP=8;RM=1024;",
    "dra-lx9": "->y5p",
    "y5p": "RE=2020.05;SZ=70.9x146.5x8.4;WT=144;DS=5.45;RS=720x1440;OI=1;OV=10;CP=47;RM=2048",
    "dua-tl00": "->honor play 7",
    "dua-al00": "->honor play 7",
    "honor play 7": "DS=5.45;RS=720x1440;SZ=70.9x146.5x8.3;WT=142;RE=2018.05;OI=1;OV=8.1;RM=2048;CP=8;",
    "dua-l22": "->honor 7s",
    "dua-lx3": "->honor 7s",
    "dua-l12": "->honor 7s",
    "honor 7s": "DS=5.45;RS=720x1440;SZ=70.9x146.5x8.3;WT=142;RE=2018.05;RM=2048;CP=8;OI=1;OV=8.1;",
    "dua-lx9": "->honor 9s",
    "honor 9s": "RE=2020.04;SZ=70.9x146.5x8.4;WT=144;DS=5.45;RS=720x1440;OI=1;OV=10;CP=47;RM=2048;",
    "hi6210sft": "->p8 lite (2017)",
    "dub-lx2": "->y7 pro (2019)",
    "dub-al00": "->y7 pro (2019)",
    "dub-al20": "->y7 pro (2019)",
    "y7 pro (2019)": "RE=2019.01;SZ=76.9x158.9x8.1;WT=168;DS=6.26;RS=720x1520;OI=1;OV=8.1;CP=224;RM=3072;",
    "dub-lx1": "->y7 (2019)",
    "dub-lx3": "->y7 (2019)",
    "dub-tl00": "->y7 (2019)",
    "y7 (2019)": "RE=2019.01;SZ=76.9x158.9x8.1;WT=168;DS=6.26;RS=720x1520;OI=1;OV=8.1;CP=224;RM=3072;",
    "duk-al20": "->honor v9",
    "honor v9": "RE=2017.04;DS=5.7;RS=1440x2560;SZ=77.5x157x6.97;WT=184;OI=1;OV=7.0;SM=2;CP=221;RM=6144;",
    "duk-l09": "->honor 8 pro",
    "duk-tl30": "->honor 8 pro",
    "honor 8 pro": "RE=2017.04;SZ=77.5x157x7;WT=184;DS=5.7;RS=1440x2560;OI=1;OV=7.0;221;RM=4096;",
    "dvc-tn20": "->enjoy 20 pro",
    "dvc-an20": "->enjoy 20 pro",
    "enjoy 20 pro": "DS=6.5;RS=1080x2400;SZ=160x75.3x8.4;WT=192;RM=6144;CP=45;RE=2020.06;OI=1;OV=10;SM=2;",
    "ebg-an00": "->honor 30 pro",
    "honor 30 pro": "DS=6.57;RS=1080x2340;SZ=73.6x160.3x8.4;WT=186;RE=2020.04;RM=8192;CP=54;OI=1;OV=10;SM=2;",
    "ebg-an10": "->honor 30 pro plus",
    "honor 30 pro plus": "DS=6.57;RS=1080x2340;SZ=73.6x160.3x8.4;WT=190;RE=2020.04;RM=8192;CP=54;OI=1;OV=10;SM=2;",
    "edi-al10": "->honor note 8",
    "honor note 8": "DS=6.6;RS=1440x2560;SZ=90.9x178.8x7.18;WT=219;RE=2016.08;RM=4096;CP=225;OI=1;OV=6;",
    "ele-al00": "->p30",
    "ele-tl00": "->p30",
    "p30": "RE=2019.05;SZ=71.4x149.1x7.6;WT=165;DS=6.1;RS=1080x2340;OI=1;OV=9.0;CP=52;RM=8192;",
    "els-an00": "->p40 pro",
    "els-tn00": "->p40 pro",
    "els-nx9": "->p40 pro",
    "els-n04": "->p40 pro",
    "p40 pro": "RE=2020.05;SZ=72.6x158.2x9;WT=209;DS=6.58;RS=1200x2640;OI=1;OV=10;CP=54;RM=8192;",
    "els-an10": "->p40 pro plus",
    "els-n39": "->p40 pro plus",
    "p40 pro plus": "RE=2020.05;SZ=72.6x158.2x9;WT=226;DS=6.58;RS=1200x2640;OI=1;OV=10;CP=54;RM=8192;",
    "eml-al00": "->p20",
    "eml-l09": "->p20",
    "eml-l29": "->p20",
    "eml-tl00": "->p20",
    "p20": "RE=2018.05;SZ=70.8x149.1x7.7;WT=165;DS=5.8;RS=1080x2240;OI=1;OV=8.1;CP=50;RM=4096;",
    "stk-tl00": "->enjoy 10 plus",
    "stk-al00": "->enjoy 10 plus",
    "enjoy 10 plus": "RE=2019.09;SZ=77.3x163.5x8.8;WT=163.5;DS=6.59;RS=1080x2340;OI=1;OV=9.0;CP=37;RM=4096;",
    "med-al00": "->enjoy 10e",
    "med-tl00": "->enjoy 10e",
    "enjoy 10e": "DS=6.3;RS=720x1600;SZ=74.1x159.1x9.04;WT=185;RM=4096;CP=38;OI=1;OV=10;SM=2;RE=2020.03;SM=2;",
    "tag-al00": "->enjoy 5s",
    "tag-cl00": "->enjoy 5s",
    "tag-tl00": "->enjoy 5s",
    "enjoy 5s": "DS=5;RS=720x1280;SZ=71x143x7.6;WT=135;RM=2048;CP=39;OI=1;OV=5.1;SM=2;RE=2015.12",
    "nce-al10": "->enjoy 6",
    "nce-al00": "->enjoy 6",
    "nce-tl10": "->enjoy 6",
    "enjoy 6": "DS=5;RS=720x1280;SZ=70.4x143.2x7.9;WT=145;RM=3072;CP=40;RE=2016.10;OI=1;OV=6;SM=2;",
    "trt-al00": "->enjoy 7 plus",
    "trt-al00a": "->enjoy 7 plus",
    "trt-tl10a": "->enjoy 7 plus",
    "enjoy 7 plus": "DS=5.5;RS=720x1280;SZ=76.4x153.6x8.4;WT=165;RM=3072;CP=35;RE=2013.04;OI=1;OV=7;",
    "sla-al00": "->enjoy 7",
    "sla-tl10": "->enjoy 7",
    "sla-tl00": "->enjoy 7",
    "enjoy 7": "DS=5;RS=720x1280;SZ=71x143.5x8.05;WT=145;RM=3072;CP=20;OI=1;OV=7;RE=2017.07",
    "fig-al00": "->enjoy 7s",
    "fig-tl00": "->enjoy 7s",
    "fig-tl10": "->enjoy 7s",
    "fig-al10": "->enjoy 7s",
    "enjoy 7s": "DS=5.65;RS=1080x2160;SZ=72.1x150.1x7.5;WT=143;RE=2017.12;OI=1;OV=8;RM=3072;CP=42;",
    "ldn-tl20": "->enjoy 8",
    "ldn-al10": "->enjoy 8",
    "ldn-al20": "->enjoy 8",
    "ldn-tl00": "->enjoy 8",
    "enjoy 8": "DS=5.99;RS=720x1440;WT=155;SZ=76.7x158.3x7.8;RM=3072;CP=43;RE=2018.03;OI=1;OV=8;",
    "mrd-al00": "enjoy 9e",
    "mrd-tl00": "enjoy 9e",
    "enjoy 9e": "DS=6.09;RS=720x1560;SZ=73.5x156.28x8;WT=150;RM=3072;CP=44;RE=2019.03;OI=1;OV=9;SM=2;",
    "eva-al00": "->p9",
    "eva-cl00": "->p9",
    "eva-dl00": "->p9",
    "eva-l09": "->p9",
    "eva-tl00": "->p9",
    "p9": "RE=2016.04;SZ=70.9x145x7;WT=144;DS=5.2;RS=1080x1920;OI=1;OV=6.0;CP=225;RM=3072;",
    "evr-al00": "->mate 20 x",
    "evr-an00": "->mate 20 x",
    "evr-n29": "->mate 20 x",
    "evr-tl00": "->mate 20 x",
    "mate 20 x": "RE=2018.10;SZ=85.4x174.6x8.2;WT=232;DS=7.2;RS=1080x2244;OI=1;OV=9.0;CP=52;RM=6144;",
    "fdr-a01l": "->mediapad t2 10.0\" pro",
    "fdr-a01w": "->mediapad t2 10.0\" pro",
    "fdr-a03l": "->mediapad t2 10.0\" pro",
    "mediapad t2 10.0\" pro": "RE=2016.08;SZ=259.1x156.4x8.5;WT=495;DS=10.0;RS=1920x1200;OI=1;OV=5.1;CP=22;RM=2048;",
    "fig-lx1": "->p smart",
    "fig-lx2": "->p smart",
    "p smart": "RE=2017.12;SZ=72.1x150.1x7.5;WT=143;DS=5.65;RS=1080x2160;OI=1;OV=8.0;CP=42;RM=3072;",
    "fla-al20": "->y9 (2018)",
    "fla-lx1": "->y9 (2018)",
    "fla-tl10": "->y9 (2018)",
    "y9 (2018)": "RE=2018.05;SZ=75.3x157.2x7.9;WT=170;DS=5.93;RS=1080x2160;OI=1;OV=8.0;CP=42;RM=3072;",
    "frd-al00": "->honor 8",
    "frd-al10": "->honor 8",
    "frd-dl00": "->honor 8",
    "frd-l02": "->honor 8",
    "frd-l04": "->honor 8",
    "frd-l09": "->honor 8",
    "frd-l14": "->honor 8",
    "frd-l19": "->honor 8",
    "vat-l19": "->honor 8",
    "honor 8": "DS=5.2;RS=71x145.5x7.5;SZ=71x145.5x7.45;WT=153;RE=2016.07;RM=4096;CP=216;OI=1;OV=8;",
    "frl-l22": "->y9a",
    "frl-l23": "->y9a",
    "y9a": "RE=2020.09;SZ=76.5x163.5x9;WT=197;DS=6.63;RS=1080x2400;OI=1;OV=10;CP=226;RM=6144;",
    "u8665": "->fusion 2",
    "fusion 2": "DS=3.5;RS=320x480;SZ=61x115.8x11.7;WT=124.2;RE=2012.10;RM=512;CP=46;OI=1;OV=2.3;SM=1;",
    "g735-l03": "->g play",
    "g735-l12": "->g play",
    "g735-l23": "->g play",
    "g play": "DS=5.5;RS=720x1280;SZ=77.2x152.9x8.65;WT=170;RE=2015.01;RM=2048;CP=3;OI=1;OV=4.4;SM=2;",
    "g2800": "RS=128x128;DS=1.5;SZ=45x109x14.2;WT=70;",
    "g510-0010": "->ascend g510",
    "g510-0100": "->ascend g510",
    "ascend g510": "RE=2013.01;SZ=67x134x9.9;WT=150;DS=4.5;RS=480x854;OI=1;OV=4.1;CP=219;RM=512;",
    "g525-u00": "->ascend g525",
    "ascend g525": "RE=2013.01;SZ=66.8x134x9.9;WT=155;DS=4.5;RS=540x960;OI=1;OV=4.1;CP=110;RM=1024;",
    "g610-c00": "->g610s",
    "g610-u00": "->g610s",
    "g610-u20": "->g610s",
    "g610s": "RE=2013.06;SZ=73.6x142x9.9;WT=170;DS=5.0;RS=540x960;OI=1;OV=4.2;CP=27;RM=1024;",
    "g621-tl00": "->g621",
    "g621-tl00m": "->g621",
    "g621": "DS=5;RS=720x1280;SZ=72.05x142.95x8.5;WT=160;RE=2014.09;RM=1024;CP=17;OI=1;OV=4.4;SM=2;",
    "g6310": "RE=2012.03;SZ=59.8x113x12.5;WT=;DS=2.4;RS=320x240;RM=256;",
    "g6609": "RE=2012.04;SZ=60.5x113.8x11.5;WT=98;DS=2.4;RS=320x240;",
    "g6800": "RE=2012.03;SZ=59x117x12.2;WT=;DS=2.8;RS=240x320;",
    "g750-t01": "->honor 3x",
    "honor 3x": "DS=5.5;RS=720x1280;SZ=77.4x149.5x8.9;WT=162;RE=2013.12;RM=2048;CP=7;OI=1;OV=4.2;SM=2;",
    "gem-701l": "->mediapad x2",
    "gem-702l": "->mediapad x2",
    "gem-703l": "->mediapad x2",
    "mediapad x2": "RE=2015.05;SZ=183.5x103.9x7.2;WT=239;DS=7.0;RS=1920x1200;OI=1;OV=5;CP=227;RM=2048;",
    "glk-al00": "->nova 5i",
    "glk-lx1u": "->nova 5i",
    "glk-lx1": "->nova 5i",
    "glk-lx2": "->nova 5i",
    "glk-lx3": "->nova 5i",
    "glk-tl00": "->nova 5i",
    "nova 5i": "RE=2019.07;SZ=75.9x159.1x8.3;WT=178;DS=6.4;RS=1080x2310;OI=1;OV=9.0;CP=37;RM=6144;",
    "kii-l21": "->gr5",
    "gr5": "DS=5.5;RS=1080x1920;SZ=76.3x151.3x8.15;WT=158;RE=2016.02;RM=2048;CP=22;OI=1;OV=5.1;SM=2;",
    "gra-l09": "->p8",
    "gra-ul00": "->p8",
    "gra-ul10": "->p8",
    "gra-tl00": "->p8",
    "p8": "RE=2015.04;SZ=72.1x144.9x6.4;WT=144;DS=5.2;RS=1080x1920;OI=1;OV=4.4.2;CP=227;RM=3072;",
    "rio-l03": "->gx8",
    "gx8": "DS=5.5;RS=1080x1920;SZ=76.5x152x7.5;WT=167;RE=2015.10;RM=2048;CP=22;OI=1;OV=5.1;SM=2;",
    "h30-t00": "->honor 3c",
    "h30-t10": "->honor 3c",
    "h30-c00": "->honor 3c",
    "h30-l01m": "->honor 3c",
    "h30-l01": "->honor 3c",
    "h30-l02": "->honor 3c",
    "h30-u10": "->honor 3c",
    "honor 3c": "DS=5;RS=720x1280;SZ=71.4x139.5x9.2;WT=140;RE=2013.12;RM=2048;CP=7;OI=1;OV=4.2;SM=2;",
    "hol-u19": "->honor 3c lite",
    "honor 3c lite": "DS=5;RS=720x1280;SZ=72.3x142.2x9.4;WT=156;RE=2013.12;RM=1024;CP=7;OI=1;OV=4.2;SM=2;",
    "h60-l01": "->honor 6",
    "h60-l02": "->honor 6",
    "h60-l03": "->honor 6",
    "h60-l04": "->honor 6",
    "h60-l11": "->honor 6",
    "h60-l12": "->honor 6",
    "h60-j1": "->honor 6",
    "honor 6": "DS=5;RS=1080x1920;SZ=69.7x139.6x7.5;WT=130;RE=2014.08;RM=3072;CP=30;OI=1;OV=4.4;SM=2;",
    "h867g": "DS=3.5;RS=320x480;WT=128;SZ=61x117x13;OI=1;OV=4.0.4;",
    "h881c": "",
    "hdl-al09": "->honor water play 8.0",
    "hdl-w09": "->honor water play 8.0",
    "honor water play 8.0": "DS=8;RS=1920x1200;SZ=209.1x119.6x8;WT=309;RE=2018.10;OI=1;OV=8.0;RM=4096;CP=42;",
    "hdn-l09": "->honor water play 10.1",
    "hdn-w09": "->honor water play 10.1",
    "honor water play 10.1": "DS=10.1;RS=1920x1200;SZ=248x173x7.8;WT=465;RE=2017.10;OI=1;OV=7.0;RM=3072;CP=42;",
    "hlk-tl00": "->honor 9x",
    "hlk-al00": "->honor 9x",
    "hlk-al00a": "->honor 9x",
    "honor 9x": "DS=6.6;RS=1080x2340;SZ=77.3x163.5x8.8;WT=196.8;RE=2019.10;RM=4096;CP=37;OI=1;OV=9;",
    "hlk-al10": "->honor 9x pro",
    "hlk-tl10": "->honor 9x pro",
    "hlk-l41": "->honor 9x pro",
    "hlk-l42": "->honor 9x pro",
    "honor 9x pro": "DS=6.6;RS=1080x2340;SZ=77.2x163.1x8.8;WT=206;RE=2020.02;RM=6144;CP=57;OI=1;OV=10;",
    "hma-al00": "->mate 20",
    "hma-tl00": "->mate 20",
    "hma-l09": "->mate 20",
    "hma-l29": "->mate 20",
    "hma-lx9": "->mate 20",
    "mate 20": "RE=2018.10;SZ=77.2x158.2x8.3;WT=188;DS=6.53;RS=1080x2244;OI=1;OV=9.0;CP=52;RM=6144",
    "hry-al00": "->honor 10 lite",
    "hry-al00a": "->honor 10 lite",
    "hry-tl00": "->honor 10 lite",
    "hry-tl00a": "->honor 10 lite",
    "hry-lx1": "->honor 10 lite",
    "hry-lx2": "->honor 10 lite",
    "lx1meb": "->honor 10 lite",
    "honor 10 lite": "DS=6.21;RS=1080x2340;SZ=73.64x154.8x8;WT=162;RE=2018.09;RM=4096;CP=28;OI=1;OV=9;SM=2;",
    "hry-lx1t": "->honor 10i",
    "honor 10i": "DS=6.21;RS=1080x2340;SZ=73.64x154.8x7.95;WT=164;RE=2019.03;RM=4096;CP=28;OI=1;OV=9;SM=2;",
    "lra-al00": "->honor 20 lite",
    "honor 20 lite": "DS=6.21;RS=1080x2340;SZ=73.6x154.8x8;WT=164;RE=2019.04;RM=4096;CP=28;OI=1;OV=9;SM=2;",
    "yal-l41": "->honor 20 pro",
    "yal-al10": "->honor 20 pro",
    "honor 20 pro": "DS=6.26;RS=1080x2340;SZ=74x154.6x8.4;WT=182;RE=2019.05;RM=8192;CP=52;OI=1;OV=9;SM=2;",
    "yal-al00": "->honor 20",
    "yal-l21": "->honor 20",
    "yal-tl00": "->honor 20",
    "honor 20": "DS=6.26;RS=1080x2340;SZ=73.97x154.25x7.87;WT=174;RE=2019.03;RM=6144;CP=52;OI=1;OV=9;SM=2;",
    "hry-al00t": "->honor 20i",
    "hry-al00ta": "->honor 20i",
    "honor 20i": "DS=6.21;RS=1080x2340;SZ=73.6x154.8x8;WT=164;RE=2019.04;RM=6144;CP=28;OI=1;OV=9;SM=2;",
    "mar-lx1h": "->honor 20s",
    "yal-al50": "->honor 20s",
    "honor 20s": "DS=6.15;RS=1080x2312;SZ=72.7x152.9x7.4;WT=159;RE=2019.10;RM=6144;CP=28;OI=1;OV=9;SM=2;",
    "u9508": "->honor 2",
    "honor 2": "DS=4.5;RS=720x1280;SZ=67.5x134x10.5;WT=145;RE=2012.09;RM=2048;CP=;OI=1;OV=4;SM=1;",
    "bmh-an10": "->honor 30",
    "bmh-an20": "->honor 30",
    "bmh-tn10": "->honor 30",
    "mxw-an00": "->honor 30",
    "honor 30": "DS=6.53;RS=1080x2400;SZ=160.3x74.2x8.1;WT=185;RE=2020.04;RM=6144;CP=53;OI=1;OV=10;SM=2;",
    "lra-lx1": "->honor 30i",
    "honor 30i": "DS=6.3;RS=1080x2400;SZ=73.2x157.2x7.7;WT=171.5;RE=2020.09;RM=4096;CP=37;OI=1;OV=10;SM=2;",
    "scl-al00": "->honor 4a",
    "scl-cl00": "->honor 4a",
    "scl-tl00": "->honor 4a",
    "scl-tl00h": "->honor 4a",
    "honor 4a": "DS=5;RS=720x1280;SZ=72.1x143.5x8.5;WT=155;RE=2015.08;RM=2048;CP=10;OI=1;OV=5.1;SM=1;",
    "honor 4c pro": "DS=5;RS=720x1280;SZ=71.8x143.1x9.7;WT=160;RE=2015.04;RM=2048;CP=56;OI=1;OV=5.1;SM=2;",
    "nem-tl00h": "->honor 5c dual sim",
    "nem-l22": "->honor 5c dual sim",
    "honor 5c dual sim": "->honor 5c",
    "nem-al10": "->honor 5c",
    "nem-tl00": "->honor 5c",
    "nem-ul10": "->honor 5c",
    "nem-l51": "->honor 5c",
    "honor 5c": "DS=5.2;RS=1080x1920;SZ=73.8x147.1x8.3;WT=165;RE=2016.05;RM=2048;CP=66;OI=1;OV=6;SM=2;",
    "kiw-cl00": "->honor 5x",
    "kiw-al10": "->honor 5x",
    "kiw-tl00h": "->honor 5x",
    "kiw-tl00": "->honor 5x",
    "kiw-tc00": "->honor 5x",
    "kiw-l21": "->honor 5x",
    "kiw-l22": "->honor 5x",
    "kiw-l23": "->honor 5x",
    "kiw-l24": "->honor 5x",
    "kiw-ul00": "->honor 5x",
    "honor 5x": "DS=5.5;RS=1080x1920;SZ=76.3x151.3x8.2;WT=158;RE=2015.11;RM=2048;CP=68;OI=1;OV=5.1;SM=2;",
    "pe-ul00": "->honor 6 plus",
    "pe-tl10": "->honor 6 plus",
    "pe-tl20": "->honor 6 plus",
    "pe-tl00m": "->honor 6 plus",
    "honor 6 plus": "DS=5.5;RS=1080x1920;SZ=75.7x150.5x7.5;WT=165;RE=2014.12;RM=3072;CP=70;OI=1;OV=4.4;SM=2;",
    "mya-tl10": "->honor 6 play",
    "honor 6 play": "DS=5;RS=720x1280;SZ=72x143.8x8.85;WT=150;RE=2014.08;RM=2048;CP=69;OI=1;OV=6;SM=1;",
    "jmm-l22": "->honor 6c pro",
    "honor 6c pro": "DS=5.2;RS=720x1280;SZ=73.2x147.9x7.7;WT=145;RE=2017.09;RM=3072;CP=40;OI=1;OV=7;SM=2;",
    "nem-l21": "->honor 7 lite",
    "honor 7 lite": "DS=5.2;RS=1080x1920;SZ=73.8x147.1x8.3;WT=156;RE=2016.06;RM=2048;CP=66;OI=1;OV=6;",
    "plk-cl00": "->honor 7",
    "plk-al10": "->honor 7",
    "plk-l01": "->honor 7",
    "plk-tl00": "->honor 7",
    "plk-tl01h": "->honor 7",
    "plk-ul00": "->honor 7",
    "honor 7": "DS=5.2;RS=1080x1920;SZ=71.9x143.2x8.5;WT=157;RE=2015.06;OI=1;OV=6;RM=3072;CP=223;",
    "pra-tl10": "->honor 8 lite",
    "pra-al00": "->honor 8 lite",
    "pra-al00x": "->honor 8 lite",
    "honor 8 lite": "DS=5.2;RS=1080x1920;SZ=72.94x147.2x7.6;WT=147;RE=2017.02;RM=3072;CP=48;OI=1;OV=7;",
    "ven-l22": "->honor 8 smart",
    "honor 8 smart": "DS=5.2;RS=1080x1920;SZ=72.6x146.8x7.5;WT=147;RE=2016.05;RM=2048;CP=66;OI=1;OV=6;",
    "jat-l41": "->honor 8a pro",
    "honor 8a pro": "RE=2019.04;SZ=73.5x156.3x8;WT=150;DS=6.09;RS=720x1560;OI=1;OV=9.0;CP=38;RM=2048;",
    "jat-lx1": "->honor 8a",
    "jat-lx3": "->honor 8a",
    "honor 8a": "RE=2020.05;SZ=73.5x156.3x8.2;WT=150;DS=6.09;RS=720x1560;OI=1;OV=9.0;CP=38;RM=3072;",
    "ksa-al00": "->honor 8s",
    "ksa-lx2": "->honor 8s",
    "ksa-lx3": "->honor 8s",
    "ksa-lx9": "->honor 8s",
    "honor 8s": "RE=2019.04;SZ=70.8x147.1x8.5;WT=146;DS=5.71;RS=720x1520;OI=1;OV=9.0;CP=44;RM=2048;",
    "jsn-al00": "->honor 8x",
    "jsn-al00a": "->honor 8x",
    "jsn-l21": "->honor 8x",
    "jsn-l23": "->honor 8x",
    "jsn-l42": "->honor 8x",
    "jsn-tl00": "->honor 8x",
    "honor 8x": "RE=2018.09;SZ=76.6x160.4x7.8;WT=175;DS=6.5;RS=1080x2340;OI=1;OV=8.1;CP=28;RM=4096;",
    "lld-l21": "->honor 9 lite",
    "lld-l31": "->honor 9 lite",
    "lld-tl10": "->honor 9 lite",
    "lld-al00": "->honor 9 lite",
    "lld-al10": "->honor 9 lite",
    "honor 9 lite": "DS=5.7;RS=1080x2160;SZ=71.9x151x7.6;WT=149;RE=2018.01;RM=4096;CP=42;OI=1;OV=8;",
    "stf-al10": "->honor 9",
    "stf-l09": "->honor 9",
    "stf-l09s": "->honor 9",
    "stf-tl10": "->honor 9",
    "honor 9": "RE=2017.06;SZ=70.9x147.3x7.5;WT=155;DS=5.15;RS=1080x1920;OI=1;OV=7.0;CP=221;RM=4096;",
    "moa-lx9n": "->honor 9a",
    "honor 9a": "DS=6.3;RS=720x1600;SZ=74.06x159.07x9.04;WT=185;RE=2020.06;RM=3072;CP=47;OI=1;OV=10;",
    "lld-al20": "->honor 9i",
    "lld-al30": "->honor 9i",
    "honor 9i": "RE=2018.07;SZ=71.8x149.2x7.7;WT=152;DS=5.84;RS=1080x2280;OI=1;OV=8.0;CP=42;RM=3072;",
    "tny-al00": "->honor magic 2",
    "tny-tl00": "->honor magic 2",
    "honor magic 2": "RE=2018.10;SZ=75.1x157.3x8.3;WT=206;DS=6.39;RS=1080x2340;OI=1;OV=9.0;CP=52;RM=6144;",
    "nts-al00": "->honor magic",
    "honor magic": "RE=2016.12;SZ=69.9x146.1x7.8;WT=145;DS=5.09;RS=1440x2560;OI=1;OV=6.0;CP=216;RM=4096;",
    "rvl-al09": "->honor note 10",
    "honor note 10": "RE=2017.07;SZ=85x177x7.7;WT=230;DS=6.95;RS=1080x2220;OI=1;OV=8.1;CP=50;RM=6144;",
    "vky-tl00": "->honor p10 plus",
    "honor p10 plus": "RE=2017.02;SZ=74.2x153.5x7;WT=165;DS=5.5;RS=1440x2560;OI=1;OV=7.0;CP=221;RM=4096;",
    "vog-al00": "->honor p30 pro",
    "honor p30 pro": "RE=2020.05;SZ=73.4x158x8.4;WT=192;DS=6.47;RS=1080x2340;OI=1;OV=10;CP=52;RM=6144;",
    "jdn-w09": "->honor pad 2",
    "jdn-al00": "->honor pad 2",
    "honor pad 2": "RE=2016.10;DS=8.0;RS=1920x1200;SZ=209.3x123x8.1;WT=340;OI=1;OV=6.0;CP=22;RM=3072;",
    "krj-w09": "->honor pad v6",
    "honor pad v6": "RE=2020.05;SZ=245.2x154.9x7.8;WT=480;DS=10.4;RS=2000x1200;OI=1;OV=10;CP=53;RM=6144;",
    "ksa-al10": "->honor play 3e",
    "honor play 3e": "RE=2019.09;SZ=70.8x147.1x8.5;WT=146;DS=5.71;RS=720x1520;OI=1;OV=9.0;CP=47;RM=2048;",
    "tnnh-an00": "->honor play 4",
    "honor play 4": "RE=2020.06;SZ=78.5x170x8.9;WT=213;DS=6.81;RS=1080x2400;OI=1;OV=10;CP=45;RM=8192;",
    "jat-tl00": "->honor play 8a",
    "honor play 8a": "RE=2019.01;SZ=73.5x156.3x8;WT=150;DS=6.09;RS=720x1560;OI=1;OV=9.0;CP=38;RM=3072;",
    "moa-al00": "->honor play 9a",
    "moa-al20": "->honor play 9a",
    "honor play 9a": "RE=2020.05;SZ=74.1x159.1x9;WT=185;DS=6.3;RS=720x1600;OI=1;OV=10;CP=38;RM=4096;",
    "pct-tl10": "->honor v20",
    "pct-al10": "->honor v20",
    "honor v20": "RE=2018.12;SZ=75.4x156.9x8.1;WT=180;DS=6.4;RS=1080x2310;OI=1;OV=9.0;CP=52;RM=6144;",
    "knt-al10": "->honor v8",
    "knt-al20": "->honor v8;RS=1440x2560;CP=225;",
    "knt-tl10": "->honor v8",
    "knt-ul10": "->honor v8",
    "honor v8": "RE=2016.05;SZ=77.6x157x7.8;WT=170;DS=5.7;RS=1080x1920;OI=1;OV=6.0;CP=216;RM=4096;",
    "jmm-al00": "->honor v9 play",
    "jmm-al10": "->honor v9 play",
    "jmm-tl10": "->honor v9 play",
    "honor v9 play": "DS=5.2;RS=720x1280;SZ=73.2x147.9x7.65;WT=145;RE=2017;OI=1;OV=7.0;RM=3072;CP=40;",
    "pct-l29": "->honor view 20",
    "honor view 20": "RE=2018.12;SZ=75.4x156.9x8.1;WT=180;DS=6.4;RS=1080x2310;OI=1;OV=9.0;CP=52;RM=6144;",
    "oxf-an10": "->honor view 30 pro",
    "honor view 30 pro": "RE=2019.11;SZ=75.8x162.7x8.8;WT=206;DS=6.57;RS=1080x2400;OI=1;OV=10;CP=54;RM=8192;",
    "oxf-an00": "->honor view 30",
    "honor view 30": "RE=2020.02;SZ=75.8x162.7x8.9;WT=213;DS=6.57;RS=1080x2400;OI=1;OV=10;CP=54;RM=6144;",
    "y6 (2019)": "RE=2019.03;SZ=73.5x156.3x8;WT=150;DS=6.09;RS=720x1560;OI=1;OV=9.0;CP=44;RM=2048;",
    "mrd-lx3": "->y6 (2019)",
    "mrd-lx1": "->y6 (2019)",
    "mrd-lx1n": "->y6 (2019)",
    "mrd-lx1f": "->y6 (2019)",
    "matepad pro": "RE=2019.11;SZ=246x159x7.2;WT=460;DS=10.8;RS=2560x1600;OI=1;OV=10;CP=327;RM=6144;",
    "mrx-al09": "->matepad pro",
    "mrx-w09": "->matepad pro"
  },
  "ipro": {
    "amber 5s pro": "DS=5.0;RS=540x960;RM=1024;CP=58;SM=2;OI=1;OV=8.1;",
    "amber 6": "DS=5.5;RS=480x960;RM=1024;SZ=70.5x147.5x9.4;WT=160;OI=1;OV=8.1;CP=8;SM=2;",
    "amber 7s": "DS=5.5;RS=480x800;SZ=70.5x147.5x9.4;WT=160;RM=1024;OI=1;OV=8.1;CP=58;SM=2;",
    "amber5s pro": "->amber 5s pro",
    "amber6": "->amber 6",
    "amber7s": "->amber 7s",
    "jade 7s": "DS=5.72;RS=480x960;SZ=73.5x153.8x9.5;RM=1024;CP=58;SM=2;",
    "jade 8s": "",
    "jade7s": "->jade 7s",
    "jade8s": "->jade 8s",
    "kylin 5.0": "",
    "kylin 5.0s": "",
    "kylin 5.5": "",
    "phoenix 5.0s": "->phonenix 50s",
    "phonenix 50s": "DS=5;RS=640x960;OI=1;OV=7.0;RM=1024;CP=5;",
    "phonenix50s": "->phonenix 50s",
    "zafiro": "DS=5.45;RS=480x960;SZ=73.5x153.8x9.5;RM=1024;CP=5;OI=1;OV=8.1;SM=2;"
  },
  "clarmin": {
    "c1": "RE=2018.07;RM=3072;DS=5;RS=720x1440;SM=2;CP=8;",
    "b5": "DS=5.5;RM=2048;RS=720x1280;OI=1;OV=7.0;CP=5;SZ=78.4x157.6x9.15;SM=2;",
    "b6": "OI=1;OV=6.0;RM=3072;DS=5.5;RS=1080x1920;SZ=76.7x154.3x9.8;WT=213;CP=118;"
  },
  "zync": {
    "cloud 605": "RE=2013.11;OI=1;OV=4.2;DS=6.5;RS=800x480;RM=512;",
    "cloud z5": "RE=2013.07;RM=512;SZ=140.4x80.9x10.2;WT=192;DS=5;RS=800x480;OI=1;OV=4.0;",
    "dual 7+": "",
    "dual 7i": "",
    "z1000 3d": "",
    "z1000": "",
    "z18": "",
    "z7i 3g": "",
    "z81": "",
    "z900 plus": "",
    "z909": "",
    "z930+": "",
    "z930": ""
  },
  "zyq": {
    "braw 3g": "RE=2016.03;SZ=83x160x14.5;WT=219;",
    "care 3g": "",
    "cheer 3g": "",
    "i7": "",
    "j77": "->q.dee",
    "q.boss p99": "",
    "q.dee r09": "",
    "q.dee": "",
    "q.dr09": "->q.dee r09",
    "q.good m9": "",
    "q.hi s1": "",
    "q.hi": "",
    "q.hot p7 3g": "",
    "q.hot": "",
    "q.mate r99": "",
    "q.me phone 7 3g": "",
    "q.me phone7 3g": "->q.me phone 7 3g",
    "q.me": "",
    "q.next b7": "",
    "q.next j2": "",
    "q.top x8": "",
    "q.top-x8": "->q.top x8",
    "q.up c5": "",
    "q.up": "",
    "q.you": "",
    "q2602": "->tv next",
    "q2623": "->win 3g",
    "q2624": "->cheer 3g",
    "q2626": "->care 3g",
    "q2688": "",
    "q2728": "->zone 3g",
    "q2729": "",
    "q3022": "",
    "q328 m9": "",
    "q328": "->speed 3g",
    "q3623": "->braw 3g",
    "q638": "->i7",
    "q668": "->tv i4",
    "q88": "",
    "speed 3g": "",
    "tv i4": "",
    "tv next": "",
    "win 3g": "",
    "zone 3g": ""
  },
  "engel": {
    "en1007q plus": "RE=2014.10;SZ=225x174x70;RM=1024;WT=700;",
    "en1007qplus": "->en1007q plus"
  },
  "tiphone": {
    "t67": "DS=2.2;RS=240x320;WT=105;SZ=58x106x13.4;RE=2009;SM=2;"
  },
  "ulefone": {
    "note 13p": "RE=2021.11;SZ=75.7x162.3x9.9;WT=198.5;DS=6.5;RS=1080x2400;OI=1;OV=11;CP=348;RM=4096;",
    "note 12p": "RE=2021.10;SZ=79.1x173.6x10.2;WT=237;DS=6.82;RS=720x1640;OI=1;OV=11;CP=109;RM=4096",
    "note 10p": "RE=2022.03;SZ=76.7x165.2x9.7;WT=203;DS=6.52;RS=720x1600;OI=1;OV=11;CP=374;RM=3072;",
    "note 6p": "RE=2021.10;SZ=73.4x155.4x8.5;WT=155;DS=6.1;RS=720x1560;OI=1;OV=11;CP=109;RM=2048;",
    "note 9p": "RE=2020.07;SZ=76.7x166x9.2;WT=188;DS=6.52;RS=720x1600;OI=1;OV=10;CP=171;RM=4096;",
    "note 11p": "RE=2021.01;SZ=77.2x166.9x10;WT=184;DS=6.55;RS=720x1600;OI=1;OV=11;CP=290;RM=8192;",
    "armor 2": "RE=2017.08;SZ=78.3x159x14.5;WT=270;DS=5.0;RS=1080x1920;OI=1;OV=7.0;CP=232;RM=6144;TT=81125",
    "armor 3": "RE=2018.09;SZ=79.2x164.8x18.14;WT=364;DS=5.7;RS=1080x2160;OI=1;OV=8.1;CP=63;RM=4096;TT=71229;"
  },
  "formuler": {
    "z8 pro": "RE=2019;OI=1;OV=7.1;SZ=105x105x22;RM=2048;CP=228;",
    "z8": "RE=2019;OI=1;OV=7.0;SZ=105x105x22;RM=2048;CP=228;"
  },
  "geotel": {
    "amigo": "DS=5.2;RS=720x1280;SZ=75x150.55x9.8;WT=160;RE=2017;OI=1;OV=7.0;RM=3072;CP=118;",
    "g1": "DS=5;RS=720x1280;SZ=80.3x156.6x19.3;WT=280;RE=2017;OI=1;OV=7.0;RM=2048;CP=4;",
    "note": "DS=5.5;RS=720x1280;SZ=76.8x152.9x8.2;WT=147;RE=2017;OI=1;OV=6.0;RM=3072;CP=86;"
  },
  "firefly mobile": {
    "intense power": "DS=5.5;RS=720x1280;CP=6;RM=2048;RE=2016.05;OI=1;OV=5.1;",
    "intense metal 2": "DS=5.5;RS=720x1280;OI=1;OV=6.0;",
    "intense xt": "DS=5.7;RS=480x960;RM=1024;OI=1;OV=8.1;RE=2018.10;SM=2;",
    "aurii amuse 3g": "",
    "aurii ultra": "",
    "aurii secret xr": "",
    "secret xr": "->aurii secret xr",
    "aurii delight": "",
    "aurii dream mini": "",
    "aurii dream one": "",
    "super sweet": "",
    "sweet mini": "",
    "aurii f8 premium": "",
    "aurii force": "",
    "aurii fusion": "",
    "aurii passion": "",
    "aurii secret lite": "",
    "aurii virtuoso": "",
    "aurii xcite": "DS=5.34;RS=480x960;OI=1;OV=8.1;RM=1024;RE=2018.11;SM=2;"
  },
  "panasonic": {
    "eluga ray 700": "RE=2017.09;SZ=75.4x153.8x8.9;WT=182;DS=5.5;RS=1080x1920;OI=1;OV=7.0;CP=118;RM=3072;",
    "lumix dmc-cm1": "DS=4.7;RS=1920x1080;SZ=135.4x68x21.1;WT=204;RE=2014.12;RM=2048;CP=139;OI=1;OV=4.4.2;",
    "dmc-cm1": "->lumix dmc-cm1",
    "p902i": "",
    "sv-mv100": "",
    "eluga a2": "",
    "eluga i2": "",
    "eluga note": "",
    "eluga ray x": "",
    "eluga turbo": "",
    "p55 novo 4g": "RE=2016.10;SZ=73.8x147.95x8.15;WT=155;DS=5.3;RS=720x1280;OI=1;OV=6.0;CP=2;RM=3072;",
    "t50": "",
    "toughpad fz-n1": "",
    "fz-n1": "->toughpad fz-n1",
    "toughpad fz-x1": "DS=5;RS=720x1280;SZ=86.4x165.1x30.5;WT=408;RE=2014.06;RM=2048;CP=138;OI=1;OV=4.2.2;",
    "fz-x1": "->toughpad fz-x1",
    "p-smart keitai": "",
    "p-01j": "->p-smart keitai",
    "toughpad fz-b2d": "",
    "fz-b2d": "->toughpad fz-b2d",
    "lumix phone p-02d": "",
    "p-02d": "->lumix phone p-02d",
    "eluga p": "",
    "p-03e": "->eluga p",
    "eluga live 10.1\"": "",
    "p-08d": "->eluga live 10.1\"",
    "diga m9031": "",
    "smart tv": "",
    "smart tv (2018)": "",
    "smart tv (2019)": "",
    "smart tv (2020)": "",
    "smart tv (2021)": "",
    "43d1270": "",
    "43d1200": "",
    "32d1270": "",
    "32d1280": "",
    "viera (2011)": "",
    "viera (2012)": "",
    "viera (2013)": "",
    "viera (2014)": "",
    "viera (2015)": "",
    "viera (2017)": "",
    "viera (2019)": "DS=42;RS=1920x1080;RE=2019;"
  },
  "oneplus": {
    "1": "->one",
    "2": "RE=2015.07;SZ=74.9x151.8x9.9;WT=175;DS=5.5;RS=1080x1920;OI=1;OV=5.1;CP=96;RM=3072;",
    "3": "RE=2016.07;SZ=74.7x152.7x7.4;WT=158;DS=5.5;RS=1080x1920;OI=1;OV=6.0.1;CP=173;RM=6144;",
    "5": "RE=2017.06;SZ=74.1x154.2x7.3;WT=153;DS=5.5;RS=1080x1920;OI=1;OV=7.1.1;CP=25;RM=6144;",
    "7": "RE=2019.05;SZ=74.8x157.7x8.2;WT=182;DS=6.41;RS=1080x2340;OI=1;OV=9.0;CP=234;RM=6144;",
    "8": "RE=2020.04;SZ=72.9x160.2x8;WT=180;DS=6.55;RS=1080x2400;OI=1;OV=10;CP=207;RM=8192;",
    "one a2005": "->2",
    "one a2003": "->2",
    "one a2001": "->2",
    "a3000": "->3",
    "a3003": "->3",
    "oneplus3": "->3",
    "a5000": "->5",
    "gm1903": "->7",
    "gm1900": "->7",
    "gm1901": "->7",
    "gm1905": "->7",
    "in2013": "->8",
    "in2017": "->8",
    "in2010": "->8",
    "in2011": "->8",
    "in2015": "->8",
    "3t": "RE=2016.10;SZ=74.7x152.7x7.4;WT=158;DS=5.5;RS=1080x1920;OI=1;OV=6.0.1;CP=173;RM=6144;",
    "a3010": "->3t",
    "5t": "RE=2017.11;SZ=75x156.1x7.3;WT=162;DS=6.01;RS=1080x2160;OI=1;OV=7.1.1;CP=25;RM=6144;",
    "a5010": "->5t",
    "6t": "RE=2018.10;SZ=74.8x157.5x8.2;WT=185;DS=6.41;RS=1080x2340;OI=1;OV=9.0;CP=198;RM=6144;",
    "a6013": "->6t",
    "a6010": "->6t",
    "7 pro": "RE=2019.05;SZ=75.9x162.6x8.8;WT=206;DS=6.67;RS=1440x3120;OI=1;OV=9.0;CP=234;RM=6144;",
    "gm1910": "->7 pro",
    "gm1913": "->7 pro",
    "gm1915": "->7 pro",
    "gm1917": "->7 pro",
    "gm1911": "->7 pro",
    "7t": "RE=2019.09;SZ=74.4x160.9x8.1;WT=190;DS=6.55;RS=1080x2400;OI=1;OV=10;CP=197;RM=8192;",
    "hd1901": "->7t",
    "hd1903": "->7t",
    "hd1900": "->7t",
    "7t pro": "RE=2019.10;SZ=75.9x162.6x8.8;WT=206;DS=6.67;RS=1440x3120;OI=1;OV=10;CP=197;RM=8192;",
    "hd1913": "->7t pro",
    "hd1910": "->7t pro",
    "hd1911": "->7t pro",
    "7t pro 5g": "RE=2019.05;SZ=75.9x162.6x8.8;WT=206;DS=6.67;RS=1440x3120;OI=1;OV=9.0;CP=234;RM=6144;",
    "gm1920": "->7t pro 5g",
    "hd1925": "->7t pro 5g",
    "8 pro": "RE=2020.04;SZ=74.4x165.3x8.5;WT=199;DS=6.78;RS=1440x3168;OI=1;OV=10;CP=207;RM=8192;",
    "in2020": "->8 pro",
    "in2023": "->8 pro",
    "in2021": "->8 pro",
    "in2025": "->8 pro",
    "8t": "RE=2020.10;SZ=74.1x160.7x8.4;WT=188;DS=6.55;RS=1080x2400;OI=1;OV=11;CP=207;RM=8192;",
    "kb2000": "->8t",
    "kb2001": "->8t",
    "kb2003": "->8t",
    "nord 5g": "RE=2020.07;SZ=73.3x158.3x8.2;WT=184;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=242;RM=6144;",
    "ac2003": "->nord 5g",
    "ac2001": "->nord 5g",
    "one": "RE=2014.04;SZ=75.9x152.9x8.9;WT=162;DS=5.5;RS=1080x1920;OI=1;OV=4.4.2;CP=243;RM=3072;",
    "a0001": "->one",
    "x": "RE=2015.10;SZ=69x140x6.9;WT=138;DS=5.0;RS=1080x1920;OI=1;OV=5.1.1;CP=243;RM=3072;",
    "one e1003": "->x",
    "one e1001": "->x",
    "one e1005": "->x",
    "clover": "DS=6.52;RS=720x1560;SM=2;OI=1;OV=10;CP=244;RM=4096;",
    "be2012": "->clover"
  },
  "utok": {
    "450d": "DS=4.5;RS=480x854;SZ=66.8x132x9.5;WT=140;RM=512;OI=1;OV=4.2.2;CP=24;",
    "451d": ""
  },
  "google": {
    "nexus 5": "RE=2013.10;SZ=69.2x137.9x8.6;WT=130;DS=4.95;RS=1080x1920;OI=1;OV=4.4;CP=94;RM=2048;",
    "nexus 5x": "RE=2015.09;SZ=72.6x147x7.9;WT=136;DS=5.2;RS=1080x1920;OI=1;OV=6.0;CP=157;RM=2048;",
    "nexus 7": "RE=2012.06;SZ=198.5x120x10.5;WT=340;DS=7.0;RS=1280x800;OI=1;OV=4.1.2;CP=137;RM=1024;",
    "nexus 6": "",
    "nexus 6p": "",
    "pixel 5": "",
    "pixel 4a (5g)": "",
    "galaxy nexus": "",
    "glass": "",
    "glass 1": "->glass",
    "nexus 4": "",
    "nexus one": "",
    "nexus s": "",
    "nexus s 4g": "->nexus s",
    "pixel": "",
    "pixel 2": "",
    "g011a": "->pixel 2",
    "pixel 2 xl": "",
    "2xl": "->pixel 2 xl",
    "pixel 3": "",
    "pixel 3 xl": "",
    "pixel 3a": "",
    "pixel 3a xl": "",
    "pixel 4": "",
    "pixel 4 xl": "",
    "pixel 4a": "",
    "pixel xl": "",
    "nexus 10": "",
    "nexus 7 2013": "->nexus 7",
    "nexus 9": "",
    "pixel c": "",
    "chromecast": "",
    "googletv": ""
  },
  "imo mobile": {
    "q3 plus": "DS=5;RS=480x960;SZ=65.8x141x9.1;WT=132;OI=1;OV=8.1;RM=1024;CP=83;RE=2018.09;",
    "s2": "DS=5;RS=720x1280;WT=171;SZ=72x142x8.2;OI=1;OV=8.1;RE=2018.09;RM=1024;CP=9;",
    "s80 hero": "",
    "q": "",
    "discovery ii": "",
    "feel a2": "",
    "q2": "",
    "q2 plus": "",
    "q8 clarity": "",
    "s50 light": "",
    "s67 blast": "",
    "s78 glory": "",
    "s87 raptor": "",
    "s88 discovery": "",
    "s89 miracle": "",
    "s98 champion": "",
    "s99 ocean": "",
    "tab x9": "",
    "tab y5": "DS=7;RS=480x800;RSZ=197x124x11.5;WT=352;RE=2013;SM=1;CP=246;RM=512;OI=1;OV=4.0.4;",
    "tab z6": "DS=7;RS=480x800;SZ=200x118x13.5;WT=390;RE=2012.09;SM=1;CP=245;RM=512;OI=1;OV=4.0.3;",
    "tab z7": "DS=7;RS=600x1024;SZ=198x124x11;WT=399;RE=2012.10;OI=1;OV=4.0.3;CP=180;RM=512;"
  },
  "motorola": {
    "v360": "RE=2005.01;SZ=47x90x24;WT=104;DS=1.9;RS=176x220;",
    "mb886": "RE=2012.07;SZ=69.9x133.5x8.4;WT=140;DS=4.5;RS=720x1280;OI=1;OV=4.0.4;CP=14;RM=1024;",
    "moto g7": "RE=2019.02;SZ=75.3x157x8;WT=172;DS=6.2;RS=1080x2270;OI=1;OV=9.0;CP=64;RM=4096;",
    "moto g(7)": "->moto g7",
    "moto g6 play": "RE=2018.04;SZ=72.2x154.4x9;WT=175;DS=5.7;RS=720x1440;OI=1;OV=8.0;CP=43;RM=2048;",
    "moto g(6) play": "->moto g6 play",
    "moto g7 supra": "RE=2018.02;DS=6.2;SZ=76x159x9.3;WT=198;RS=720x1520;OI=1;OV=9.0;CP=64;RM=3072;",
    "moto g(7) supra": "->moto g7 supra",
    "moto g7 plus": "RE=2019.02;SZ=75.3x157x8.3;WT=176;DS=6.2;RS=1080x2270;OI=1;OV=9.0;CP=205;RM=4096;",
    "xt1965-6": "->moto g7 plus",
    "moto e5 play": "RE=2018.04;SZ=74x151x9;WT=150;DS=5.2;RS=720x1280;OI=1;OV=8.0;CP=281;RM=2048;",
    "razr 5g": "RE=2020.09;SZ=72.6x169.2x7.9;WT=192;DS=6.2;RS=876x2142;OI=1;OV=10;CP=242;RM=8192;",
    "razr": "RE=2019.11;SZ=72x172x6.9;WT=205;DS=6.2;RS=876x2142;OI=1;OV=9.0;CP=62;RM=6144;",
    "one macro": "RE=2019.10;SZ=75.4x157.6x9;WT=186;DS=6.2;RS=720x1520;OI=1;OV=9.0;CP=114;RM=4096;",
    "moto c plus": "RE=2017.05;SZ=72.3x144x10;WT=162;DS=5.0;RS=720x1280;OI=1;OV=7.0;CP=9;RM=1024;",
    "one 5g uw": "RE=2020.10;SZ=73.9x167.9x8.9;WT=209.8;DS=6.7;RS=1080x2520;OI=1;OV=10;CP=242;RM=4096;",
    "moto z4": "RE=2019.05;SZ=75x158x7.4;WT=165;DS=6.4;RS=1080x2340;OI=1;OV=9.0;CP=258;RM=4096;",
    "moto e6": "RE=2019.07;SZ=72.3x149.7x8.6;WT=159;DS=5.5;RS=720x1440;OI=1;OV=9.0;CP=282;RM=2048;",
    "moto g power": "RE=2020.02;SZ=75.8x159.9x9.6;WT=199;DS=6.4;RS=1080x2300;OI=1;OV=10;CP=283;RM=4096;",
    "moto g power (xt2041dl)": "->moto g power",
    "moto e5 go": "RE=2017.07;SZ=71.2x147.88x9.19;WT=145;DS=5.3;RS=480x960;OI=1;OV=8.0;CP=20;RM=1024;",
    "moto g8 power lite": "RE=2020.04;SZ=75.8x164.9x9.2;WT=200;DS=6.5;RS=720x1600;OI=1;OV=9.0;CP=38;RM=4096;",
    "moto g(8) power lite": "->moto g8 power lite",
    "moto e7 plus": "RE=2020.09;SZ=75.7x165.2x9.2;WT=200;DS=6.5;RS=720x1600;OI=1;OV=10;CP=244;RM=4096;",
    "moto e(7) plus": "->moto e7 plus",
    "moto e7": "RE=2020.09;SZ=75.7x164.9x8.9;WT=180;DS=6.5;RS=720x1600;OI=1;OV=10;CP=284;RM=2048;",
    "moto e(7)": "->moto e7",
    "moto g9 power": "RE=2020.09;SZ=76.8x172.1x9.7;WT=221;DS=6.8;RS=720x1640;OI=1;OV=10;CP=285;RM=4096;",
    "moto g(9) power": "->moto g9 power",
    "moto z2": "",
    "moto z (2)": "->moto z2",
    "moto c": "RE=2017.05;SZ=73.6x145.5x9;WT=154;DS=5.0;RS=480x854;OI=1;OV=7.0;CP=86;RM=1024;",
    "a953": "RE=2010.09;SZ=60.5x116.3x13.7;WT=169;DS=3.7;RS=480x854;OI=1;OV=2.2;CP=29;RM=512;",
    "motoa953": "->a953",
    "admiral": "RE=2011.09;SZ=62x119x12.2;WT=134;DS=3.1;RS=480x640;OI=1;OV=2.3.4;CP=286;RM=512;",
    "xt603": "->admiral",
    "atrix": "RE=2012.05;SZ=60.5x119.8x9.9;WT=129.7;DS=4.0;RS=480x854;OI=1;OV=2.3.7;CP=31;RM=512;",
    "xt682": "->atrix",
    "atrix tv": "->atrix",
    "xt687": "->atrix tv",
    "moto g": "RE=2014.09;SZ=70.7x141.5x11;WT=149;DS=5.0;RS=720x1280;OI=1;OV=4.4.4;CP=78;RM=1024;",
    "xt1028": "->moto g",
    "xt1032": "->moto g",
    "xt1033": "->moto g",
    "xt1031": "->moto g",
    "xt1034": "->moto g",
    "xt1068": "->moto g",
    "xt1069": "->moto g",
    "moto g3": "RE=2015.05;SZ=72.4x142.1x11.6;WT=155;DS=5.0;RS=720x1280;OI=1;OV=5.1.1;CP=17;RM=1024;",
    "motog3": "->moto g3",
    "moto g4": "RE=2016.05;SZ=76.6x153x9.8;WT=155;DS=5.5;RS=1080x1920;OI=1;OV=6.0.1;CP=65;RM=2048;",
    "moto g (4)": "->moto g4",
    "moto razr 40 ultra": "RE=2023.06;SZ=74x170.8x7;WT=184.5;DS=6.9;RS=1080x2640;OI=1;OV=13;CP=373;RM=8192;TT=975461;TG=3645;",
    "xt2321-2": "->moto razr 40 ultra",
    "xt2321-3": "->moto razr 40 ultra"
  },
  "nuvo": {
    "nd40": "RE=2014.12;RS=480x800;WT=130;SZ=63x128x9.6;DS=4;SM=2;OI=1;OV=4.4;",
    "blue nd40": "->nd40",
    "nd45": "RE=2015.05;WT=135;SZ=68x135x9.2;RS=480x854;DS=4.5;SM=2;OI=1;OV=4.4;",
    "green nd 45": "->nd45",
    "ns35": "RE=2015.05;WT=120;SZ=61.5x114.5x10.6;RS=320x480;DS=3.5;SM=2;OI=1;OV=4.4;"
  },
  "samsung": {
    "galaxy camera": "RE=2012.08;SZ=70.8x128.7x19.1;WT=300;DS=4.8;RS=720x1280;OI=1;OV=4.1;CP=233;RM=1024;",
    "ek-gc100": "->galaxy camera",
    "galaxy camera 2": "DS=4.8;RS=720x1280;SZ=71.2x132.5x19.3;WT=283;RE=2014;OI=1;OV=4.3;RM=2048;CP=233;",
    "ek-gc200": "->galaxy camera 2",
    "galaxy camera wifi only": "DS=4.8;RS=720x1280;WT=300;SZ=70.8x128.7x19.1;RM=1024;OI=1;OV=4.1;SM=0;",
    "ek-gc110": "->galaxy camera wifi only",
    "galaxy nx": "RS=720x1280;DS=4.8;CP=233;OI=1;OV=4.1;SZ=101.46x136.5x56;RE=2013;",
    "ek-gn120": "->galaxy nx",
    "e2152": "DS=2.0;RS=128x160;SZ=45.7x111.6x13.5;WT=80.0;RE=;RM=;CP=;OS=;",
    "gt-s3850": "RE=2011.05;SZ=60.6x109.9x11.7;WT=102;DS=3.2;RS=240x320;",
    "gt-i9500.": "->galaxy s4",
    "sch-i959": "->galaxy s4",
    "sc-04e": "->galaxy s4",
    "gt-i9505x": "->galaxy s4;",
    "gt-i9500": "->galaxy s4",
    "sgh-m919n": "->galaxy s4",
    "sch-i545": "->galaxy s4",
    "sm-s975l": "->galaxy s4",
    "sch-r970c": "->galaxy s4",
    "sgh-m919": "->galaxy s4",
    "gt-i9502": "->galaxy s4",
    "galaxy s4": "RE=2013.05;SZ=69.8x136.6x7.9;WT=130;DS=5.0;RS=1080x1920;OI=1;OV=4.2.2;CP=138;RM=2048;",
    "galaxy s5": "RE=2014.02;SZ=72.5x142x8.1;WT=145;DS=5.1;RS=1080x1920;OI=1;OV=4.4.2;CP=243;RM=2048;",
    "sm-g900w8": "->galaxy s5",
    "sm-g900f": "->galaxy s5",
    "sm-s902l": "->galaxy s5",
    "sm-g906l": "->galaxy s5",
    "sm-g906k": "->galaxy s5",
    "sm-s903vl": "->galaxy s5",
    "sm-g906s": "->galaxy s5",
    "scl23": "->galaxy s5",
    "sch-i500": "RE=2010.09;SZ=64.2x122.4x9.9;WT=116;DS=4;RS=480x800;SM=1;RM=512;CP=13;OI=1;OV=2.1;",
    "galaxy fold": "RE=2019.02;SZ=117.9x160.9x6.9;WT=263;DS=7.3;RS=1536x2152;OI=1;OV=9.0;CP=234;RM=12288;",
    "sm-f9000": "->galaxy fold",
    "sm-f900u": "->galaxy fold",
    "sm-f907b": "->galaxy fold",
    "sm-f907n": "->galaxy fold",
    "sm-f900f": "->galaxy fold",
    "sm-f900w": "->galaxy fold",
    "scv44": "->galaxy fold",
    "galaxy grand": "RE=2012.12;SZ=76.9x143.5x9.6;WT=162;DS=5.0;RS=480x800;OI=1;OV=4.1.2;CP=235;RM=1024;",
    "gt-i9080": "->galaxy grand",
    "gt-i9128i": "->galaxy grand",
    "galaxy grand 2": "RE=2013.09;SZ=75.3x146.8x8.9;WT=163;DS=5.25;RS=720x1280;OI=1;OV=4.3;CP=78;RM=1536;",
    "sm-g710": "->galaxy grand 2",
    "sm-g7105": "->galaxy grand 2",
    "sm-g7102": "->galaxy grand 2",
    "sm-g710l": "->galaxy grand 2",
    "sm-g7106": "->galaxy grand 2",
    "sm-g7108": "->galaxy grand 2",
    "sm-g7109": "->galaxy grand 2",
    "sm-g7102t": "->galaxy grand 2",
    "sm-g7105h": "->galaxy grand 2",
    "sm-g7105l": "->galaxy grand 2",
    "sm-g710k": "->galaxy grand 2",
    "sm-g710s": "->galaxy grand 2",
    "galaxy grand duos": "RE=2012.12;SZ=76.9x143.5x9.6;WT=162;DS=5.0;RS=480x800;OI=1;OV=4.1.2;CP=235;RM=1024;",
    "gt-i9082": "->galaxy grand duos",
    "gt-i9082l": "->galaxy grand duos",
    "galaxy grand neo": "RE=2014.01;SZ=77.1x143.7x9.6;WT=163;DS=5.01;RS=480x800;OI=1;OV=4.2;CP=236;RM=1024;",
    "gt-i9060": "->galaxy grand neo",
    "galaxy grand neo duos": "DS=5;RS=480x800;SZ=77.10x143.70x9.6;WT=166;CP=236;RM=1024;RE=2014.05;OI=1;OV=4.2;SM=2;",
    "gt-i9063t": "->galaxy grand neo duos",
    "galaxy gran neo duos tv": "->galaxy grand neo duos",
    "galaxy grand neo+": "DS=5;RS=480x800;SZ=77.10x143.7x9.6;WT=165;CP=236;RM=1024;OI=1;OV=4.2;RE=2014.05;SM=1;",
    "gt-i9168": "->galaxy grand neo+",
    "galaxy grand prime": "RE=2014.09;SZ=72.1x144.8x8.6;WT=156;DS=5.0;RS=540x960;OI=1;OV=4.4.4;CP=17;RM=1024;",
    "sm-g5308w": "->galaxy grand prime",
    "sm-g530bt": "->galaxy grand prime",
    "sm-g530m": "->galaxy grand prime",
    "sm-g530mu": "->galaxy grand prime",
    "sm-g5306w": "->galaxy grand prime",
    "sm-s920l": "->galaxy grand prime",
    "sm-g530h": "->galaxy grand prime",
    "sm-g530f": "->galaxy grand prime",
    "sm-g530az": "->galaxy grand prime",
    "sm-g530t": "->galaxy grand prime",
    "sm-g530w": "->galaxy grand prime",
    "sm-g530r7": "->galaxy grand prime",
    "sm-g5309w": "->galaxy grand prime",
    "sm-g530p": "->galaxy grand prime",
    "sm-g530a": "->galaxy grand prime",
    "sm-g530r4": "->galaxy grand prime",
    "sm-g531f": "->galaxy grand prime",
    "sm-g531h": "->galaxy grand prime",
    "galaxy tab a 7.0\" wifi": "RE=2016.05;SZ=186.9x108.8x8.7;WT=283;DS=7.0;RS=1280x800;OI=1;OV=5.1.1;CP=17;RM=1536;SM=0;",
    "sm-t280": "->galaxy tab a 7.0\" wifi",
    "galaxy tab a 10.1\" (2019)": "RE=2019.02;SZ=245.2x149.4x7.5;WT=469;DS=10.1;RS=1920x1200;OI=1;OV=9.0;CP=237;RM=2048;",
    "sm-t515": "->galaxy tab a 10.1\" (2019)",
    "galaxy a11": "RE=2020.05;SZ=76.3x161.4x8;WT=177;DS=6.4;RS=720x1560;OI=1;OV=10;CP=214;RM=2048;",
    "sm-a115m": "->galaxy a11",
    "sm-a115f": "->galaxy a11",
    "sm-a115a": "->galaxy a11",
    "sm-a115ap": "->galaxy a11",
    "sm-a115az": "->galaxy a11",
    "sm-a115u": "->galaxy a11",
    "sm-a115u1": "->galaxy a11",
    "sm-a115w": "->galaxy a11",
    "sm-a127f": "->galaxy a12;RE=2021.08;CP=250;OI=1;OV=11;",
    "galaxy a12": "RE=2020.10;SZ=75.8x164x8.9;WT=205;DS=6.5;RS=720x1600;OI=1;OV=10;CP=38;RM=3072;",
    "sm-a125f": "->galaxy a12",
    "sm-a125m": "->galaxy a12",
    "galaxy grand prime plus": "RE=2016.10;SZ=72.1x144.8x8.9;WT=160;DS=5.0;RS=540x960;OI=1;OV=6.0;CP=69;RM=1536;",
    "sm-g532f": "->galaxy grand prime plus",
    "galaxy grand prime ve duos": "RE=2014.09;SZ=72.1x144.8x8.6;WT=156;DS=5.0;RS=540x960;OI=1;OV=4.4.4;CP=238;RM=1024;SM=2;",
    "sm-g531y": "->galaxy grand prime ve duos",
    "sm-g531bt": "->galaxy grand prime ve duos",
    "sm-g531m": "->galaxy grand prime ve duos",
    "galaxy j2 prime": "RE=2016.10;SZ=72.1x144.8x8.9;WT=160;DS=5.0;RS=540x960;OI=1;OV=6.0;CP=69;RM=1536;",
    "sm-g532m": "->galaxy j2 prime",
    "sm-g532g": "->galaxy j2 prime",
    "galaxy j2 prime (tv)": "->galaxy j2 prime;SM=2;",
    "sm-g532mt": "->galaxy j2 prime (tv)",
    "galaxy mega 2": "RE=2014.09;SZ=84.9x163.6x8.6;WT=194;DS=6.0;RS=720x1280;OI=1;OV=4.4.3;CP=239;RM=1536;",
    "sm-g750f": "->galaxy mega 2",
    "sm-g750h": "->galaxy mega 2",
    "sm-g7508q": "->galaxy mega 2;CP=17;",
    "sm-g750a": "->galaxy mega 2",
    "sm-g7509": "->galaxy mega 2",
    "galaxy mega 5.8": "RE=2013.04;SZ=82.4x162.6x9;WT=182;DS=5.8;RS=540x960;CP=235;OI=1;OV=4.2.2;RM=1536;SM=2;",
    "gt-i9152": "->galaxy mega 5.8",
    "gt-i9158": "->galaxy mega 5.8",
    "galaxy mega 6.3": "RE=2013.04;SZ=88x167.6x8;WT=199;DS=6.3;RS=720x1280;OI=1;OV=4.2.2;CP=78;RM=1536;",
    "gt-i9205": "->galaxy mega 6.3",
    "gt-i9200": "->galaxy mega 6.3",
    "galaxy note": "RE=2011.09;SZ=83x146.9x9.7;WT=178;DS=5.3;RS=800x1280;OI=1;OV=2.3.5;CP=240;RM=1024;",
    "gt-n7000": "->galaxy note",
    "galaxy note 10": "RE=2019.08;SZ=71.8x151x7.9;WT=168;DS=6.3;RS=1080x2280;OI=1;OV=9.0;CP=67;RM=8192;",
    "sm-n970f": "->galaxy note 10",
    "sm-n970u": "->galaxy note 10",
    "sm-n970x": "->galaxy note 10",
    "sm-n9700": "->galaxy note 10",
    "sm-n970u1": "->galaxy note 10",
    "sm-n970w": "->galaxy note 10",
    "sm-n971n": "->galaxy note 10",
    "galaxy note 10 lite": "RE=2020.01;SZ=76.1x163.7x8.7;WT=199;DS=6.7;RS=1080x2400;OI=1;OV=10;CP=241;RM=6144;",
    "sm-n770x": "->galaxy note 10 lite",
    "sm-n770f": "->galaxy note 10 lite",
    "galaxy note 10+": "RE=2019.08;SZ=77.2x162.3x7.9;WT=196;DS=6.8;RS=1440x3040;OI=1;OV=9.0;CP=67;RM=12288;",
    "sm-n9750": "->galaxy note 10+",
    "sm-n975f": "->galaxy note 10+",
    "sm-n975u": "->galaxy note 10+",
    "sm-n975u1": "->galaxy note 10+",
    "sm-n975w": "->galaxy note 10+",
    "sm-n976v": "->galaxy note 10+",
    "sc-01m": "->galaxy note 10+",
    "sm-n975x": "->galaxy note 10+",
    "sm-n975xu": "->galaxy note 10+",
    "sm-n9760": "->galaxy note 10+",
    "sm-n976n": "->galaxy note 10+",
    "sm-n976u": "->galaxy note 10+",
    "sm-n976q": "->galaxy note 10+",
    "scv45": "->galaxy note 10+",
    "galaxy note 7": "RE=2016.08;SZ=73.9x153.5x7.9;WT=169;DS=5.7;RS=1440x2560;OI=1;OV=6.0.1;CP=270;RM=4096;",
    "sm-n930f": "->galaxy note 7",
    "sm-n930s": "->galaxy note 7",
    "sm-n930t": "->galaxy note 7",
    "sm-n9300": "->galaxy note 7",
    "sm-n930v": "->galaxy note 7",
    "sm-n930p": "->galaxy note 7",
    "scv34": "->galaxy note 7",
    "sm-n930x": "->galaxy note 7",
    "sm-n930k": "->galaxy note 7",
    "sm-n930a": "->galaxy note 7",
    "sm-n930l": "->galaxy note 7",
    "sm-n930u": "->galaxy note 7",
    "sm-n930r4": "->galaxy note 7",
    "sm-n930r6": "->galaxy note 7",
    "sm-n930r7": "->galaxy note 7",
    "sm-n930w8": "->galaxy note 7",
    "galaxy a10": "RE=2019.02;SZ=75.6x155.6x7.9;WT=168;DS=6.2;RS=720x1520;OI=1;OV=9.0;CP=248;RM=2048;",
    "sm-a105n": "->galaxy a10",
    "sm-a105f": "->galaxy a10",
    "sm-a105fn": "->galaxy a10",
    "galaxy a10e": "DS=5.8;RS=720x1560;SZ=69.6x147.3x8.4;WT=141;RE=2019.06;RM=2048;CP=248;OI=1;OV=10;",
    "sm-s102dl": "->galaxy a10e",
    "sm-a102n": "->galaxy a10e",
    "sm-a102u": "->galaxy a10e",
    "sm-a102w": "->galaxy a10e",
    "sm-a107m": "->galaxy a10s",
    "sm-a107f": "->galaxy a10s",
    "galaxy a10s": "RE=2019.08;SZ=75.8x156.9x7.8;WT=168;DS=6.2;RS=720x1520;OI=1;OV=9;CP=171;RM=2048;",
    "galaxy note 20 5g": "RE=2020.08;SZ=75.2x161.6x8.3;WT=192;DS=6.7;RS=1080x2400;OI=1;OV=10;CP=249;RM=8192;",
    "sm-n9810": "->galaxy note 20 5g",
    "sm-n981b": "->galaxy note 20 5g",
    "sm-n981n": "->galaxy note 20 5g",
    "sm-n981u1": "->galaxy note 20 5g",
    "sm-n981u": "->galaxy note 20 5g",
    "sm-n981w": "->galaxy note 20 5g",
    "galaxy a20": "RE=2019.05;SZ=74.7x158.4x7.8;WT=169;DS=6.4;RS=720x1560;OI=1;OV=9.0;CP=248;RM=3072;",
    "sc-02m": "->galaxy a20",
    "sm-a205u": "->galaxy a20",
    "scv46": "->galaxy a20",
    "sm-a205u1": "->galaxy a20",
    "sm-a205w": "->galaxy a20",
    "sm-a205yn": "->galaxy a20",
    "sm-a205s": "->galaxy a20",
    "scv46-u": "->galaxy a20",
    "scv46-j": "->galaxy a20",
    "galaxy a20e": "RE=2019.04;SZ=69.7x147.4x8.4;WT=141;DS=5.8;RS=720x1560;OI=1;OV=9.0;CP=248;RM=3072;",
    "sm-a202f": "->galaxy a20e",
    "galaxy a20s": "RE=2019.09;SZ=77.5x163.3x8;WT=183;DS=6.5;RS=720x1560;OI=1;OV=9.0;CP=214;RM=2048;",
    "sm-a2070": "->galaxy a20s",
    "sm-a207m": "->galaxy a20s",
    "sm-a207f": "->galaxy a20s",
    "galaxy a21": "RE=2020.04;SZ=76.7x167.8x8.1;WT=193;DS=6.5;RS=720x1600;OI=1;OV=10;CP=38;RM=3072;",
    "sm-a215u": "->galaxy a21",
    "sm-a215u1": "->galaxy a21",
    "sm-a215w": "->galaxy a21",
    "sm-a215dl": "->galaxy a21",
    "scv49": "->galaxy a21",
    "sc-42a": "->galaxy a21",
    "galaxy a21s": "RE=2020.05;SZ=75.3x163.7x8.9;WT=192;DS=6.5;RS=720x1600;OI=1;OV=10;CP=250;RM=2048;",
    "sm-a217f": "->galaxy a21s",
    "sm-a217n": "->galaxy a21s",
    "galaxy a3 (2015)": "RE=2014.10;SZ=65.5x130.1x6.9;WT=110.3;DS=4.5;RS=540x960;OI=1;OV=4.4.4;CP=17;RM=1024;",
    "sm-a3009": "->galaxy a3 (2015)",
    "sm-a300yz": "->galaxy a3 (2015)",
    "sm-a3000": "->galaxy a3 (2015)",
    "sm-a300xu": "->galaxy a3 (2015)",
    "sm-a300h": "->galaxy a3 (2015)",
    "sm-a300y": "->galaxy a3 (2015)",
    "sm-a300xz": "->galaxy a3 (2015)",
    "sm-a300x": "->galaxy a3 (2015)",
    "galaxy a3 (2016)": "RE=2015.12;SZ=65.2x134.5x7.3;WT=132;DS=4.7;RS=720x1280;OI=1;OV=5.1.1;CP=251;RM=1536;",
    "sm-a310x": "->galaxy a3 (2016)",
    "sm-a310m": "->galaxy a3 (2016)",
    "sm-a310f": "->galaxy a3 (2016)",
    "sm-a310n0": "->galaxy a3 (2016)",
    "sm-a310y": "->galaxy a3 (2016)",
    "galaxy a3 (2017)": "RE=2017.01;SZ=66.2x135.4x7.9;WT=138;DS=4.7;RS=720x1280;OI=1;OV=6.0.1;CP=252;RM=2048;",
    "sm-a320y": "->galaxy a3 (2017)",
    "sm-a320x": "->galaxy a3 (2017)",
    "sm-a320fl": "->galaxy a3 (2017)",
    "galaxy a30": "RE=2019.02;SZ=74.7x158.5x7.7;WT=165;DS=6.4;RS=1080x2340;OI=1;OV=9.0;CP=237;RM=3072;",
    "scv43": "->galaxy a30",
    "sm-a305n": "->galaxy a30",
    "scv43-j": "->galaxy a30",
    "scv43-u": "->galaxy a30",
    "galaxy a30s": "RE=2019.08;SZ=74.7x158.5x7.8;WT=169;DS=6.4;RS=720x1560;OI=1;OV=9.0;CP=237;RM=3072;",
    "sm-a307g": "->galaxy a30s",
    "galaxy a31": "RE=2020.05;SZ=73.1x159.3x8.6;WT=185;DS=6.4;RS=1080x2400;OI=1;OV=10;CP=253;RM=4096;",
    "sm-a315g": "->galaxy a31",
    "sm-a315f": "->galaxy a31",
    "sm-a315n": "->galaxy a31",
    "galaxy a40": "RE=2019.05;SZ=69.2x144.4x7.9;WT=140;DS=5.9;RS=1080x2340;OI=1;OV=9.0;CP=237;RM=4096;",
    "sm-a405fm": "->galaxy a40",
    "sm-a405s": "->galaxy a40",
    "galaxy a40s": "RE=2019.04;SZ=75.1x159.1x8.5;WT=174;DS=6.4;RS=720x1560;OI=1;OV=9.0;CP=237;RM=4096;",
    "sm-a3051": "->galaxy a40s",
    "sm-a3050": "->galaxy a40s",
    "sm-a3058": "->galaxy a40s",
    "galaxy a41": "RE=2020.05;SZ=69.8x149.9x7.9;WT=152;DS=6.1;RS=1080x2400;OI=1;OV=10;CP=253;RM=4096;",
    "sm-a415f": "->galaxy a41",
    "sc-41a": "->galaxy a41",
    "scv48": "->galaxy a41",
    "galaxy a5": "RE=2014.10;SZ=69.7x139.3x6.7;WT=123;DS=5.0;RS=720x1280;OI=1;OV=4.4.4;CP=17;RM=2048;",
    "sm-a500y": "->galaxy a5",
    "sm-a500l": "->galaxy a5",
    "sm-a5009": "->galaxy a5",
    "sm-a500": "->galaxy a5",
    "sm-a500w": "->galaxy a5",
    "sm-a500s": "->galaxy a5",
    "sm-a500yz": "->galaxy a5",
    "galaxy a5 (2015)": "->galaxy a5;",
    "sm-a500x": "->galaxy a5 (2015)",
    "galaxy a5 (2016)": "RE=2015.12;SZ=71x144.8x7.3;WT=155;DS=5.2;RS=1080x1920;OI=1;OV=5.1.1;CP=254;RM=2048;",
    "sm-a510m": "->galaxy a5 (2016)",
    "sm-a5100": "->galaxy a5 (2016)",
    "sm-a510x": "->galaxy a5 (2016)",
    "sm-a510s": "->galaxy a5 (2016)",
    "sm-a510y": "->galaxy a5 (2016)",
    "sm-a510l": "->galaxy a5 (2016)",
    "sm-a510k": "->galaxy a5 (2016)",
    "sm-a5108": "->galaxy a5 (2016)",
    "galaxy a5 (2017)": "RE=2017.01;SZ=71.4x146.1x7.9;WT=157;DS=5.2;RS=1080x1920;OI=1;OV=6.0.1;CP=255;RM=3072;",
    "sm-a520x": "->galaxy a5 (2017)",
    "sm-a520k": "->galaxy a5 (2017)",
    "sm-a520l": "->galaxy a5 (2017)",
    "sm-a520f": "->galaxy a5 (2017)",
    "sm-a520s": "->galaxy a5 (2017)",
    "sm-a520w": "->galaxy a5 (2017)",
    "galaxy a5 duos": "RE=2014.10;SZ=69.7x139.3x6.7;WT=123;DS=5.0;RS=720x1280;OI=1;OV=4.4.4;CP=17;RM=2048;SM=2;",
    "sm-a500k": "->galaxy a5 duos",
    "sm-a500g": "->galaxy a5 duos",
    "galaxy a50": "RE=2019.02;SZ=74.7x158.5x7.7;WT=166;DS=6.4;RS=1080x2340;OI=1;OV=9.0;CP=256;RM=4096;",
    "sm-505fn": "->galaxy a50",
    "sm-a505u": "->galaxy a50",
    "sm-a505x": "->galaxy a50",
    "sm-a505n": "->galaxy a50",
    "sm-a505u1": "->galaxy a50",
    "sm-a505w": "->galaxy a50",
    "sm-a505yn": "->galaxy a50",
    "sm-s506dl": "->galaxy a50",
    "galaxy a50s": "RE=2019.08;SZ=74.5x158.5x7.7;WT=169;DS=6.4;RS=1080x2340;OI=1;OV=9.0;CP=257;RM=4096;",
    "sm-a5070": "->galaxy a50s",
    "sm-a507fn": "->galaxy a50s",
    "galaxy a51": "RE=2019.12;SZ=73.6x158.5x7.9;WT=172;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=257;RM=4096;",
    "sm-a515f": "->galaxy a51",
    "sm-a515w": "->galaxy a51",
    "sm-s515dl": "->galaxy a51",
    "sm-a515u": "->galaxy a51",
    "galaxy a51 5g": "RE=2020.08;SZ=73.4x158.8x8.6;WT=188.8;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=242;RM=6144;",
    "scg07": "->galaxy a51 5g",
    "sm-a5160": "->galaxy a51 5g",
    "sm-a516b": "->galaxy a51 5g",
    "sm-a516n": "->galaxy a51 5g",
    "sm-a516u": "->galaxy a51 5g",
    "sm-a516u1": "->galaxy a51 5g",
    "sm-a516v": "->galaxy a51 5g",
    "sc-54a": "->galaxy a51 5g",
    "galaxy a6": "RE=2018.05;SZ=70.8x149.9x7.7;WT=162;DS=5.6;RS=720x1480;OI=1;OV=8.0;CP=252;RM=3072;",
    "sm-a600p": "->galaxy a6",
    "sm-a600a": "->galaxy a6",
    "sm-a600n": "->galaxy a6",
    "sm-a600t": "->galaxy a6",
    "sm-a600fn": "->galaxy a6",
    "sm-a600g": "->galaxy a6",
    "sm-a600t1": "->galaxy a6",
    "sm-a600x": "->galaxy a6",
    "sm-a600az": "->galaxy a6",
    "sm-a600gn": "->galaxy a6",
    "sm-a600f": "->galaxy a6",
    "sm-a600u": "->galaxy a6",
    "galaxy a6+": "RE=2018.05;SZ=75.7x160.2x7.9;WT=186;DS=6.0;RS=1080x2220;OI=1;OV=8.0;CP=214;RM=3072;",
    "sm-a605gn": "->galaxy a6+",
    "sm-a605x": "->galaxy a6+",
    "sm-a6050": "->galaxy a6+",
    "sm-a6058": "->galaxy a6+",
    "sm-a605g": "->galaxy a6+",
    "sm-a605f": "->galaxy a6+",
    "sm-a605fn": "->galaxy a6+",
    "galaxy a60": "RE=2019.04;SZ=73.9x155.3x7.9;WT=168;DS=6.3;RS=1080x2340;OI=1;OV=9.0;CP=258;RM=6144;",
    "sm-a6060": "->galaxy a60",
    "sm-a606y": "->galaxy a60",
    "galaxy a6s (2018)": "RE=2018.10;SZ=76.5x156.3x8.4;WT=183;DS=6.0;RS=1080x2160;OI=1;OV=8.0;CP=61;RM=6144;",
    "sm-g6200": "->galaxy a6s (2018)",
    "galaxy a7": "RE=2015.01;SZ=76.2x151x6.3;WT=141;DS=5.5;RS=1080x1920;OI=1;OV=4.4.4;CP=22;RM=2048;",
    "sm-a7009": "->galaxy a7",
    "sm-a700yd": "->galaxy a7",
    "sm-a700x": "->galaxy a7",
    "sm-a7000": "->galaxy a7",
    "sm-a700h": "->galaxy a7",
    "galaxy a7 (2016)": "RE=2015.12;SZ=74.1x151.5x7.3;WT=172;DS=5.5;RS=1080x1920;OI=1;OV=5.1.1;CP=254;RM=3072;",
    "sm-a7108": "->galaxy a7 (2016)",
    "sm-a710x": "->galaxy a7 (2016)",
    "sm-a710l": "->galaxy a7 (2016)",
    "sm-a710f": "->galaxy a7 (2016)",
    "sm-a710s": "->galaxy a7 (2016)",
    "sm-a710k": "->galaxy a7 (2016)",
    "galaxy a7 (2017)": "RE=2017.01;SZ=77.6x156.8x7.9;WT=186;DS=5.7;RS=1080x1920;OI=1;OV=6.0.1;CP=255;RM=3072;",
    "sm-a720x": "->galaxy a7 (2017)",
    "sm-a720f": "->galaxy a7 (2017)",
    "sm-a720s": "->galaxy a7 (2017)",
    "galaxy a7 (2018)": "RE=2018.09;SZ=76.8x159.8x7.5;WT=168;DS=6.0;RS=1080x2220;OI=1;OV=8.0;CP=259;RM=4096;",
    "sm-a750g": "->galaxy a7 (2018)",
    "sm-a750x": "->galaxy a7 (2018)",
    "sm-a750gn": "->galaxy a7 (2018)",
    "sm-a750f": "->galaxy a7 (2018)",
    "sm-a750n": "->galaxy a7 (2018)",
    "sm-a750c": "->galaxy a7 (2018)",
    "galaxy a70": "RE=2019.05;SZ=76.7x164.3x7.9;WT=183;DS=6.7;RS=1080x2400;OI=1;OV=9.0;CP=258;RM=6144;",
    "sm-a705fn": "->galaxy a70",
    "sm-a705x": "->galaxy a70",
    "sm-a705f": "->galaxy a70",
    "sm-a705mn": "->galaxy a70",
    "galaxy a70s": "RE=2019.09;SZ=76.7x164.3x7.9;WT=187;DS=6.7;RS=1080x2400;OI=1;OV=9.0;CP=258;RM=6144;",
    "sm-a7070": "->galaxy a70s",
    "sm-a707f": "->galaxy a70s",
    "galaxy a71": "RE=2019.12;SZ=76x163.6x7.7;WT=179;DS=6.7;RS=1080x2400;OI=1;OV=10;CP=261;RM=6144;",
    "sm-a715w": "->galaxy a71",
    "sm-a715f": "->galaxy a71;SM=2;CP=260;",
    "sm-a71": "->galaxy a71",
    "galaxy a71 5g": "RE=2020.04;SZ=75.5x162.5x8.1;WT=185;DS=6.7;RS=1080x2400;OI=1;OV=10;CP=262;RM=6144;",
    "sm-a716u": "->galaxy a71 5g",
    "sm-a7160": "->galaxy a71 5g",
    "sm-a716b": "->galaxy a71 5g",
    "sm-a716u1": "->galaxy a71 5g",
    "sm-a716v": "->galaxy a71 5g",
    "galaxy a8": "RE=2015.08;SZ=76.8x158x5.9;WT=151;DS=5.7;RS=1080x1920;OI=1;OV=5.1;CP=22;RM=2048;",
    "sm-a800i": "->galaxy a8",
    "sm-a8000": "->galaxy a8",
    "sm-a800s": "->galaxy a8",
    "sm-a800f": "->galaxy a8",
    "sm-a800y": "->galaxy a8;CP=263;",
    "sm-a800x": "->galaxy a8",
    "scv32": "->galaxy a8",
    "sm-a530f": "->galaxy a8",
    "galaxy a8 (2016)": "RE=2016.09;SZ=76.8x156.6x7.2;WT=182;DS=5.7;RS=1080x1920;OI=1;OV=6.0.1;CP=264;RM=3072;",
    "sm-a810f": "->galaxy a8 (2016)",
    "sm-a810s": "->galaxy a8 (2016)",
    "sm-a810yz": "->galaxy a8 (2016)",
    "galaxy a8 (2018)": "RE=2017.12;SZ=70.6x149.2x8.4;WT=172;DS=5.6;RS=1080x2220;OI=1;OV=7.1.1;CP=259;RM=4096;",
    "sm-a530x": "->galaxy a8 (2018)",
    "sm-a530w": "->galaxy a8 (2018)",
    "sm-a530n": "->galaxy a8 (2018)",
    "sm-a530m": "->galaxy a8 (2018)",
    "galaxy a8 star": "RE=2018.06;SZ=77x162.4x7.6;WT=191;DS=6.3;RS=1080x2220;OI=1;OV=8.0;CP=61;RM=4096;",
    "sm-g885f": "->galaxy a8 star",
    "sm-g885y": "->galaxy a8 star",
    "sm-g885s": "->galaxy a8 star",
    "galaxy a8+ (2018)": "RE=2017.12;SZ=75.7x159.9x8.3;WT=191;DS=6.0;RS=1080x2220;OI=1;OV=7.1.1;CP=259;RM=4096;",
    "sm-a730f": "->galaxy a8+ (2018)",
    "sm-a730x": "->galaxy a8+ (2018)",
    "galaxy a80": "RE=2019.04;SZ=76.5x165.2x9.3;WT=220;DS=6.7;RS=1080x2400;OI=1;OV=9.0;CP=261;RM=8192;",
    "sm-a8050": "->galaxy a80",
    "sm-a805n": "->galaxy a80",
    "sm-a805x": "->galaxy a80",
    "sm-a805f": "->galaxy a80",
    "galaxy a8s": "RE=2018.12;SZ=74.9x158.4x7.4;WT=173;DS=6.4;RS=1080x2340;OI=1;OV=9.0;CP=62;RM=6144;",
    "sm-g8870": "->galaxy a8s",
    "sm-g887f": "->galaxy a8s",
    "sm-a8s": "->galaxy a8s",
    "galaxy a9": "RE=2015.12;SZ=80.9x161.7x7.4;WT=200;DS=6.0;RS=1080x1920;OI=1;OV=5.1.1;CP=115;RM=3072;",
    "sm-a9000": "->galaxy a9",
    "sm-a900f": "->galaxy a9",
    "galaxy a9 (2018)": "RE=2018.10;SZ=77x162.5x7.8;WT=183;DS=6.3;RS=1080x2220;OI=1;OV=8.0;CP=61;RM=6144;",
    "sm-a920x": "->galaxy a9 (2018)",
    "sm-a920f": "->galaxy a9 (2018)",
    "sm-a9200": "->galaxy a9 (2018)",
    "sm-a920n": "->galaxy a9 (2018)",
    "galaxy a9 7": "",
    "sm-a9[7]": "->galaxy a9 7",
    "galaxy a9 pro": "RE=2016.05;SZ=80.9x161.7x7.9;WT=210;DS=6.0;RS=1080x1920;OI=1;OV=6.0.1;CP=115;RM=4096;",
    "sm-a9100": "->galaxy a9 pro",
    "sm-a910f": "->galaxy a9 pro",
    "sm-g887n": "->galaxy a9 pro",
    "galaxy a9 star": "RE=2018.06;SZ=77x162.4x7.6;WT=191;DS=6.3;RS=1080x2220;OI=1;OV=8.0;CP=61;RM=4096;",
    "sm-g8858": "->galaxy a9 star",
    "sm-g8850": "->galaxy a9 star",
    "galaxy a90": "RE=2019.09;SZ=76.4x164.8x8.4;WT=206;DS=6.7;RS=1080x2400;OI=1;OV=9.0;CP=234;RM=6144;",
    "sm-a908n": "->galaxy a90",
    "sm-a908b": "->galaxy a90",
    "galaxy ace": "RE=2011.01;SZ=59.9x112.4x11.5;WT=113;DS=3.5;RS=320x480;OI=1;OV=2.3;CP=145;RM=278;",
    "gt-s5830l": "->galaxy ace",
    "gt-s5830i": "->galaxy ace;CP=265;",
    "gt-s5830": "->galaxy ace",
    "galaxy ace 2": "RE=2012.02;SZ=62.2x118.3x10.5;WT=122;DS=3.8;RS=480x800;OI=1;OV=2.3;CP=266;RM=768;",
    "gt-i8160p-orange": "->galaxy ace 2",
    "gt-i8160": "->galaxy ace 2",
    "gt-i8160p": "->galaxy ace 2",
    "galaxy s21+ 5g": "RE=2021.01;SZ=75.6x161.5x7.8;WT=200;DS=6.7;RS=1080x2400;OI=1;OV=11;CP=267;RM=8192;",
    "sm-g9960": "->galaxy s21+ 5g",
    "sm-g996u1": "->galaxy s21+ 5g",
    "sm-g996w": "->galaxy s21+ 5g",
    "sm-g996b": "->galaxy s21+ 5g",
    "sm-g996n": "->galaxy s21+ 5g",
    "sm-g996u": "->galaxy s21+ 5g",
    "scg10": "->galaxy s21+ 5g",
    "galaxy note 20 ultra": "RE=2020.08;SZ=77.2x164.8x8.1;WT=208;DS=6.9;RS=1440x3088;OI=1;OV=10;CP=249;RM=8192;",
    "sm-n985f": "->galaxy note 20 ultra",
    "galaxy note 20 ultra 5g": "RE=2020.08;SZ=77.2x164.8x8.1;WT=208;DS=6.9;RS=1440x3088;OI=1;OV=10;CP=249;RM=12288;",
    "sm-n986b": "->galaxy note 20 ultra 5g",
    "sm-n986n": "->galaxy note 20 ultra 5g",
    "sm-n9860": "->galaxy note 20 ultra 5g",
    "scg06": "->galaxy note 20 ultra 5g",
    "sc-53a": "->galaxy note 20 ultra 5g",
    "galaxy c9 pro": "RE=2016.10;SZ=80.7x162.9x6.9;WT=189;DS=6.0;RS=1080x1920;OI=1;OV=6.0.1;CP=268;RM=6144;",
    "sm-c9008": "->galaxy c9 pro",
    "sm-c900f": "->galaxy c9 pro",
    "galaxy s10e": "RE=2019.02;SZ=69.9x142.2x7.9;WT=150;DS=5.8;RS=1080x2280;OI=1;OV=9.0;CP=269;RM=6144;",
    "sm-g9700": "->galaxy s10e",
    "sm-g970n": "->galaxy s10e",
    "sm-g9708": "->galaxy s10e",
    "sm-g970w": "->galaxy s10e",
    "sm-g970x": "->galaxy s10e",
    "galaxy s20": "RE=2020.02;SZ=69.1x151.7x7.9;WT=163;DS=6.2;RS=1440x3200;OI=1;OV=10;CP=249;RM=8192;",
    "sm-g980f": "->galaxy s20",
    "galaxy s20 5g": "RE=2020.02;SZ=69.1x151.7x7.9;WT=163;DS=6.2;RS=1440x3200;OI=1;OV=10;CP=249;RM=8192;",
    "sm-g981b": "->galaxy s20 5g",
    "scg01": "->galaxy s20 5g",
    "sm-g981v": "->galaxy s20 5g",
    "sc-51a": "->galaxy s20 5g",
    "sc51aa": "->galaxy s20 5g",
    "galaxy s21 ultra 5g": "RE=2021.01;SZ=75.6x165.1x8.9;WT=227;DS=6.8;RS=1440x3200;OI=1;OV=11;CP=267;RM=12288;",
    "sm-g9980": "->galaxy s21 ultra 5g",
    "sm-g998u": "->galaxy s21 ultra 5g",
    "sm-g998u1": "->galaxy s21 ultra 5g",
    "sm-g998w": "->galaxy s21 ultra 5g",
    "sm-g998b": "->galaxy s21 ultra 5g",
    "sm-g998n": "->galaxy s21 ultra 5g",
    "galaxy s21 5g": "RE=2021.01;SZ=71.2x151.7x7.9;WT=169;DS=6.2;RS=1080x2400;OI=1;OV=11;CP=267;RM=8192;",
    "sm-g9910": "->galaxy s21 5g",
    "sm-g991b": "->galaxy s21 5g",
    "sm-g991n": "->galaxy s21 5g",
    "sm-g991w": "->galaxy s21 5g",
    "sm-g991u1": "->galaxy s21 5g",
    "scg09": "->galaxy s21 5g",
    "galaxy a01": "RE=2019.12;SZ=70.9x146.2x8.3;WT=149;DS=5.7;RS=720x1520;OI=1;OV=10;CP=41;RM=2048;",
    "sm-a015a": "->galaxy a01",
    "sm-a015m": "->galaxy a01",
    "sm-a015t1": "->galaxy a01",
    "sm-a015u": "->galaxy a01",
    "sm-a015u1": "->galaxy a01",
    "sm-a015v": "->galaxy a01",
    "sm-s111dl": "->galaxy a01",
    "galaxy a01 core": "RE=2020.07;SZ=67.5x141.7x8.6;WT=150;DS=5.3;RS=720x1480;OI=1;OV=10;CP=8;RM=1024;",
    "sm-a013f": "->galaxy a01 core",
    "sm-a013g": "->galaxy a01 core",
    "galaxy a02s": "RE=2020.09;SZ=75.9x164.2x9.1;WT=196;DS=6.5;RS=720x1600;OI=1;OV=10;CP=214;RM=2048;",
    "sm-a025f": "->galaxy a02s",
    "sm-a025g": "->galaxy a02s",
    "sm-a025m": "->galaxy a02s",
    "sm-a022g": "->galaxy a02",
    "galaxy s20 fe 5g": "RE=2020.09;SZ=74.5x159.8x8.4;WT=190;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=141;RM=6144;",
    "sm-g7810": "->galaxy s20 fe 5g",
    "sm-g781b": "->galaxy s20 fe 5g",
    "sm-g781n": "->galaxy s20 fe 5g",
    "sm-g781u": "->galaxy s20 fe 5g",
    "sm-g781u1": "->galaxy s20 fe 5g",
    "sm-g781v": "->galaxy s20 fe 5g",
    "sm-g781w": "->galaxy s20 fe 5g",
    "galaxy s20 fe": "RE=2020.09;SZ=74.5x159.8x8.4;WT=190;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=249;RM=6144;",
    "sm-g780g": "->galaxy s20 fe;CP=207;",
    "sm-g780f": "->galaxy s20 fe;",
    "galaxy s4 zoom": "RE=2013.06;SZ=63.5x125.5x15.4;WT=208;DS=4.3;RS=540x960;OI=1;OV=4.2.2;CP=;RM=1536;",
    "sm-c105a": "->galaxy s4 zoom",
    "sm-c105": "->galaxy s4 zoom",
    "sm-c101": "->galaxy s4 zoom",
    "sm-c105l": "->galaxy s4 zoom",
    "galaxy s6": "RE=2015.05;SZ=70.6x143.5x6.9;WT=140;DS=5.1;RS=1440x2560;OI=1;OV=5.0.2;CP=264;RM=3072;",
    "sm-g920": "->galaxy s6",
    "sm-g920w8": "->galaxy s6",
    "sm-g920v": "->galaxy s6",
    "sm-g920x": "->galaxy s6",
    "sm-g920f": "->galaxy s6",
    "sm-g920i": "->galaxy s6",
    "sm-g920p": "->galaxy s6",
    "sm-g9200": "->galaxy s6",
    "sm-g920l": "->galaxy s6",
    "sm-s907vl": "->galaxy s6",
    "sm-g920k": "->galaxy s6",
    "sm-g920t": "->galaxy s6",
    "sm-g920r4": "->galaxy s6",
    "sc-05g": "->galaxy s6",
    "galaxy s6 edge": "RE=2015.05;SZ=70.1x142.1x7;WT=132;DS=5.1;RS=1440x2560;OI=1;OV=5.0.2;CP=264;RM=3072;",
    "sm-g925a": "->galaxy s6 edge",
    "sm-g9250": "->galaxy s6 edge",
    "sm-g925x": "->galaxy s6 edge",
    "sm-g925i": "->galaxy s6 edge",
    "sm-g925s": "->galaxy s6 edge",
    "sm-g925p": "->galaxy s6 edge",
    "sm-g925d": "->galaxy s6 edge",
    "sm-g925t": "->galaxy s6 edge",
    "sm-g925k": "->galaxy s6 edge",
    "scv31": "->galaxy s6 edge",
    "404sc": "->galaxy s6 edge",
    "sm-g925l": "->galaxy s6 edge",
    "galaxy s6 edge+": "RE=2015.08;SZ=75.8x154.4x6.9;WT=153;DS=5.7;RS=1440x2560;OI=1;OV=5.1.1;CP=264;RM=4096;",
    "sm-g928g": "->galaxy s6 edge+",
    "sm-g928p": "->galaxy s6 edge+",
    "sm-g928w8": "->galaxy s6 edge+",
    "sm-g928l": "->galaxy s6 edge+",
    "sm-g928x": "->galaxy s6 edge+",
    "sm-g928a": "->galaxy s6 edge+",
    "sm-g9287c": "->galaxy s6 edge+",
    "sm-g928i": "->galaxy s6 edge+",
    "sm-g928v": "->galaxy s6 edge+",
    "sm-g9280": "->galaxy s6 edge+",
    "sm-g928s": "->galaxy s6 edge+",
    "sm-g9287": "->galaxy s6 edge+",
    "sm-g928k": "->galaxy s6 edge+",
    "sm-g928r4": "->galaxy s6 edge+",
    "sm-g928t": "->galaxy s6 edge+",
    "galaxy s7": "RE=2016.02;SZ=69.6x142.4x7.9;WT=152;DS=5.1;RS=1440x2560;OI=1;OV=6.0;CP=270;RM=4096;",
    "sm-g930s": "->galaxy s7",
    "sm-g9300": "->galaxy s7",
    "sm-g930x": "->galaxy s7",
    "sm-g930v": "->galaxy s7",
    "sm-g930k": "->galaxy s7",
    "sm-g930l": "->galaxy s7",
    "sm-g9308": "->galaxy s7",
    "sm-g930": "->galaxy s7",
    "sm-g930r4": "->galaxy s7",
    "sm-g930r6": "->galaxy s7",
    "sm-g930r7": "->galaxy s7",
    "sm-g930vc": "->galaxy s7",
    "sm-g930vl": "->galaxy s7",
    "galaxy note 8": "RE=2017.08;SZ=74.8x162.5x8.6;WT=195;DS=6.3;RS=1440x2960;OI=1;OV=7.1.1;CP=271;RM=6144;",
    "sm-n950u1": "->galaxy note 8",
    "sm-n950f": "->galaxy note 8",
    "scv37": "->galaxy note 8",
    "sc-01k": "->galaxy note 8",
    "sm-n950u": "->galaxy note 8",
    "galaxy ace 4": "RE=2014.06;SZ=62.9x121.4x11;WT=130.3;DS=4.0;RS=480x800;OI=1;OV=4.4.2;RM=1024",
    "sm-g313hy": "->galaxy ace 4",
    "sm-g313mu": "->galaxy ace 4",
    "sm-g313ml": "->galaxy ace 4",
    "sm-g313my": "->galaxy ace 4",
    "sm-g313m": "->galaxy ace 4",
    "sm-g313f": "->galaxy ace 4",
    "galaxy ace 4 lite": "->galaxy ace 4",
    "sm-g313u": "->galaxy ace 4 lite",
    "sm-g313hz": "->galaxy ace 4 lite",
    "sm-g313hn": "->galaxy ace 4 lite",
    "sm-g313h": "->galaxy ace 4 lite",
    "galaxy ace 4 duos": "->galaxy ace 4;SM=2;",
    "sm-g316m": "->galaxy ace 4 duos",
    "galaxy ace 4 neo": "->galaxy ace 4 duos;",
    "sm-g318ml": "->galaxy ace 4 neo",
    "sm-g318h": "->galaxy ace 4 neo",
    "galaxy alpha": "RE=2014.08;SZ=65.5x132.4x6.7;WT=115;DS=4.7;RS=720x1280;OI=1;OV=4.4.4;CP=243;RM=2048;",
    "sm-g850a": "->galaxy alpha",
    "sm-g850m": "->galaxy alpha",
    "sm-g850y": "->galaxy alpha",
    "sm-g850k": "->galaxy alpha",
    "sm-g850l": "->galaxy alpha",
    "sm-g850s": "->galaxy alpha",
    "sm-g850w": "->galaxy alpha",
    "sm-g850x": "->galaxy alpha",
    "sm-g8508s": "->galaxy alpha",
    "galaxy c7 pro": "RE=2017.01;SZ=77.2x156.5x7;WT=172;DS=5.7;RS=1080x1920;OI=1;OV=6.0.1;CP=272;RM=4096;",
    "sm-c7018": "->galaxy c7 pro",
    "sm-c701f": "->galaxy c7 pro",
    "sm-c7010": "->galaxy c7 pro",
    "sm-c701x": "->galaxy c7 pro",
    "galaxy c7": "RE=2016.05;SZ=77.2x156.6x6.8;WT=169;DS=5.7;RS=1080x1920;OI=1;OV=6.0.1;CP=81;RM=4096;",
    "sm-c7000": "->galaxy c7",
    "sm-c700x": "->galaxy c7",
    "galaxy tab s7 11.0\" 5g": "RE=2020.08;SZ=253.8x165.3x6.3;DS=11;WT=502;RS=2560x1600;SM=1;OI=1;OV=10;RM=6144;CP=141;",
    "sm-t878u": "->galaxy tab s7 11.0\" 5g",
    "galaxy tab s7 11.0\" lte": "RE=2020.08;SZ=253.8x165.3x6.3;WT=498;DS=11.0;RS=2560x1600;OI=1;OV=10;CP=141;RM=6144;",
    "sm-t875": "->galaxy tab s7 11.0\" lte",
    "sm-t875n": "->galaxy tab s7 11.0\" lte",
    "galaxy tab s7+ 12.4\" wifi": "->galaxy tab s7+ 12.4\" 5g;SM=0;",
    "sm-t970": "->galaxy tab s7+ 12.4\" wifi",
    "galaxy tab s7+ 12.4\" 5g": "RE=2020.08;SZ=285x185x5.7;DS=12.4;WT=575;RS=2800x1752;OI=1;OV=10;CP=141;RM=6144;",
    "sm-t976b": "->galaxy tab s7+ 12.4\" 5g",
    "galaxy a52": "RE=2021.05;SZ=75.1x159.9x8.4;WT=189;DS=6.5;RS=1080x2400;OI=1;OV=11;CP=273;RM=4096;",
    "sm-a525f": "->galaxy a52",
    "galaxy a32": "RE=2021.02;SZ=73.6x158.9x8.4;WT=184;DS=6.4;RS=1080x2400;OI=1;OV=11;CP=226;RM=4096;",
    "sm-a325f": "->galaxy a32",
    "galaxy a32 5g": "RE=2021.01;SZ=76.1x164.2x9.1;WT=205;DS=6.5;RS=720x1600;OI=1;OV=11;CP=274;RM=4096;",
    "sm-a326b": "->galaxy a32 5g",
    "sm-a326br": "->galaxy a32 5g",
    "scg08": "->galaxy a32 5g",
    "galaxy a72": "RE=2021.05;SZ=77.4x165x8.4;WT=203;DS=6.7;RS=1080x2400;OI=1;OV=11;CP=273;RM=6144;",
    "sm-a725f": "->galaxy a72",
    "galaxy folder": "RE=2015.07;SZ=60.2x122x15.3;WT=155;DS=3.8;RS=480x800;OI=1;OV=5.1;RM=1536;",
    "sm-g155s": "->galaxy folder",
    "sm-g150nk": "->galaxy folder",
    "sm-g150n0": "->galaxy folder",
    "sm-g150nl": "->galaxy folder",
    "sm-g150ns": "->galaxy folder",
    "galaxy folder 2": "RE=2017.07;SZ=60.2x122x16.1;WT=165;DS=3.8;RS=480x800;OI=1;OV=6.0;CP=20;RM=2048;",
    "sm-g160n": "->galaxy folder 2",
    "sm-g1600": "->galaxy folder 2",
    "sm-g1650": "->galaxy folder 2",
    "sm-g165n": "->galaxy folder 2",
    "galaxy c5 pro": "RE=2017.05;SZ=71.4x145.7x7;WT=145;DS=5.2;RS=1080x1920;OI=1;OV=7.0;CP=272;RM=4096;",
    "sm-c5018": "->galaxy c5 pro",
    "sm-c5010": "->galaxy c5 pro",
    "galaxy c5": "RE=2016.05;SZ=72x145.9x6.7;WT=143;DS=5.2;RS=1080x1920;OI=1;OV=6.0.1;65;RM=4096;",
    "sm-c5000": "->galaxy c5",
    "sm-c500x": "->galaxy c5",
    "galaxy c8": "RE=2017.09;SZ=74.7x152.4x7.9;WT=180;DS=5.5;RS=1080x1920;OI=1;OV=7.1;CP=174;RM=4096;SM=2;",
    "sm-c7100": "->galaxy c8",
    "sm-c7108": "->galaxy c8",
    "galaxy m12": "RE=2021.02;SZ=75.9x164x9.7;WT=221;DS=6.5;RS=720x1600;OI=1;OV=11;CP=250;RM=4096",
    "sm-m127f": "->galaxy m12",
    "sm-m127g": "->galaxy m12",
    "galaxy z flip 5g": "RE=2020.07;SZ=73.6x167.3x7.2;WT=183;DS=6.7;RS=1080x2636;OI=1;OV=10;CP=141;RM=8192;",
    "sm-f7070": "->galaxy z flip 5g",
    "sm-f707b": "->galaxy z flip 5g",
    "sm-f707n": "->galaxy z flip 5g",
    "sm-f707u": "->galaxy z flip 5g",
    "sm-f707u1": "->galaxy z flip 5g",
    "sm-f707w": "->galaxy z flip 5g",
    "scg04": "->galaxy z flip 5g",
    "galaxy a42 5g": "RE=2020.12;SZ=75.9x164.4x8.6;WT=193;DS=6.6;RS=720x1600;OI=1;OV=10;CP=275;RM=4096",
    "sm-a4260": "->galaxy a42 5g",
    "sm-a426b": "->galaxy a42 5g",
    "sm-a426n": "->galaxy a42 5g",
    "sm-a430f": "->galaxy a4",
    "galaxy a90 5g": "RE=2019.09;SZ=76.4x164.8x8.4;WT=206;DS=6.7;RS=1080x2400;OI=1;OV=9.0;CP=234;RM=6144;",
    "sm-a9080": "->galaxy a90 5g",
    "galaxy m51": "RE=2020.08;SZ=76.3x163.9x9.5;WT=213;DS=6.7;RS=1080x2400;OI=1;OV=10;CP=260;RM=6144;",
    "sm-m515f": "->galaxy m51",
    "galaxy m20": "RE=2019.01;SZ=74.5x156.4x8.8;WT=186;DS=6.3;RS=1080x2340;OI=1;OV=8.1;CP=237;RM=3072;",
    "sm-m205n": "->galaxy m20",
    "sm-m205m": "->galaxy m20",
    "sm-m205f": "->galaxy m20",
    "sm-m205g": "->galaxy m20",
    "sm-m205fn": "->galaxy m20",
    "galaxy m11": "RE=2020.05;SZ=76.3x161.4x9;WT=197;DS=6.4;RS=720x1560;OI=1;OV=10;CP=214;RM=3072;",
    "sm-m115f": "->galaxy m11",
    "sm-m115m": "->galaxy m11",
    "galaxy m01 core": "RE=2020.07;SZ=67.5x141.7x8.6;WT=150;DS=5.3;RS=720x1480;OI=1;OV=10;CP=8;RM=1024;",
    "sm-m013f": "->galaxy m01 core",
    "galaxy m01s": "RE=2020.07;SZ=75.8x156.9x7.8;WT=168;DS=6.2;RS=720x1520;OI=1;OV=9.0;CP=47;RM=3072;",
    "sm-m017f": "->galaxy m01s",
    "galaxy m02s": "RE=2021.01;SZ=75.9x164.2x9.1;WT=196;DS=6.5;RS=720x1600;OI=1;OV=10;CP=214;RM=3072;",
    "sm-m025f": "->galaxy m02s",
    "sm-g9758": "->galaxy s10+",
    "galaxy s10+": "RE=2019.02;SZ=74.1x157.6x7.8;WT=175;DS=6.4;RS=1440x3040;OI=1;OV=9.0;CP=269;RM=8192;",
    "sm-g975n": "->galaxy s10+",
    "sm-g975x": "->galaxy s10+",
    "sc-04l": "->galaxy s10+",
    "scv42": "->galaxy s10+",
    "galaxy s20 ultra 5g": "RE=2020.02;SZ=76x166.9x8.8;WT=222;DS=6.9;RS=1440x3200;OI=1;OV=10;CP=249;RM=12288;",
    "sm-g988b": "->galaxy s20 ultra 5g",
    "sm-g9880": "->galaxy s20 ultra 5g",
    "scg03": "->galaxy s20 ultra 5g",
    "galaxy s20+ 5g": "RE=2020.02;SZ=73.7x161.9x7.8;WT=188;DS=6.7;RS=1440x3200;OI=1;OV=10;CP=249;RM=12288;",
    "sm-g986b": "->galaxy s20+ 5g",
    "sc-52a": "->galaxy s20+ 5g",
    "sm-g986n": "->galaxy s20+ 5g",
    "galaxy s20+": "RE=2020.02;SZ=73.7x161.9x7.8;WT=186;DS=6.7;RS=1440x3200;OI=1;OV=10;CP=249;RM=8192;",
    "sm-g985f": "->galaxy s20+",
    "scg02": "->galaxy s20+",
    "galaxy s10": "RE=2019.02;SZ=77.1x162.6x7.9;WT=198;DS=6.7;RS=1440x3040;OI=1;OV=9.0;CP=269;RM=8192;",
    "sm-g977t": "->galaxy s10",
    "sm-g973f": "->galaxy s10",
    "sm-g977p": "->galaxy s10",
    "sm-g977u": "->galaxy s10",
    "sc-03l": "->galaxy s10",
    "sm-g977b": "->galaxy s10",
    "sm-g977n": "->galaxy s10",
    "scv41": "->galaxy s10",
    "galaxy note 5": "RE=2015.08;SZ=76.1x153.2x7.6;WT=171;DS=5.7;RS=1440x2560;OI=1;OV=5.1.1;CP=264;RM=4096;",
    "sm-n920v": "->galaxy note 5",
    "sm-n920t": "->galaxy note 5",
    "sm-n920x": "->galaxy note 5",
    "sm-n920f": "->galaxy note 5",
    "sm-n920i": "->galaxy note 5",
    "sm-n920s": "->galaxy note 5",
    "sm-n920c": "->galaxy note 5",
    "sm-n920k": "->galaxy note 5",
    "sm-n920r7": "->galaxy note 5",
    "sm-n9200": "->galaxy note 5",
    "sm-n920l": "->galaxy note 5",
    "sm-n920g": "->galaxy note 5",
    "sm-n920r6": "->galaxy note 5",
    "sm-n920r4": "->galaxy note 5",
    "sm-n920w8": "->galaxy note 5",
    "sm-n920p": "->galaxy note 5",
    "galaxy note 5 duos": "->galaxy note 5;SM=2;",
    "sm-n9208": "->galaxy note 5 duos",
    "galaxy note 9": "RE=2018.08;SZ=76.4x161.9x8.8;WT=201;DS=6.4;RS=1440x2960;OI=1;OV=8.1;CP=241;RM=6144;",
    "scv40": "->galaxy note 9",
    "sc-01l": "->galaxy note 9",
    "sm-n960f": "->galaxy note 9",
    "sm-n960u": "->galaxy note 9",
    "sm-n960n": "->galaxy note 9",
    "galaxy note edge": "RE=2014.09;SZ=82.4x151.3x8.3;WT=174;DS=5.6;RS=1600x2560;OI=1;OV=4.4.4;CP=276;RM=3072",
    "sc-01g": "->galaxy note edge",
    "galaxy note fe": "->galaxy note fan edition",
    "galaxy note fan edition": "RE=2017.07;SZ=73.9x153.5x7.9;WT=167;DS=5.7;RS=1440x2560;OI=1;OV=7.1.1;CP=270;RM=4096;",
    "sm-n935f": "->galaxy note fan edition",
    "sm-n935s": "->galaxy note fan edition",
    "sm-n935l": "->galaxy note fan edition",
    "sm-n935k": "->galaxy note fan edition",
    "galaxy pocket 2": "RE=2014.09;SZ=60.8x109.9x11.7;WT=107;DS=3.3;RS=240x320;OI=1;OV=4.4.2;CP=277;RM=512;",
    "sm-g110h": "->galaxy pocket 2",
    "sm-g110m": "->galaxy pocket 2",
    "sm-g110b": "->galaxy pocket 2",
    "galaxy on5": "RE=2015.10;SZ=72.1x142.3x8.5;WT=149;DS=5.0;RS=720x1280;OI=1;OV=5.1;CP=278;RM=1536;",
    "sm-g5500": "->galaxy on5",
    "sm-g550t1": "->galaxy on5",
    "sm-g550t2": "->galaxy on5",
    "sm-g550fy": "->galaxy on5",
    "sm-g5510": "->galaxy on5",
    "sm-g5520": "->galaxy on5",
    "sm-s550tl": "->galaxy on5",
    "sm-g550t": "->galaxy on5",
    "galaxy on5 (2016)": "->galaxy on5;RE=2016.11;",
    "sm-g5528": "->galaxy on5 (2016)",
    "galaxy on7": "RE=2015.10;SZ=77.5x151.8x8.2;WT=172;DS=5.5;RS=720x1280;OI=1;OV=5.1;CP=17;RM=1536;",
    "sm-g6000": "->galaxy on7",
    "sm-g600fy": "->galaxy on7",
    "sm-g600f": "->galaxy on7",
    "galaxy on7 (2016)": "RE=2016.09;SZ=75x151.7x8;WT=167;DS=5.5;RS=1080x1920;OI=1;OV=6.0.1;CP=81;RM=3072;",
    "sm-g6100": "->galaxy on7 (2016)",
    "sm-g610k": "->galaxy on7 (2016)",
    "sm-g610l": "->galaxy on7 (2016)",
    "sm-g610s": "->galaxy on7 (2016)",
    "galaxy s4 mini": "RE=2013.05;SZ=61.3x124.6x8.9;WT=107;DS=4.3;RS=540x960;OI=1;OV=4.2.2;CP=32;RM=1536;",
    "gt-i9190": "->galaxy s4 mini",
    "sch-i435": "->galaxy s4 mini",
    "gt-i9192": "->galaxy s4 mini",
    "gt-i9195": "->galaxy s4 mini",
    "gt-i9195i": "->galaxy s4 mini",
    "galaxy s9+": "RE=2018.02;SZ=73.8x158.1x8.5;WT=189;DS=6.2;RS=1440x2960;OI=1;OV=8.0;CP=241;RM=6144;",
    "sc-03k": "->galaxy s9+",
    "scv39": "->galaxy s9+",
    "sm-g965f": "->galaxy s9+",
    "sm-g965n": "->galaxy s9+",
    "sm-g965x": "->galaxy s9+",
    "galaxy s9": "RE=2018.02;SZ=68.7x147.7x8.5;WT=163;DS=5.8;RS=1440x2960;OI=1;OV=8.0;CP=241;RM=4096;",
    "sm-g960": "->galaxy s9",
    "sc-02k": "->galaxy s9",
    "sm-g960x": "->galaxy s9",
    "sm-g960n": "->galaxy s9",
    "scv38": "->galaxy s9",
    "sm-g960f": "->galaxy s9",
    "galaxy xcover 4": "RE=2017.05;SZ=73.3x146.2x9.7;WT=172;DS=5.0;RS=720x1280;OI=1;OV=7.0;CP=279;RM=2048;",
    "sm-g390f": "->galaxy xcover 4",
    "sm-g390w": "->galaxy xcover 4",
    "sm-g390y": "->galaxy xcover 4",
    "galaxy young 2": "RE=2014.06;SZ=59.9x109.8x11.8;WT=108;DS=3.5;RS=320x480;CP=280;OI=1;OV=4.4.2;RM=512;",
    "sm-g130h": "->galaxy young 2",
    "sm-g130hn": "->galaxy young 2",
    "sm-g130m": "->galaxy young 2",
    "sm-g130u": "->galaxy young 2",
    "sm-g130e": "->galaxy young 2",
    "sm-g130bt": "->galaxy young 2",
    "galaxy note ii": "RE=2012.08;SZ=80.5x151.1x9.4;WT=183;DS=5.5;RS=720x1280;OI=1;OV=4.1.1;CP=233;RM=2048;",
    "sph-l900": "->galaxy note ii",
    "gt-n7100": "->galaxy note ii",
    "sch-i605": "->galaxy note ii",
    "sch-r950": "->galaxy note ii",
    "sch-n719": "->galaxy note ii",
    "n7100": "->galaxy note ii",
    "galaxy j5 (2015)": "RE=2015.06;SZ=71.8x142.1x7.9;WT=146;DS=5.0;RS=720x1280;OI=1;OV=5.1;CP=17;RM=1536;",
    "sm-j5008": "->galaxy j5 (2015)",
    "sm-j500n0": "->galaxy j5 (2015)",
    "sm-j5007": "->galaxy j5 (2015)",
    "sm-j500h": "->galaxy j5 (2015)",
    "sm-j500f": "->galaxy j5 (2015)",
    "galaxy j5 (2016)": "RE=2016.03;SZ=72.3x145.8x8.1;WT=159;DS=5.2;RS=720x1280;OI=1;OV=6.0.1;CP=17;RM=2048;",
    "sm-g510h": "->galaxy j5 (2016)",
    "sm-5108": "->galaxy j5 (2016)",
    "sm-j5108": "->galaxy j5 (2016)",
    "sm-j510g": "->galaxy j5 (2016)",
    "sm-j510mn": "->galaxy j5 (2016)",
    "sm-j510un": "->galaxy j5 (2016)",
    "sm-j510y": "->galaxy j5 (2016)",
    "sm-j510fn": "->galaxy j5 (2016)",
    "sm-j510h": "->galaxy j5 (2016)",
    "sm-j510k": "->galaxy j5 (2016)",
    "sm-j510l": "->galaxy j5 (2016)",
    "sm-j510gn": "->galaxy j5 (2016)",
    "sm-j510s": "->galaxy j5 (2016)",
    "galaxy j5 (2017)": "RE=2017.06;SZ=71.3x146.2x8;WT=160;DS=5.2;RS=720x1280;OI=1;OV=7.0;CP=252;RM=2048;",
    "sm-j530f": "->galaxy j5 (2017)",
    "sm-j530g": "->galaxy j5 (2017)",
    "galaxy j5 prime": "RE=2016.09;SZ=69.5x142.8x8.1;WT=143;DS=5.0;RS=720x1280;OI=1;OV=6.0.1;CP=279;RM=2048;",
    "sm-g570m": "->galaxy j5 prime",
    "sm-g570y": "->galaxy j5 prime",
    "sm-g570f": "->galaxy j5 prime",
    "sm-g5700": "->galaxy j5 prime",
    "galaxy j6": "RE=2018.05;SZ=70.2x149.3x8.2;WT=154;DS=5.6;RS=720x1480;OI=1;OV=8.0;CP=252;RM=2048;",
    "sm-j600g": "->galaxy j6",
    "sm-j600gf": "->galaxy j6",
    "sm-j600f": "->galaxy j6",
    "sm-j600n": "->galaxy j6",
    "sm-j600l": "->galaxy j6",
    "galaxy j6+": "RE=2018.09;SZ=76.9x161.4x7.9;WT=178;DS=6.0;RS=720x1480;OI=1;OV=8.1;CP=20;RM=3072;",
    "sm-j610f": "->galaxy j6+",
    "galaxy j7": "RE=2015.06;SZ=78.7x152.2x7.5;WT=171;DS=5.5;RS=720x1280;OI=1;OV=5.1;CP=22;RM=1536;",
    "sm-j7008": "->galaxy j7",
    "sm-j700k": "->galaxy j7",
    "sm-j700f": "->galaxy j7",
    "sm-j710mn": "->galaxy j7",
    "sm-j710f": "->galaxy j7",
    "galaxy j7 (2015)": "->galaxy j7",
    "sm-j700p": "->galaxy j7 (2015)",
    "galaxy j7 (2016)": "RE=2016.05;SZ=76x151.7x7.8;WT=170;DS=5.5;RS=720x1280;OI=1;OV=6.0.1;CP=252;RM=2048;",
    "sm-j700t": "->galaxy j7 (2016)",
    "sm-j7109": "->galaxy j7 (2016)",
    "sm-j700t1": "->galaxy j7 (2016)",
    "sm-j710gn": "->galaxy j7 (2016)",
    "galaxy j7 (2017)": "RE=2017.05;SZ=76.2x151.4x8.6;WT=167.3;DS=5.5;RS=720x1280;OI=1;OV=7.0.1;CP=204;RM=2048;",
    "sm-j727f": "->galaxy j7 (2017)",
    "sm-j727a": "->galaxy j7 (2017)",
    "sm-j727u": "->galaxy j7 (2017)",
    "sm-j727s": "->galaxy j7 (2017)",
    "sm-j727r4": "->galaxy j7 (2017)",
    "sm-j730k": "->galaxy j7 (2017)",
    "galaxy z fold 2 5g": "RE=2020.08;SZ=128.2x159.2x6.9;WT=282;DS=7.6;RS=1768x2208;OI=1;OV=10;CP=141;RM=12288;",
    "sm-f916b": "->galaxy z fold 2 5g",
    "sm-f916n": "->galaxy z fold 2 5g",
    "sm-f916q": "->galaxy z fold 2 5g",
    "sm-f916u": "->galaxy z fold 2 5g",
    "sm-f916u1": "->galaxy z fold 2 5g",
    "sm-f916w": "->galaxy z fold 2 5g",
    "scg05": "->galaxy z fold 2 5g",
    "sm-f9160": "->galaxy z fold 2 5g"
  },
  "intex": {
    "aqua v3g": "RE=2015.01;DS=3.5;RS=320x480;SZ=62x115.1x9.5;WT=104;RM=512;CP=277;OI=1;OV=4.4;",
    "aqua lions x1+": "DS=5.2;RS=720x1280;SZ=74.4x150x9;WT=170;RE=2017;OI=1;OV=7.0;RM=3072;CP=9;",
    "aqua lions x1": "DS=5.2;RS=720x1280;SZ=74.4x150x9;WT=170;RE=2017;OI=1;OV=7.0;RM=2018;CP=9;",
    "cloud c1": "DS=4;RS=480x800;SZ=64x125x11.2;WT=119;RE=2017;OI=1;OV=7.0;RM=1024;CP=21;",
    "aqua lions 3": "DS=5;RS=720x1280;SZ=72.2x146.5x10.1;WT=167;RE=2017;OI=1;OV=7.0;RM=2048;CP=9;",
    "im0318nd": "->aqua lions 3",
    "aqua star 4g": "DS=5;RS=720x1280;SZ=72.6x143x9;WT=150;RE=2015;OI=1;OV=5.1;RM=1024;CP=56",
    "aqua4g": "->aqua star 4g",
    "aqua a4": "DS=4;RS=480x800;SZ=64x125x11.2;WT=119;RE=2017;OI=1;OV=7.0;RM=1024;CP=360;",
    "aqua a4 plus": "DS=4.5;RS=480x854;SZ=68x136.4x11.1;WT=134;RE=2017;OI=1;OV=7.0;RM=1024;CP=21;",
    "aqua 3g": "DS=4;OI=1;OV=4.2;RM=512;RE=2014.06;RS=480x800;WT=105;SZ=65x125x10;CP=287;",
    "aqua active": "RE=2013.04;DS=3.5;RM=256;WT=122;SZ=49.70x115.30x13.80;OI=1;OV=2.3;RS=320x480;",
    "aqua.active": "->aqua active",
    "aqua glory": "RE=2013.01;DS=4;RM=256;CP=246;WT=114;SZ=67.3x114x11.8;OI=1;OV=2.3;RS=320x480;SM=2;",
    "aqua i-4+": "",
    "aqua marvel": "",
    "aqua star": "DS=5;RS=480x854;SZ=72.9x141x8;WT=130;RE=2014;OI=1;OV=4.4.2;RM=1024;CP=1;",
    "aqua style": "DS=4;RS=480x800;SZ=62.5x119.7x9.3;WT=110;RE=2014;OI=1;OV=4.4.2;RM=1024;CP=1;",
    "aqua sx": "",
    "aqua y2": "",
    "cloud fame 4g": "DS=4.5;RS=480x854;SZ=68x134.2x9;WT=120;RE=2016;OI=1;OV=6.0;RM=1024;CP=310;",
    "cloud x1": "",
    "cloud x11": "",
    "cloud x2": "",
    "cloud x4": "",
    "cloud x5": "",
    "cloud y2": "",
    "cloud y4": "",
    "aqua lions n1": "DS=4;RS=480x800;SZ=64.3x124.9x10.2;WT=112;RE=2017;OI=1;OV=7.0;RM=1024;CP=113;",
    "aqua lions e3": "DS=5;RS=720x1280;SZ=73x145x8;WT=154;RE=2018;OI=1;OV=7.0;RM=2048;CP=361;",
    "aqua lions 2": "DS=5;RS=480x854;SZ=72x143.8x9.8;WT=158;RE=2017;OI=1;OV=7.0;RM=1024;CP=21;",
    "aqua 4.5e": "RE=2014.12;SZ=68.9x133.4x8.9;WT=132;DS=4.5;RS=480x854;OI=1;OV=4.4.2;CP=288;RM=512;"
  },
  "irbis": {
    "tz22": "DS=10.1;RS=1024x600;SZ=261x163x11.6;WT=540;CP=163;RM=1024;OI=1;OV=5.1;RE=2016.04;",
    "tz72": "DS=7;RS=1024x600;SZ=192x120x11;WT=270;CP=74;RM=1024;OI=1;OV=5.1;RE=2016.05;",
    "tz85": "DS=8;RS=1280x800;WT=360;SZ=208x124x9;CP=163;RM=1024;OI=1;OV=5.1;RE=2016.06;",
    "tz13": "DS=10.1;RS=1024x600;SZ=259x149x11;WT=515;CP=163;RM=1024;OI=1;OV=5.1;RE=2016.07;",
    "tz711": "DS=7;RS=1024x600;WT=260;SZ=188.31x108.2x10.3;CP=6;RM=1024;OI=1;OV=9.0;SM=2;RE=2020.09;",
    "tz192": "DS=10.1;RS=1280x800;SZ=254x153x10.1;WT=527;OI=1;OV=6.0;CP=21;RM=2048;SM=2;RE=2017.06;",
    "tz191": "DS=10.1;RS=1280x800;SZ=241.6x170.5x10.2;WT=540;OI=1;OV=6.0;CP=21;RM=2048;SM=2;RE=2017.06;",
    "tz185": "DS=10.1;RS=1024x600;SZ=250.8x158.8x10.2;WT=530;OI=1;OV=6.0;CP=287;RM=1024;SM=2;RE=2017.02;",
    "tz174": "DS=10.1;RS=1024x600;SZ=250x159x10;WT=500;OI=1;OV=6.0;RM=1024;CP=287;RM=1024;SM=2;RE=2017.06;",
    "tz171": "DS=10.1;RS=1280x800;SZ=241.6x170.5x10.2;WT=540;OI=1;OV=6.0;CP=21;RM=1024;SM=2;RE=2016.11;",
    "tz104": "DS=10.1;RS=1024x600;SZ=251x159x10.2;WT=562;OI=1;OV=5.1;CP=21;RM=1024;SM=2;RE=2016.09;",
    "tz761": "DS=7;RS=1024x600;SZ=188.8x108.5x10.8;WT=260;OI=1;OV=6.0;CP=21;RM=512;SM=2;RE=2016.11;",
    "tz151": "DS=10.1;RS=1280x800;SZ=253.6x174x12.9;WT=615;OI=1;OV=8.1;CP=287;RM=1024;SM=2;RE=2019.06;",
    "tz791": "DS=7;RS=1280x800;SZ=186.78x109.6x9.95;WT=260;OI=1;OV=6.0;CP=21;RM=1024;SM=2;RE=2016.11;",
    "tz722": "DS=7;RS=1024x600;SZ=188x108.3x10.2;WT=263;OI=1;OV=8.1;CP=74;RM=1024;SM=2;RE=2018;"
  },
  "vivo": {
    "1601": "->v5",
    "1603": "->y55l",
    "1606": "->y53i",
    "1609": "->v5 lite",
    "1610": "->y55s",
    "1611": "->v5 plus",
    "1612": "->v5s",
    "1613": "->y25",
    "1707": "->y51 (2015)",
    "1713": "->v5s",
    "1714": "->y69",
    "1716": "->v7 plus",
    "1718": "->v7",
    "1719": "->y65",
    "1720": "->x20 plus",
    "1721": "->x20",
    "1723": "->v9",
    "1724": "->y71",
    "1725": "->x21",
    "1726": "->y83 pro",
    "1727": "->v9 youth",
    "1801": "->y71i",
    "1802": "->y83",
    "1803": "->y81",
    "1804": "->v11 pro",
    "1805": "->nex",
    "1806": "->v11i",
    "1807": "->y95",
    "1808": "->y81",
    "1811": "->y91",
    "1812": "->y81i",
    "1813": "->nex dual display",
    "1814": "->y93",
    "1815": "->y93",
    "1816": "->y91i",
    "1817": "->y91",
    "1818": "->v15 pro",
    "1819": "->v15",
    "1820": "->y91i;RE=2019.05;CP=171;",
    "1823": "->y90",
    "1850": "->v7 plus",
    "1851": "->v9 pro",
    "1901": "->y15",
    "1902": "->y17",
    "1904": "->y12",
    "1906": "->y11",
    "1907": "->v17 neo",
    "1908": "->y90",
    "1909": "->v17 pro",
    "1910": "->v17 pro",
    "1912": "->nex 3",
    "1915": "->y19",
    "1916": "->u10",
    "1917": "->z1x",
    "1918": "->z1 pro",
    "1919": "->v17",
    "1920": "->v17",
    "1921": "->u20",
    "1929": "->y1s",
    "1933": "->v19",
    "1935": "->y50",
    "1937": "->x50 lite",
    "1938": "->y30",
    "1939": "->y30",
    "1940": "->y12",
    "1951": "->z1 pro",
    "2004": "->x50",
    "2005": "->x50 5g",
    "2006": "->x50 pro",
    "2007": "->y12i",
    "2015": "->y1s",
    "2018": "->v20 pro",
    "iqoo z5": "RE=2021.09;SZ=76.7x164.7x8.5;WT=193;DS=6.67;RS=1080x2400;OI=1;OV=11;CP=359;RM=8192;",
    "v2148a": "->iqoo z5",
    "x flip": "RE=2023.04;SZ=75.3x166.4x7.8;WT=198;DS=6.74;RS=1080x2520;OI=1;OV=13;CP=373;RM=12288;TT=1060911;TG=3930;",
    "v2256a": "->x flip",
    "x fold 3 pro": "RE=2024.03;SZ=142.4x160x5.2;WT=236;DS=8.03;RS=2200x2480;OI=1;OV=14;CP=378;RM=12288;TT=2066322;TG=6791;",
    "v2337a": "->x fold 3 pro",
    "x fold 3": "RE=2024.03;SZ=142.7x160x4.7;WT=219;DS=8.03;RS=2200x2480;OI=1;OV=14;CP=377;RM=12288;TT=1650000;",
    "v2303a": "->x fold 3",
    "x fold 2": "RE=2023.04;SZ=143.4x161.3x6;WT=279;DS=8.03;RS=1916x2160;OI=1;OV=13;CP=377;RM=12288;TT=1314521;TG=4769;",
    "v2266a": "->x fold 2",
    "x fold+": "RE=2022.09;SZ=144.9x162x6.3;WT=311;DS=8.03;RS=1916x2160;OI=1;OV=12;CP=373;RM=12288;",
    "v2229a": "->x fold+",
    "x50 5g": "RE=2020.06;SZ=75.4x159.5x7.6;WT=174.5;DS=6.56;RS=1080x2376;OI=1;OV=10;CP=242;RM=8192;",
    "x90 pro": "RE=2022.11;SZ=74.5x164.1x9.3;WT=214.9;DS=6.78;RS=1260x2800;OI=1;OV=13;CP=376;RM=8192;TT=1228735;TG=4273;",
    "v2242a": "->x90 pro",
    "v2219": "->x90 pro",
    "x100": "RE=2023.11;SZ=75.2x164.1x8.5;WT=202;DS=6.78;RS=1260x2800;OI=1;OV=14;CP=375;RM=12288;TT=2098442;TG=6177",
    "v2309a": "->x100",
    "v2308": "->x100",
    "t1 5g": "RE=2022.02;SZ=75.8x164x8.3;WT=187;DS=6.58;RS=1080x2408;OI=1;OV=11;CP=358;RM=4096;A",
    "v2115a": "->t1 5g",
    "v2141": "->t1 5g",
    "v2150": "->t1 5g",
    "v2157": "->t1 5g",
    "iqoo 8 pro": "RE=2021.08;SZ=75.2x165x9.2;WT=205;DS=6.78;RS=1440x3200;OI=1;OV=11;CP=305;RM=8192;",
    "v2141a": "->iqoo 8 pro",
    "iqoo 8": "RE=2021.08;SZ=75.1x159x8.6;WT=200;DS=6.56;RS=1080x2376;OI=1;OV=11;CP=305;RM=8192;",
    "v2136a": "->iqoo 8",
    "s10": "RE=2021.07;SZ=73.7x158.2x7.3;WT=173;DS=6.44;RS=1080x2400;OI=1;OV=11;CP=306;RM=8192;",
    "v2121a": "->s10",
    "nex a": "RE=2018.06;SZ=77x162x8;WT=199;DS=6.59;RS=1080x2316;OI=1;OV=9.0;CP=62;RM=6144;",
    "nex s": "RE=2018.06;SZ=77x162x8;WT=199;DS=6.59;RS=1080x2316;OI=1;OV=8.1;CP=198;RM=8192;",
    "y53": "RE=2017.03;SZ=71.4x144.2x7.6;WT=137;DS=5.0;RS=540x960;OI=1;OV=6;CP=20;RM=2048;",
    "1606a": "->y53",
    "y1s": "RE=2020.11;SZ=75.1x155.1x8.3;WT=161;DS=6.22;RS=720x1520;OI=1;OV=10;CP=38;RM=2048;",
    "2015 21": "->y1s",
    "y53s": "RE=2021.06;SZ=75.3x164x8.5;WT=189;DS=6.58;RS=1080x2408;OI=1;OV=11;CP=307;RM=8192;",
    "v2058": "->y53s",
    "v2111a": "->y53s",
    "v2123a": "->y53s",
    "v5": "RE=2016.11;SZ=75.5x153.8x7.6;WT=154;DS=5.5;RS=720x1280;OI=1;OV=6.0;CP=40;RM=4096;",
    "y55": "RE=2016.10;SZ=72.9x147.9x7.5;WT=142;DS=5.2;RS=720x1280;OI=1;OV=6;CP=43;RM=2048;",
    "y55l": "->y55",
    "y55a": "->y55",
    "y53i": "RE=2018.04;SZ=71.4x144.2x7.6;WT=137;DS=5.0;RS=540x960;OI=1;OV=6;CP=20;RM=2048;",
    "v5 plus": "RE=2017.01;SZ=74x152.6x7.3;WT=158.6;DS=5.5;RS=1080x1920;OI=1;OV=6;CP=204;RM=4096;",
    "v5s": "RE=2017.04;SZ=75.5x153.8x7.6;WT=154;DS=5.5;RS=720x1280;OI=1;OV=6;CP=40;RM=4096;",
    "v5 lite": "RE=2017.01;SZ=75.5x153.8x7.6;WT=155;DS=5.5;RS=720x1280;OI=1;OV=6;CP=40;RM=3072;",
    "y55s": "RE=2017.02;SZ=72.9x147.9x7.5;WT=142;DS=5.2;RS=720x1280;OI=1;OV=6;CP=20;RM=2048;SM=2;",
    "x50": "RE=2020.07;SZ=75.4x159.5x7.6;WT=173;DS=6.56;RS=1080x2376;OI=1;OV=10;CP=261;RM=8192;",
    "v2005a": "->x50 pro",
    "x50 pro": "RE=2020.06;SZ=72.8x158.5x8;WT=181.5;DS=6.56;RS=1080x2376;OI=1;OV=10;CP=242;RM=8192;",
    "v2001a": "->x50",
    "1907 19": "->v17 neo",
    "v17 neo": "RE=2019.08;SZ=75.2x159.5x8.1;WT=179;DS=6.38;RS=1080x2340;OI=1;OV=9.0;CP=253;RM=6144",
    "v17 pro": "RE=2019.09;SZ=74.7x159x9.8;WT=201.8;DS=6.44;RS=1080x2400;OI=1;OV=9.0;CP=258;RM=8192;",
    "y19": "RE=2019.11;SZ=76.5x162.2x8.9;WT=193;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=253;RM=4096;",
    "z1x": "RE=2019.09;SZ=75.2x159.5x8.1;WT=189.6;DS=6.38;RS=1080x2340;OI=1;OV=9.0;CP=289;RM=6144;",
    "z1 pro": "RE=2019.07;SZ=77.3x162.4x8.9;WT=201;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=289;RM=4096",
    "v17": "RE=2019.11;SZ=74.2x159x8.5;WT=176;DS=6.44;RS=1080x2400;OI=1;OV=9.0;CP=258;RM=8192;",
    "u20": "RE=2019.11;SZ=76.5x162.2x8.9;WT=193;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=258;RM=4096;",
    "v19": "RE=2020.04;SZ=75x159.6x8.5;WT=186.5;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=289;RM=8192;",
    "v2034a": "->y30",
    "v2036a": "->y30",
    "y30": "RE=2020.05;SZ=76.5x162x9.1;WT=197;DS=6.47;RS=720x1560;OI=1;OV=10;CP=38;RM=4096;",
    "v1824a": "->iqoo",
    "v1824ba": "->iqoo",
    "iqoo": "RE=2019.03;SZ=75.2x157.7x8.5;WT=196;DS=6.41;RS=1080x2340;OI=1;OV=9.0;CP=234;RM=6144;",
    "v1955a": "->iqoo 3",
    "iqoo 3": "RE=2020.02;SZ=74.9x158.5x9.2;WT=214.5;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=207;RM=6144;",
    "v2024a": "->iqoo 5",
    "iqoo 5": "E=2020.08;SZ=75.6x160x8.3;WT=197;DS=6.56;RS=1080x2376;OI=1;OV=10;CP=207;RM=8192;",
    "v2025a": "->iqoo 5 pro",
    "iqoo 5 pro": "RE=2020.08;SZ=73.3x159.6x8.9;WT=198;DS=6.56;RS=1080x2376;OI=1;OV=10;CP=207;RM=8192;",
    "iqoo neo": "RE=2019.07;SZ=75.2x159.5x8.1;WT=198.5;DS=6.38;RS=1080x2340;OI=1;OV=9.0;CP=198;RM=6144;",
    "v1914a": "->iqoo neo",
    "iqoo neo 3": "RE=2020.04;SZ=75.6x163.7x8.9;WT=198.1;DS=6.57;RS=1080x2408;OI=1;OV=10;CP=207;RM=6144;",
    "v1981a": "->iqoo neo 3",
    "iqoo neo 855": "RE=2019.10;SZ=75.2x159.5x8.1;WT=198.5;DS=6.38;RS=1080x2340;OI=1;OV=9.0;CP=234;RM=6144;",
    "v1936a": "->iqoo neo 855",
    "v1936al": "->iqoo neo 855",
    "iqoo pro": "RE=2019.08;SZ=75.7x158.8x9.3;WT=215;DS=6.41;RS=1080x2340;OI=1;OV=9.0;CP=234;RM=8192;",
    "v1922a": "->iqoo pro",
    "iqoo pro 5g": "RE=2019.08;SZ=75.7x158.8x9.3;WT=217;DS=6.41;RS=1080x2340;OI=1;OV=9.0;CP=234;RM=8192;",
    "v1916a": "->iqoo pro 5g",
    "v15 pro": "RE=2019.02;SZ=74.7x157.3x8.2;WT=185;DS=6.39;RS=1080x2340;OI=1;OV=9.0;CP=258;RM=6144;",
    "y69": "RE=2017.08;SZ=75.5x154.6x7.7;WT=162.8;DS=5.5;RS=720x1280;OI=1;OV=7.0;CP=40;RM=3072;",
    "v7 plus": "RE=2017.09;SZ=75.8x155.9x7.7;WT=160;DS=5.99;RS=720x1440;OI=1;OV=7.1.2;CP=214;RM=4096;",
    "v7": "RE=2017.11;SZ=72.8x149.3x7.9;WT=139;DS=5.7;RS=720x1440;OI=1;OV=7.1.2;CP=214;RM=4096;",
    "y65": "RE=2017.10;SZ=75.8x153.6x7.5;WT=157;DS=5.5;RS=720x1280;OI=1;OV=7.1;CP=20;RM=3072;",
    "x20a": "->x20",
    "x20": "RE=2017.09;SZ=75.2x155.9x7.2;WT=159;DS=6.01;RS=1080x2160;OI=1;OV=7.1.1;CP=61;RM=4096;",
    "v9": "RE=2018.05;SZ=75.1x154.8x7.9;WT=150;DS=6.3;RS=1080x2280;OI=1;OV=8.1;CP=272;RM=4096;",
    "y71a": "->y71",
    "y71": "RE=2018.04;SZ=75.7x155.9x7.8;WT=150;DS=6.0;RS=720x1440;OI=1;OV=8.1;CP=20;RM=3072;",
    "x21": "RE=2018.05;SZ=74.8x154.5x7.4;WT=156.2;DS=6.28;RS=1080x2280;OI=1;OV=8.1;CP=61;RM=6144;",
    "x21a": "->x21",
    "y83 pro": "RE=2018.09;SZ=75.2x155.2x7.7;WT=152;DS=6.22;RS=720x1520;OI=1;OV=8.1;CP=171;RM=4096;",
    "v9 youth": "RE=2018.04;SZ=75.1x154.8x7.9;WT=150;DS=6.3;RS=1080x2280;OI=1;OV=8.1;CP=214;RM=4096;",
    "1801i": "->y71i",
    "y71i": "RE=2018.10;SZ=75.7x155.9x7.8;WT=150;DS=6.0;RS=720x1440;OI=1;OV=8.1;CP=20;RM=2048;",
    "1808i": "->y81",
    "y81": "RE=2018.06;SZ=75x155.1x7.8;WT=146.5;DS=6.22;RS=720x1520;OI=1;OV=8.1;CP=171;RM=3072;",
    "v11 pro": "RE=2018.09;SZ=75x157.9x7.9;WT=156;DS=6.41;RS=1080x2340;OI=1;OV=8.1;CP=61;RM=6144;",
    "nex": "RE=2018.06;SZ=77x162x8;WT=199;DS=6.59;RS=1080x2316;OI=1;OV=8.1;CP=198;RM=8192;",
    "v11i": "RE=2018.09;SZ=75.6x156x8.1;WT=164;DS=6.3;RS=1080x2280;OI=1;OV=8.1;CP=290;RM=6144;",
    "y95": "RE=2018.11;SZ=75.1x155.1x8.3;WT=163.5;DS=6.22;RS=720x1520;OI=1;OV=8.1;CP=41;RM=3072;",
    "y91": "RE=2018.11;SZ=75.1x155.1x8.3;WT=163.5;DS=6.22;RS=720x1520;OI=1;OV=8.1;CP=41;RM=2048;",
    "1812 19": "->y81i",
    "y81i": "RE=2018.10;SZ=75x155.1x7.8;WT=143;DS=6.22;RS=720x1520;OI=1;OV=8.1;CP=44;RM=2048;",
    "v1818t": "->y93",
    "v1818a": "->y93",
    "y93": "RE=2018.11;SZ=75.1x155.1x8.3;WT=163.5;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=41;RM=4096;",
    "1820 19": "->1820;",
    "y91i": "RE=2018.11;SZ=75.1x155.1x8.3;WT=163.5;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=41;RM=2048;",
    "v15": "RE=2019.03;SZ=75.9x161.2x8.5;WT=189.5;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=114;RM=6144;",
    "1908 19": "->y90",
    "y90": "RE=2019.07;SZ=75.1x155.1x8.3;WT=163.5;DS=6.22;RS=720x1520;OI=1;OV=8.1;CP=44;RM=2048;",
    "v9 pro": "RE=2018.06;SZ=75x154.8x7.9;WT=150;DS=6.3;RS=1080x2280;OI=1;OV=8.1;CP=61;RM=6144;",
    "y15": "RE=2019.05;SZ=76.8x159.4x8.9;WT=190.5;DS=6.35;RS=720x1544;OI=1;OV=9.0;CP=171;RM=4096;",
    "y17": "RE=2019.04;SZ=76.8x159.4x8.9;WT=190.5;DS=6.35;RS=720x1544;OI=1;OV=9.0;CP=38;RM=4096;",
    "y12": "RE=2019.06;SZ=76.8x159.4x8.9;WT=190.5;DS=6.35;RS=720x1544;OI=1;OV=9.0;CP=171;RM=3072;",
    "1906 20": "->y11",
    "y11": "RE=2019.10;SZ=76.8x159.4x8.9;WT=190.5;DS=6.35;RS=720x1544;OI=1;OV=9.0;CP=41;RM=2048;",
    "Y50": "RE=2020.04;SZ=76.5x162x9.1;WT=197;DS=6.53;RS=1080x2400;OI=1;OV=10;CP=283;RM=8192;",
    "v20 pro": "RE=2020.09;SZ=74.2x158.8x7.4;WT=170;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=242;RM=8192;",
    "iqoo u1": "RE=2020.07;SZ=76.6x162.1x8.5;WT=190;DS=6.53;RS=1080x2340;OI=1;OV=10;CP=273;RM=6144;",
    "v2023a": "->iqoo u1",
    "iqoo z1": "RE=2020.05;SZ=75.6x164x8.9;WT=195;DS=6.57;RS=1080x2408;OI=1;OV=10;CP=296;RM=6144;",
    "v1986a": "->iqoo z1",
    "iqoo z1x": "RE=2020.07;SZ=76.5x164.2x9.1;WT=199.5;DS=6.57;RS=1080x2408;OI=1;OV=10;CP=242;RM=6144;",
    "v2012a": "->iqoo z1x",
    "iqoo z5x": "RE=2019.05;SZ=77.3x162.4x8.9;WT=204.1;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=62;RM=4096;",
    "v1919a": "->iqoo z5x",
    "nex 3": "RE=2019.09;SZ=76.1x167.4x9.4;WT=217.3;DS=6.89;RS=1080x2256;OI=1;OV=9.0;CP=234;RM=8192;",
    "v1923a": "->nex 3",
    "nex 3 5g": "->nex 3;WT=218.5;",
    "v1924a": "->nex 3 5g",
    "nex 3s": "RE=2020.05;SZ=76.1x167.4x9.4;WT=219.5;DS=6.89;RS=1080x2256;OI=1;OV=10;CP=207;RM=8192;",
    "v1950a": "->nex 3s",
    "y20g": "RE=2021.01;SZ=76.3x164.4x8.4;WT=192.3;DS=6.51;RS=720x1600;OI=1;OV=10;CP=226;RM=4096;",
    "v2037": "->y20g",
    "s1": "RE=2019.05;SZ=76x162x8.5;WT=189.5;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=114;RM=4096;",
    "v1831a": "->s1",
    "v1831t": "->s1",
    "s1 pro": "RE=2019.05;SZ=74.7x157.3x8.2;WT=185;DS=6.39;RS=1080x2340;OI=1;OV=9.0;CP=258;RM=6144;",
    "v1832a": "->s1 pro",
    "v1832t": "->s1 pro",
    "s5": "RE=2019.11;SZ=73.9x157.9x8.6;WT=188;DS=6.44;RS=1080x2400;OI=1;OV=9.0;CP=289;RM=8192;",
    "v1932a": "->s5",
    "s6 5g": "RE=2020.05;SZ=74.7x161.2x8.7;WT=181;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=262;RM=6144;",
    "v1962a": "->s6 5g",
    "s7": "RE=2020.08;SZ=74.2x158.8x7.4;WT=170;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=242;RM=8192;",
    "v2020ca": "->s7",
    "v2020a": "->s7",
    "u3": "RE=2019.10;SZ=76.5x162.2x8.9;WT=193;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=258;RM=4096;",
    "v1941a": "->u3",
    "u3x": "RE=2019.09;SZ=76.8x159.4x8.9;WT=190.5;DS=6.35;RS=720x1544;OI=1;OV=9.0;CP=283;RM=3072;",
    "v1928a": "->u3x",
    "v1": "DS=5;RS=720x1280;SZ=71x143.3x6.78;WT=145;RE=2015.07;OI=1;OV=5.0;RM=2048;CP=17;",
    "x70": "RE=2021.09;SZ=75.4x160.1x7.6;WT=182;DS=6.56;RS=1080x2376;OI=1;OV=11;CP=324;RM=8192;",
    "v2133a": "->x70",
    "x1": "",
    "x20 plus": "RE=2017.09;SZ=80.1x165.3x7.5;WT=181.5;DS=6.43;RS=1080x2160;OI=1;OV=7.1.1;CP=61;RM=4096;",
    "x20plus": "->x20 plus",
    "x21s": "RE=2018.09;SZ=75x157.9x7.9;WT=156;DS=6.41;RS=1080x2340;OI=1;OV=8.1;CP=61;RM=6144;",
    "v1814a": "->x21s",
    "v1814t": "->x21s",
    "x23": "RE=2018.09;SZ=74.1x157.7x7.5;WT=160.5;DS=6.41;RS=1080x2340;OI=1;OV=8.1;CP=298;RM=8192;",
    "v1809t": "->x23",
    "v1816a": "->x23",
    "v1809a": "->x23",
    "v1816t": "->x23",
    "x27": "RE=2019.05;SZ=74.3x157.7x9;WT=188;DS=6.39;RS=1080x2340;OI=1;OV=9.0;CP=62;RM=8192;",
    "v1829t": "->x27",
    "v1838a": "->x27",
    "v1829a": "->x27",
    "v1838t": "->x27",
    "x27 pro": "RE=2019.04;SZ=74.6x165.7x9;WT=200;DS=6.7;RS=1080x2460;OI=1;OV=9.0;CP=62;RM=8192;",
    "v1836t": "->x27 pro",
    "v1836a": "->x27 pro",
    "x30": "RE=2019.12;SZ=74.1x158.5x8.8;WT=196.5;DS=6.44;RS=1080x2400;OI=1;OV=9.0;CP=262;RM=8192;",
    "v1938ct": "->x30",
    "x30 pro": "RE=2019.12;SZ=74.1x158.5x8.8;WT=198.5;DS=6.44;RS=1080x2400;OI=1;OV=9.0;CP=262;RM=8192;",
    "v1938t": "->x30 pro",
    "x3v": "RE=2015.01;SZ=71.0x143.3x6.8;WT=166;DS=5;RS=720x1280;OI=1;OV=4.4;CP=111;RM=2048;",
    "bbg-vivo x3v": "->x3v",
    "x50 pro plus": "RE=2020.06;SZ=73x158.5x8.8;WT=192.2;DS=6.56;RS=1080x2376;OI=1;OV=10;CP=207;RM=8192;",
    "v2011a": "->x50 pro plus",
    "x50 pro+": "->x50 pro plus",
    "x5max v": "RE=2014.12;SZ=78.0x153.9x4.8;WT=146;DS=5.5;RS=1080x1920;OI=1;OV=4.4;CP=22;RM=2048;",
    "viv-vivo x5max v": "->x5max v",
    "y3": "RE=2020.12;SZ=76.8x159.4x8.9;WT=190.5;DS=6.35;RS=720x1544;OI=1;OV=9.0;CP=38;RM=4096;",
    "v1901a": "->y3",
    "v1901t": "->y3",
    "y3 standard": "RE=2019.11;SZ=76.7x159.4x8.9;WT=191.5;DS=6.35;RS=720x1544;OI=1;OV=9.0;CP=41;RM=3072;",
    "v1930a": "->y3 standard",
    "y50": "RE=2020.04;SZ=76.5x162x9.1;WT=197;DS=6.53;RS=1080x2400;OI=1;OV=10;CP=283;RM=8192;",
    "v1965a": "->y50",
    "y5s": "RE=2019.11;SZ=76.5x162.2x8.9;WT=193;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=253;RM=6144;",
    "v1934a": "->y5s",
    "y70s": "RE=2020.05;SZ=76.6x162x8.5;WT=190;DS=6.53;RS=1080x2340;OI=1;OV=10;CP=299;RM=6144;",
    "v2002a": "->y70s",
    "y73": "DS=5.99;RS=720x1440;SZ=75.74x155.87x7.8;WT=150;RE=2018.09;OI=1;OV=8.1;RM=3072;CP=41;",
    "v1731ca": "->y73",
    "y7s": "DS=6.38;RS=1080x2340;SZ=75.23x159.53x8.13;WT=179;RE=2019.06;OI=1;OV=9.0;RM=6144;CP=253;",
    "v1913a": "->y7s",
    "v1913t": "->y7s",
    "y81s": "DS=6.22;RS=720x1520;SZ=75x155.06x7.77;WT=146;RE=2018.06;OI=1;OV=8.1;RM=3072;CP=171;",
    "v1732t": "->y81s",
    "v1732a": "->y81s",
    "v2080a": "->s7t",
    "s7t": "RE=2021.02;SZ=74.2x158.8x7.4;WT=169;DS=6.44;RS=1080x2400;OI=1;OV=11;CP=297;RM=8192;",
    "v21e": "RE=2021.04;SZ=74.37x161.24x7.38;WT=171;DS=6.44;RS=1080x2400;CP=273;RM=8192;OI=1;OV=11;SM=2",
    "v2061": "->v21e",
    "v21": "RE=2021.04;SZ=73.90x159.68x7.29;WT=176;DS=6.44;RS=1080x2404;CP=300;RM=8192;OI=1;OV=11;SM=2;",
    "v2066": "->v21",
    "v2108": "->v21",
    "y51": "RE=2020.12;SZ=75.3x163.9x8.4;WT=188;DS=6.58;RS=1080x2408;OI=1;OV=11;CP=283;RM=8192;",
    "v2030": "->y51",
    "v2035": "->y51",
    "y51a": "->y51",
    "y54s": "RE=2021.11;SZ=75.4x164.2x8.5;WT=188.4;DS=6.51;RS=720x1600;OI=1;OV=11;CP=302;RM=6144;",
    "v2045a": "->y54s",
    "y76s": "RE=2021.11;SZ=75x163.8x7.8;WT=175;DS=6.58;RS=1080x2408;OI=1;OV=11;CP=356;RM=8192;",
    "v2156a": "->y76s",
    "y20 (2021)": "RE=2020.12;SZ=76.3x164.4x8.4;WT=192;DS=6.51;RS=720x1600;OI=1;OV=10;CP=38;RM=4096;",
    "v2043": "->y20 (2021)",
    "v2043 21": "->y20 (2021)",
    "v20 (2021)": "RE=2020.12;SZ=74.2x161.3x7.4;WT=171;DS=6.44;RS=1080x2400;OI=1;OV=11;CP=261;RM=8192;",
    "v2040": "->v20 (2021)",
    "y89": "RE=2019.01;SZ=75x154.8x7.9;WT=149.3;DS=6.26;RS=1080x2280;OI=1;OV=8.1;CP=272;RM=4096;",
    "v1730ea": "->y89",
    "y93s": "RE=2018.12;SZ=75.1x155.1x8.3;WT=165;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=171;RM=4096;",
    "v1818ct": "->y93s",
    "v1818ca": "->y93s",
    "y97": "RE=2018.09;SZ=75.6x155.9x8.1;WT=162;DS=6.3;RS=1080x2280;OI=1;OV=8.1;CP=290;RM=4096;",
    "v1813t": "->y97",
    "v1813a": "->y97",
    "y9s": "RE=2019.12;SZ=75.2x159.3x8.7;WT=186.7;DS=6.38;RS=1080x2340;OI=1;OV=9.0;CP=283;RM=8192;",
    "v1945a": "->y9s",
    "z1": "RE=2018.05;SZ=75x154.8x7.9;WT=149.3;DS=6.26;RS=1080x2280;OI=1;OV=8.1;CP=61;RM=4096;",
    "v1801a0": "->z1",
    "z1i": "RE=2017.07;SZ=75x154.8x7.9;WT=149.3;DS=6.26;RS=1080x2280;OI=1;OV=8.1;CP=205;RM=4096;",
    "v1730da": "->z1i",
    "v1730dt": "->z1i",
    "z3": "RE=2018.10;SZ=75.6x156x8.1;WT=164;DS=6.3;RS=1080x2280;OI=1;OV=8.1;CP=298;RM=4096;",
    "v1813ba": "->z3",
    "v1813bt": "->z3",
    "z3x": "RE=2019.04;SZ=75x154.8x7.9;WT=150;DS=6.26;RS=1080x2280;OI=1;OV=9.0;CP=61;RM=4096;",
    "v1730ga": "->z3x",
    "z5": "RE=2019.07;SZ=75.2x159.5x8.1;WT=187;DS=6.38;RS=1080x2340;OI=1;OV=9.0;CP=289;RM=6144",
    "v1921a": "->z5",
    "z5x": "RE=2019.05;SZ=77.3x162.4x8.9;WT=204.1;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=62;RM=4096;",
    "v1911a": "->z5x",
    "z5x (2020)": "RE=2020.06;SZ=77.3x162.4x8.9;WT=204.1;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=289;RM=4096;",
    "v1990a": "->z5x (2020)",
    "z6": "RE=2020.02;SZ=75.1x164x9.2;WT=201;DS=6.57;RS=1080x2400;OI=1;OV=10;CP=242;RM=6144;",
    "v1963a": "->z6",
    "y20": "RE=2020.08;SZ=76.3x164.4x8.4;WT=192.3;DS=6.51;RS=720x1600;OI=1;OV=10;CP=244;RM=3072;",
    "v2027": "->y20",
    "v2029": "->y20",
    "v20 se": "RE=2020.09;SZ=74.1x161x7.8;WT=171;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=283;RM=8192;",
    "v2023": "->v20 se",
    "v2022": "->v20 se",
    "v20": "RE=2020.09;SZ=74.2x161.3x7.4;WT=171;DS=6.44;RS=1080x2400;OI=1;OV=11;CP=273;RM=8192;",
    "v2025": "->v20",
    "y20i": "RE=2020.08;SZ=76.3x164.4x8.4;WT=192.3;DS=6.51;RS=720x1600;OI=1;OV=10;CP=244;RM=3072;",
    "v2032": "->y20i",
    "y31": "RE=2021.01;SZ=75.3x163.9x8.4;WT=188;DS=6.58;RS=1080x2408;OI=1;OV=11;CP=285;RM=4096;",
    "y31l": "->y31",
    "y31a": "->y31",
    "v2036": "->y31",
    "s7e": "->y73s;",
    "v2031ea": "->s7e",
    "y73s": "RE=2020.11;SZ=74x161x7.7;WT=171.7;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=274;RM=8192;",
    "v2031a": "->y73s",
    "x60": "RE=2020.12;SZ=75x159.6x7.4;WT=175.6;DS=6.56;RS=1080x2376;OI=1;OV=11;CP=301;RM=8192;",
    "v2046a": "->x60",
    "y52s": "RE=2020.12;SZ=75.4x164.2x8.4;WT=185.5;DS=6.58;RS=1080x2408;OI=1;OV=10;CP=274;RM=6144;",
    "v2057a": "->y52s",
    "y52": "RE=2021.05;SZ=75.3x164x8.5;WT=193;DS=6.58;RS=1080x2408;OI=1;OV=11;CP=302;RM=4096;",
    "v2053": "->y52",
    "y72 5g": "RE=2021.05;SZ=75.3x164x8.5;WT=193;DS=6.58;RS=1080x2408;OI=1;OV=11;CP=302;RM=8192;",
    "v2041": "->y72 5g",
    "x21i": "RE=2018.05;SZ=75x154.4x7;WT=159;DS=6.28;RS=1080x2280;OI=1;OV=8.1;CP=290;RM=6144;",
    "x21i a": "->x21i",
    "y30g": "RE=2021.03;SZ=76.3x164.4x8.4;WT=191.4;DS=6.51;RS=720x1600;OI=1;OV=11;CP=253;RM=8192;",
    "v2066ba": "->y30g",
    "v2066a": "->y30g",
    "iqoo z3": "RE=2021.03;SZ=75.3x164x8.5;WT=185.5;DS=6.58;RS=1080x2408;OI=1;OV=11;CP=303;RM=6144;",
    "v2073a": "->iqoo z3",
    "x60 pro": "RE=2021.03;SZ=73.2x158.6x7.6;WT=179;DS=6.56;RS=1080x2376;OI=1;OV=11;CP=304;RM=12288;",
    "v2047a": "->x60 pro",
    "v2046": "->x60 pro",
    "x60 pro plus": "RE=2021.01;SZ=73.4x158.6x9.1;WT=191;DS=6.56;RS=1080x2376;OI=1;OV=11;CP=305;RM=8192;",
    "v2056a": "->x60 pro plus",
    "s9 (2021)": "RE=2021.03;SZ=73.9x158.4x7.4;WT=173;DS=6.44;RS=1080x2400;OI=1;OV=11;CP=306;RM=8192;",
    "v2072a": "->s9 (2021)",
    "s9e": "RE=2021.03;SZ=73.9x158.4x7.5;WT=175;DS=6.44;RS=1080x2400;OI=1;OV=11;CP=297;RM=8192;",
    "v2048a": "->s9e",
    "iqoo neo 5": "RE=2021.03;SZ=76.4x163.3x8.4;WT=196;DS=6.62;RS=1080x2400;OI=1;OV=11;CP=304;RM=8192;",
    "v2055a": "->iqoo neo 5",
    "y20s": "RE=2020.10;SZ=76.3x164.4x8.4;WT=192.3;DS=6.51;RS=720x1600;OI=1;OV=10;CP=244;RM=4096;",
    "v2038": "->y20s",
    "y21s": "RE=2021.09;SZ=76.1x164.3x8;WT=182;DS=6.51;RS=720x1600;OI=1;OV=11;CP=334;RM=4096;SM=2;",
    "v2110": "->y21s",
    "y60s": "RE=2021.12;RS=75.3x159.5x7.49;WT=173;DS=6.57;RS=1080x2340;OI=1;OV=10;CP=242;RM=8192;",
    "v2006": "->y60s",
    "v21 5g": "RE=2021.05;SZ=73.9x159.7x7.3;WT=176;DS=6.44;RS=1080x2400;OI=1;OV=11;CP=300;RM=8192;",
    "v2050": "->v21 5g",
    "iqoo 7 5g": "RE=2021.01;SZ=75.8x162.2x8.7;WT=209.5;DS=6.62;RS=1080x2400;OI=1;OV=11;CP=305;RM=8192;",
    "i2012": "->iqoo 7 5g",
    "y20g (2021)": "RE=2021.01;SZ=76.3x164.4x8.4;WT=192.3;DS=6.51;RS=720x1600;OI=1;OV=10;CP=226;RM=4096;",
    "v2065": "->y20g (2021)",
    "y12s (2021)": "RE=2021.05;SZ=76.3x164.4x8.4;WT=191;DS=6.51;RS=720x1600;OI=1;OV=11;CP=41;RM=3072;",
    "v2069": "->y12s (2021)",
    "v2026 21": "->y12s (2021)",
    "y12s": "RE=2020.11;SZ=76.3x164.4x8.4;WT=191;DS=6.51;RS=720x1600;OI=1;OV=10;CP=38;RM=3072;",
    "v2026": "->y12s",
    "v2033": "->y12s",
    "y30 (2021)": "RE=2021.01;SZ=75.3x163.9x8.4;WT=188;DS=6.58;RS=1080x2408;OI=1;OV=11;CP=285;RM=4096;",
    "v2099a": "->y30 (2021)",
    "iqoo u1x": "RE=2020.10;SZ=76.3x164.4x8.4;WT=192.3;DS=6.51;RS=720x1600;OI=1;OV=10;CP=285;RM=4096;",
    "v2065a": "->iqoo u1x",
    "iqoo u3": "RE=2020.12;SZ=75.4x164.2x8.4;WT=185.5;DS=6.58;RS=1080x2408;OI=1;OV=10;CP=300;RM=6144;",
    "v2061a": "->iqoo u3",
    "iqoo 3 5g": "RE=2020.02;SZ=74.9x158.5x9.2;WT=214.5;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=207;RM=6144;",
    "i1927": "->iqoo 3 5g",
    "i1928": "->iqoo 3 5g",
    "y51s": "RE=2020.07;SZ=76.6x162x8.5;WT=190;DS=6.53;RS=1080x2340;OI=1;OV=10;CP=299;RM=6144;",
    "v2031": "->y51s",
    "y31s": "RE=2021.01;SZ=75.4x164.2x8.4;WT=185.5;DS=6.58;RS=1080x2408;OI=1;OV=11;CP=307;RM=4096;",
    "v2054a": "->y31s",
    "v2068a": "->y31s",
    "v2068": "->y31s",
    "y11s": "RE=2020.10;SZ=76.3x164.4x8.4;WT=191;DS=6.51;RS=720x1600;OI=1;OV=10;CP=244;RM=3072;",
    "v2028": "->y11s",
    "iqoo 7": "RE=2021.01;SZ=75.8x162.2x8.7;WT=209.5;DS=6.62;RS=1080x2400;OI=1;OV=11;CP=305;RM=8192;",
    "v2049a": "->iqoo 7",
    "i2009": "->iqoo 7",
    "x5 pro": "RE=2015.05;SZ=73.5x147.9x6.4;WT=151;DS=5.2;RS=1080x1920;OI=1;OV=5.0;CP=22;RM=2048;",
    "x5pro": "->x5 pro",
    "x5pro l": "->x5 pro",
    "x5pro d": "->x5 pro",
    "nex dual display": "RE=2018.12;SZ=75.3x157.2x8.1;WT=199;DS=6.39;RS=1080x2340;OI=1;OV=9.0;CP=198;RM=10240;",
    "v1821t": "->nex dual display",
    "v1821a": "->nex dual display",
    "x9 plus": "RE=2016.11;SZ=79x162x7.5;WT=199;DS=5.88;RS=1080x1920;OI=1;OV=6.0;CP=196;RM=6144;",
    "x9plus": "->x9 plus",
    "x9plus l": "->x9 plus",
    "x5pro v": "RE=2015.07;SZ=73.45x147.9x6.44;WT=147;DS=5.2;RS=1080x1920;OI=1;OV=5.0;CP=22;RM=2048;",
    "y913": "RE=2014.11;RS=480x854;DS=4.5;CP=17;RM=1024;OI=1;OV=4.4.4;SZ=65.85x130.4x8.6;WT=142;SM=2;",
    "y51l": "DS=5;RS=540x960;SZ=71.7x143.8x7.52;WT=157;RE=2016.01;OI=1;OV=5.0.2;RM=1024;CP=17;SM=2;",
    "u10": "RE=2019.09;SZ=76.8x159.4x8.9;WT=190.5;DS=6.35;RS=720x1544;OI=1;OV=9.0;CP=283;RM=3072;",
    "y67": "RE=2016.11;SZ=75.5x153.8x7.6;WT=154;DS=5.5;RS=720x1280;OI=1;OV=6.0;CP=40;RM=4096;",
    "y67l": "->y67",
    "y67a": "->y67",
    "y12i": "RE=2020.07;SZ=76.8x159.4x8.9;WT=190.5;DS=6.35;RS=720x1544;OI=1;OV=9.0;CP=41;RM=3072;",
    "y23": "DS=4.5;RS=480x854;SZ=66.2x132.5x8.65;WT=141;RE=2014;OI=1;OV=4.4;RM=1024;CP=111;",
    "y23l": "->y23",
    "y27": "DS=4.7;RS=720x1280;SZ=68.1x136.9x6.99;WT=137;RE=2015;OI=1;OV=4.4;RM=1024;CP=17;",
    "y27l": "->y27",
    "y25": "RE=2017.03;SZ=66.4x130.7x9.2;WT=;DS=4.5;RS=480x854;OI=1;OV=5.1;CP=5;RM=1024;",
    "y51 (2015)": "RE=2015.12;SZ=71.7x143.8x7.5;WT=157;DS=5.0;RS=540x960;OI=1;OV=5.0.2;CP=17;RM=2048;",
    "y83": "RE=2018.05;SZ=75.2x155.2x7.7;WT=150;DS=6.22;RS=720x1520;OI=1;OV=8.1;CP=171;RM=4096;",
    "y83a": "->y83",
    "y85": "RE=2018.03;SZ=75.2x155.8x7.9;WT=150;DS=6.26;RS=1080x2280;OI=1;OV=8.1;CP=214;RM=4096;",
    "y85a": "->y85",
    "y37": "RE=2015.08;SZ=77x154x6.8;WT=165;DS=5.5;RS=720x1280;OI=1;OV=5.0;CP=22;RM=2048;",
    "y37a": "->y37",
    "x7": "RE=2016.06;SZ=71.3x147.3x7.2;WT=151;DS=5.2;RS=1080x1920;OI=1;OV=5.1;CP=115;RM=4096;",
    "x7l": "->x7",
    "x6": "RE=2015.11;SZ=73.8x147.9x6.7;WT=135.5;DS=5.2;RS=1080x1920;OI=1;OV=5.1;CP=332;RM=4096;",
    "x6a": "->x6",
    "x6l": "->x6",
    "x6d": "->x6",
    "x9": "RE=2016.11;SZ=74x152.6x7;WT=154;DS=5.5;RS=1080x1920;OI=1;OV=6.1;CP=204;RM=4096;",
    "x9l": "->x9",
    "y66": "RE=2017.03;SZ=75.8x153.6x7.5;WT=157;DS=5.5;RS=720x1280;OI=1;OV=6.1;CP=43;RM=3072;",
    "y66l": "->y66",
    "y75": "RE=2017.12;SZ=72.8x149.3x7.9;WT=142;DS=5.7;RS=720x1440;CP=166;RM=4096;OI=1;OV=7;",
    "y75a": "->y75",
    "y66i": "RE=2017.12;SZ=75.79x153.56x7.49;WT=157;DS=5.5;RS=720x1280;CP=20;RM=3072;OI=1;OV=7;",
    "y66i a": "->y66i",
    "y79": "RE=2017.11;SZ=75.7x155.9x7.7;WT=160;DS=5.99;RS=720x1440;CP=204;RM=4096;OI=1;OV=7;",
    "y79a": "->y79",
    "y12a": "RE=2021.06;SZ=76.3x164.4x8.4;WT=191;DS=6.51;RS=720x1600;OI=1;OV=11;CP=41;RM=3072;",
    "v2102": "->y12a",
    "y33s": "RE=2021.08;SZ=76.1x164.3x8;WT=182;DS=6.58;RS=1080x2408;OI=1;OV=11;CP=226;RM=8192;",
    "v2109": "->y33s",
    "v3 max": "RE=2016.04;SZ=77.1x153.9x7.6;WT=168;DS=5.5;RS=1080x1920;OI=1;OV=5.1;CP=115;RM=4096;",
    "v3max a": "->v3 max",
    "v3max": "->v3 max",
    "v3max l": "->v3 max",
    "x50 lite": "RE=2020.05;SZ=75.2x159.3x8.7;WT=190.2;DS=6.38;RS=1080x2340;OI=1;OV=10;CP=283;RM=8192;",
    "x21 ud": "RE=2018.03;SZ=74.8x154.5x7.4;WT=156.2;DS=6.28;RS=1080x2280;OI=1;OV=8.1;CP=61;RM=6144",
    "x21ud a": "->x21 ud",
    "x21ud": "->x21 ud",
    "iqoo 9": "RE=2022.02;SZ=75.1x159.1x8.6;WT=200;DS=6.56;RS=1080x2376;OI=1;OV=12;CP=305;RM=8192",
    "v2171a": "->iqoo 9",
    "i2017": "->iqoo 9",
    "iqoo 9 pro": "RE=2022.01;SZ=75.2x164.8x8.8;WT=204;DS=6.78;RS=1440x3200;OI=1;OV=12;CP=357;RM=8192",
    "v2172a": "->iqoo 9 pro",
    "i2022": "->iqoo 9 pro",
    "iqoo 9 se": "RE=2022.02;SZ=76.4x163.2x8.4;WT=199;DS=6.62;RS=1080x2400;OI=1;OV=12;CP=305;RM=8192;",
    "i2019": "->iqoo 9 se",
    "v23e 5g": "RE=2021.11;SZ=74.3x160.9x7.4;WT=172;DS=6.44;RS=1080x2400;OI=1;OV=11;CP=356;RM=8192;",
    "v2126": "->v23e 5g",
    "y15a": "RE=2021.11;SZ=75.2x164x8.3;WT=179;DS=6.51;RS=720x1600;OI=1;OV=11;CP=38;RM=4096;",
    "v2134": "->y15a",
    "y15s": "RE=2021.11;SZ=75.2x164x8.3;WT=179;DS=6.51;RS=720x1600;OI=1;OV=11;CP=38;RM=3072;",
    "v2120": "->y15s",
    "v2125": "->y15s",
    "x50e": "RE=2020.09;SZ=75x162.1x8.9;WT=200.4;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=242;RM=8192;",
    "v1930": "->x50e",
    "iqoo u5": "RE=2021.12;SZ=75.8x164x8.3;WT=185;DS=6.58;RS=1080x2408;OI=1;OV=12;CP=358;RM=4096;",
    "v2165a": "->iqoo u5",
    "iqoo z6": "RE=2022.03;SZ=75.8x164x8.3;WT=185;DS=6.58;RS=1080x2408;OI=1;OV=12;CP=358;RM=4096",
    "i2203": "->iqoo z6",
    "i2127": "->iqoo z6",
    "i2202": "->iqoo z6",
    "i2206": "->iqoo z6",
    "iqoo z6 pro": "RE=2022.04;SZ=73.6x159.7x8.5;WT=180;DS=6.44;RS=1080x2404;OI=1;OV=12;CP=359;RM=6144",
    "i2126": "->iqoo z6 pro",
    "x note": "RE=2022.04;SZ=80.3x168.8x8.4;WT=221;DS=7.0;RS=1440x3080;OI=1;OV=12;CP=357;RM=8192;",
    "v2170a": "->x note",
    "x fold": "RE=2022.04;SZ=144.9x162x6.3;WT=311;DS=8.03;RS=1916x2160;OI=1;OV=12;CP=357;RM=12288;",
    "v2178a": "->x fold"
  },
  "mobicel": {
    "r7": "DS=5.7;RS=480x101;SZ=71x146x9.6;WT=167;RE=2020.04;RM=1024;CP=4;OI=1;OV=8.1;SM=2",
    "r7 1": "->r7",
    "hype": "OI=1;OV=8.1;SZ=74.3x153.7x8;WT=164;RE=2019.03;CP=76;DS=5.99;RM=1024;",
    "trendy plus": "DS=5;SZ=73.7x146.3x9.8;RS=480x854;RM=1024;OI=1;OV=7;SM=1;"
  },
  "vernee": {
    "apollo lite": "DS=5.5;RS=1080x1920;SZ=76.2x152x9.2;WT=175;RE=2016.04;OI=1;OV=6.0.1;RM=4096;CP=291;",
    "apollo x": "DS=5.5;RS=1080x1920;SZ=76.2x152x9.2;WT=175;RE=2017.12;OI=1;OV=7.0;RM=4096;CP=291;SM=2;",
    "m5": "DS=5.2;RS=720x1280;SZ=72.6x147.3x6.9;WT=145;RE=2017.08;OI=1;OV=7.0;RM=4096;CP=40;",
    "mars pro": "DS=5.5;RS=1080x1920;SZ=74x151x7.6;WT=161;RE=2017;OI=1;OV=7.1;RM=6144;CP=292;",
    "thor e": "RE=2017.05;SZ=70.1x144x8.6;WT=149;DS=5.0;RS=720x1280;OI=1;OV=7.0;CP=118;RM=3072;",
    "thor plus": "DS=5.5;RS=720x1280;SZ=76.7x153x8.7;WT=189;RE=2017.07;OI=1;OV=7.0;RM=3072;CP=118;",
    "x2": "DS=6;RS=720x1440;SZ=;WT=;RE=2019;OI=1;OV=9.0;RM=3072;CP=293;",
    "x2 euro": "->x2",
    "m7": "RE=2018.03;SZ=156x80x11.2;WT=178;DS=5.8;RS=1080x1920;OI=1;OV=8.0;CP=40;RM=4096;",
    "m7 eea": "->m7",
    "t3 pro": "RE=2018.07;SZ=70.3x147.0x9.8;WT=159;DS=5.5;RS=1440x720;OI=1;OV=8.0;CP=84;RM=3072;"
  },
  "sony": {
    "xperia 5 ii": "RE=2020.09;SZ=68x158x8;WT=163;DS=6.1;RS=1080x2520;OI=1;OV=10;CP=207;RM=8192;",
    "sog02": "->xperia 5 ii",
    "a002so": "->xperia 5 ii",
    "xq-as62": "->xperia 5 ii",
    "xq-as52": "->xperia 5 ii",
    "xq-as72": "->xperia 5 ii",
    "so-52a": "->xperia 5 ii",
    "xperia z5": "RE=2015.09;SZ=72x146x7.3;WT=154;DS=5.2;RS=1080x1920;OI=1;OV=5.1.1;CP=96;RM=3072;",
    "501so": "->xperia z5",
    "e6653": "->xperia z5",
    "so-01h": "->xperia z5",
    "sov32": "->xperia z5",
    "e6603": "->xperia z5",
    "xperia t2 ultra": "RE=2014.01;SZ=83.8x165.2x7.7;WT=171.7;DS=6.0;RS=720x1280;OI=1;OV=4.3;CP=294;RM=1024;",
    "xm50t": "->xperia t2 ultra",
    "d5303": "->xperia t2 ultra",
    "d5306": "->xperia t2 ultra",
    "xm50h": "->xperia t2 ultra",
    "d5316": "->xperia t2 ultra",
    "xperia z ultra": "RE=2013.06;SZ=92.2x179.4x6.5;WT=212;DS=6.4;RS=1080x1920;OI=1;OV=4.2;CP=94;RM=2048;",
    "sonyc6806": "->xperia z ultra",
    "xl39h": "->xperia z ultra",
    "c6806": "->xperia z ultra",
    "c6802": "->xperia z ultra",
    "c6833": "->xperia z ultra",
    "sol24": "->xperia z ultra",
    "c6843": "->xperia z ultra",
    "xperia 1": "RE=2019.02;SZ=72x167x8.2;WT=178;DS=6.5;RS=1644x3840;OI=1;OV=9.0;CP=234;RM=6144;",
    "j8170": "->xperia 1",
    "j9110": "->xperia 1",
    "j8110": "->xperia 1",
    "802so": "->xperia 1",
    "so-03l": "->xperia 1",
    "sov40": "->xperia 1",
    "xperia 10": "RE=2019.02;SZ=68x156x8.4;WT=162;DS=6.0;RS=1080x2520;OI=1;OV=9.0;CP=19;RM=3072;",
    "i3113": "->xperia 10",
    "i3123": "->xperia 10",
    "i4113": "->xperia 10",
    "xperia 10 dual": "->xperia 10;SM=2;",
    "i4193": "->xperia 10 dual",
    "xperia 10 ii": "RE=2020.02;SZ=69x157x8.2;WT=151;DS=6.0;RS=1080x2520;OI=1;OV=10;CP=283;RM=4096;",
    "a001so": "->xperia 10 ii",
    "xq-au52": "->xperia 10 ii",
    "xq-at52": "->xperia 10 ii",
    "so-41a": "->xperia 10 ii",
    "sov43": "->xperia 10 ii",
    "xq-au42": "->xperia 10 ii",
    "xperia 10 plus": "RE=2019.02;SZ=73x167x8.3;WT=180;DS=6.5;RS=1080x2520;OI=1;OV=9.0;CP=205;RM=4096;",
    "i4293": "->xperia 10 plus",
    "i3223": "->xperia 10 plus",
    "i4213": "->xperia 10 plus",
    "xperia 5": "RE=2019.09;SZ=68x158x8.2;WT=164;DS=6.1;RS=1080x2520;OI=1;OV=9.0;CP=234;RM=6144;",
    "901so": "->xperia 5",
    "sov41": "->xperia 5",
    "j8270": "->xperia 5",
    "so-01m": "->xperia 5",
    "j8210": "->xperia 5",
    "xperia 5 dual": "->xperia 5;SM=2;",
    "j9210": "->xperia 5 dual",
    "xperia 8": "DS=6;RS=1080x2520;SZ=69x158x8.1;WT=170;RE=2019.10;OI=1;OV=9.0;RM=4096;CP=19;",
    "902so": "->xperia 8",
    "sov42": "->xperia 8",
    "xperia xa": "RE=2016.02;SZ=66.8x143.6x7.9;WT=137.4;DS=5.0;RS=720x1280;OI=1;OV=6.0.1;CP=117;RM=2048;",
    "f3111": "->xperia xa",
    "f3113": "->xperia xa",
    "f3115": "->xperia xa",
    "xperia xa dual": "->xperia xa;SM=2;",
    "f3112": "->xperia xa dual",
    "f3116": "->xperia xa dual",
    "xperia xa ultra": "RE=2016.05;SZ=79x164x8.4;WT=202;DS=6.0;RS=1080x1920;OI=1;OV=6.0.1;CP=117;RM=3072;",
    "f3213": "->xperia xa ultra",
    "f3211": "->xperia xa ultra",
    "f3215": "->xperia xa ultra",
    "xperia xa ultra dual": "->xperia xa ultra;SM=2;",
    "f3216": "->xperia xa ultra dual",
    "f3212": "->xperia xa ultra dual",
    "xperia xa1": "RE=2017.02;SZ=67x145x8;WT=143;DS=5.0;RS=720x1280;OI=1;OV=7.0;CP=174;RM=3072;",
    "g3116": "->xperia xa1",
    "g3123": "->xperia xa1",
    "xperia xa1 plus": "RE=2017.08;SZ=75x155x8.7;WT=189;DS=5.5;RS=1080x1920;OI=1;OV=7.0;CP=174;RM=3072;",
    "g3423": "->xperia xa1 plus",
    "g3412": "->xperia xa1 plus",
    "g3416": "->xperia xa1 plus",
    "g3421": "->xperia xa1 plus",
    "xperia x performance": "RE=2016.02;SZ=70.4x143.7x8.7;WT=164.4;DS=5.0;RS=1080x1920;OI=1;OV=6.0.1;CP=173;RM=3072;",
    "sov33": "->xperia x performance",
    "so-04h": "->xperia x performance",
    "502so": "->xperia x performance",
    "f8131": "->xperia x performance",
    "xperia x performance dual": "->xperia x performance;SM=2;",
    "f8132": "->xperia x performance dual",
    "xperia xa2": "RE=2018.01;SZ=70x142x9.7;WT=171;DS=5.2;RS=1080x1920;OI=1;OV=8.0;CP=19;RM=3072;",
    "h3133": "->xperia xa2",
    "h3113": "->xperia xa2",
    "h3123": "->xperia xa2",
    "xperia xa2 dual": "->xperia xa2;SM=2;",
    "h4113": "->xperia xa2 dual",
    "h4133": "->xperia xa2 dual",
    "xperia xz": "RE=2016.09;SZ=72x146x8.1;WT=161;DS=5.2;RS=1080x1920;OI=1;OV=6.0.1;CP=173;RM=3072;",
    "f8331": "->xperia xz",
    "601so": "->xperia xz",
    "f8332": "->xperia xz",
    "so-01j": "->xperia xz",
    "sov34": "->xperia xz",
    "xperia xz1": "RE=2017.08;SZ=73.4x148x7.4;WT=155;DS=5.2;RS=1080x1920;OI=1;OV=8.0;CP=25;RM=4096;",
    "701so": "->xperia xz1",
    "sov36": "->xperia xz1",
    "g8342": "->xperia xz1",
    "so-01k": "->xperia xz1",
    "xperia xz1 compact": "RE=2017.08;SZ=65x129x9.3;WT=140;DS=4.6;RS=720x1280;OI=1;OV=8.0;CP=25;RM=4096;",
    "so-02k": "->xperia xz1 compact",
    "g8441": "->xperia xz1 compact",
    "xperia xz2": "RE=2018.02;SZ=72x153x11.1;WT=198;DS=5.7;RS=1080x2160;OI=1;OV=8.0;CP=198;RM=4096;",
    "h8216": "->xperia xz2",
    "h8324": "->xperia xz2",
    "sov37": "->xperia xz2",
    "h8314": "->xperia xz2",
    "h8296": "->xperia xz2",
    "702so": "->xperia xz2",
    "so-03k": "->xperia xz2",
    "h8276": "->xperia xz2",
    "xperia xz premium": "RE=2017.02;SZ=77x156x7.9;WT=195;DS=5.46;RS=3840x2160;OI=1;OV=7.1;CP=25;RM=4096;",
    "g8141": "->xperia xz premium",
    "so-04j": "->xperia xz premium",
    "so-04k": "->xperia xz premium",
    "xperia xa2 ultra": "RE=2018.01;SZ=80x163x9.5;WT=221;DS=6.0;RS=1080x1920;OI=1;OV=8.0;CP=19;RM=4096;",
    "h4213": "->xperia xa2 ultra",
    "h3223": "->xperia xa2 ultra",
    "xperia xa2 plus dual": "RE=2017.07;SZ=75x157x9.6;WT=205;DS=6.0;RS=1080x2160;OI=1;OV=8.0;CP=19;RM=4096;SM=2;",
    "h4413": "->xperia xa2 plus dual",
    "h4493": "->xperia xa2 plus dual",
    "xperia sp": "RE=2013.03;SZ=67.1x130.6x10;WT=155;DS=4.6;RS=720x1280;OI=1;OV=4.1;CP=15;RM=1024;",
    "sonyc5306": "->xperia sp",
    "sonyc5303": "->xperia sp",
    "c5306": "->xperia sp",
    "c5303": "->xperia sp",
    "c5302": "->xperia sp",
    "xperia s": "RE=2012.01;SZ=64x128x10.6;WT=144;DS=4.3;RS=720x1280;OI=1;OV=2.3;CP=202;RM=1024;",
    "lt26i": "->xperia s",
    "sonyericssonlt26i": "->xperia s",
    "sonyericssonlt26i-o": "->xperia s",
    "sonylt26iv": "->xperia s",
    "sonylt26i": "->xperia s",
    "xperia sl": "RE=2012.08;SZ=64x128x10.6;WT=144;DS=4.3;RS=720x1280;OI=1;OV=4.0.4;CP=202;RM=1024;",
    "lt26ii": "->xperia sl",
    "xperia t": "RE=2012.08;SZ=67.3x129.4x9.4;WT=139;DS=4.55;RS=720x1280;OI=1;OV=4.0.4;CP=140;RM=1024;",
    "lt30p": "->xperia t",
    "sonylt30p": "->xperia t",
    "sonylt30p-o": "->xperia t",
    "xperia t lte": "RE=2012.10;SZ=67.3x129.4x9.4;WT=148;DS=4.55;RS=720x1280;OI=1;OV=4.0.4;CP=14;RM=1024;",
    "sonylt30a": "->xperia t lte",
    "lt30a": "->xperia t lte",
    "xperia t2 ultra dual": "RE=2014.01;SZ=83.8x165.2x7.7;WT=171.8;DS=6.0;RS=720x1280;OI=1;OV=4.3;CP=78;RM=1024;SM=2;",
    "d5322": "->xperia t2 ultra dual",
    "xperia m": "RE=2013.06;SZ=62x124x9.3;WT=115;DS=4.0;RS=480x854;OI=1;OV=4.1;CP=295;RM=1024;",
    "sonyc1905": "->xperia m",
    "sonyc1904": "->xperia m",
    "c1905": "->xperia m",
    "c1904": "->xperia m",
    "xperia xz3": "RE=2018.08;SZ=73x158x9.9;WT=193;DS=6.0;RS=1440x2880;OI=1;OV=9.0;CP=198;RM=4096;",
    "h8416": "->xperia xz3",
    "h9436": "->xperia xz3",
    "h9493": "->xperia xz3",
    "so-01l": "->xperia xz3",
    "801so": "->xperia xz3",
    "sov39": "->xperia xz3",
    "xperia z1": "RE=2013.09;SZ=74x144x8.5;WT=170;DS=5.0;RS=1080x1920;OI=1;OV=4.2;CP=94;RM=2048;",
    "sonyc6903": "->xperia z1",
    "c6903": "->xperia z1",
    "so-01f": "->xperia z1",
    "c6906": "->xperia z1",
    "sonyc6906": "->xperia z1",
    "l39h": "->xperia z1",
    "c6916": "->xperia z1",
    "sonyso-02f": "->xperia z1",
    "c6902": "->xperia z1",
    "c6943": "->xperia z1",
    "so-02f": "->xperia z1",
    "sol23": "->xperia z1",
    "xperia z3": "RE=2014.09;SZ=72x146x7.3;WT=152;DS=5.2;RS=1080x1920;OI=1;OV=4.4.4;CP=243;RM=3072;",
    "d6653": "->xperia z3",
    "d6603": "->xperia z3",
    "so-01g": "->xperia z3",
    "sol26": "->xperia z3",
    "401so": "->xperia z3",
    "d6616": "->xperia z3",
    "d6646": "->xperia z3",
    "d6643": "->xperia z3",
    "xperia 1 ii": "RE=2020.02;SZ=71.1x165.1x7.6;WT=181.4;DS=6.5;RS=1644x3840;OI=1;OV=10;CP=207;RM=8192;",
    "xq-at42": "->xperia 1 ii",
    "xq-at51": "->xperia 1 ii",
    "sog01": "->xperia 1 ii",
    "so-51a": "->xperia 1 ii",
    "xperia xzs": "RE=2017.02;SZ=72x146x8.1;WT=161;DS=5.2;RS=1080x1920;OI=1;OV=7.1;CP=173;RM=4096;",
    "g8232": "->xperia xzs",
    "g8231": "->xperia xzs",
    "sov35": "->xperia xzs",
    "so-03j": "->xperia xzs",
    "602so": "->xperia xzs",
    "xperia m2": "RE=2014.02;SZ=71.1x139.7x8.6;WT=148;DS=4.8;RS=540x960;OI=1;OV=4.3;CP=78;RM=1024;",
    "d2305": "->xperia m2",
    "d2306": "->xperia m2",
    "d2303": "->xperia m2",
    "xperia m2 dual": "->xperia m2;SM=2;",
    "d2302": "->xperia m2 dual",
    "xperia c4": "RE=2015.05;SZ=77.4x150.3x7.9;WT=147;DS=5.5;RS=1080x1920;OI=1;OV=5.0;CP=332;RM=2048;",
    "e5306": "->xperia c4",
    "e5353": "->xperia c4",
    "e5303": "->xperia c4"
  },
  "gionee": {
    "f103 pro": "RE=2016.09;SZ=70.5x145.3x8.5;WT=143;DS=5;RS=720x1280;OI=1;OV=6.0;RM=3072;",
    "s11 lite": "RE=2017.11;SZ=72.6x153.8x7.9;WT=141;DS=5.7;RS=720x1440;OI=1;OV=7.1;CP=43;RM=4096;",
    "f205": "RE=2018.01;SZ=70.7x148.4x8;WT=135.6;DS=5.45;RS=720x1440;OI=1;OV=7.1;CP=8;RM=2048;",
    "f9": "DS=5.71;RS=720x1520;SZ=70.9x147.3x8.9;WT=158;RE=2019.09;OI=1;OV=9.0;RM=3072;CP=109;"
  },
  "walton": {
    "primo c1": "RS=320x480;DS=3.5;RM=512;OI=1;OV=2.3;SZ=61x116x12;WT=112.17;RE=2013.04;SM=2;",
    "primo d2": "RE=2013.09;SZ=63x118.63x12.35;WT=107;DS=3.5;RS=320x480;OI=1;OV=4.2.2;RM=256;SM=2;",
    "primo d4": "",
    "primo d6": "",
    "primo d8": "DS=4;RS=480x800;SZ=66x128x11;WT=120;RE=2016;OI=1;OV=6.0;RM=512;CP=5;",
    "primo e8": "DS=4.5;RS=480x854;SZ=68.2x133.5x10.4;WT=132;RE=2016;OI=1;OV=6.0;RM=512;CP=287;",
    "primo f7": "DS=5;RS=480x854;SZ=73.7x146.3x9.8;WT=120;RE=2016;OI=1;OV=6.0;RM=1024;CP=58;",
    "primo g4": "",
    "primo g6": "RE=2015.10;WT=135;SZ=64.7x131.4x8.0;OI=1;OV=5.1;RM=1024;DS=4.5;RS=480x854;",
    "primo g7": "DS=5.5;RS=720x1280;SZ=80x152x10.5;WT=190;RE=2017;OI=1;OV=7.0;RM=1024;CP=5;",
    "primo h4": "DS=5;RS=720x1280;SZ=71x143x8;WT=136;RE=2015;OI=1;OV=5.1;RM=1024;CP=5;",
    "primo h6": "DS=5;RS=720x1280;SZ=71.2x143.2x8.4;WT=141;RE=2016;OI=1;OV=6.0;RM=2048;CP=56;",
    "primo hm": "DS=5;RS=480x854;SZ=72x144x10.5;WT=193;RE=2014;OI=1;OV=4.2.2;RM=1024;CP=7;",
    "primo r4": "DS=5;RS=720x1280;SZ=70.3x143x7.9;WT=136;RE=2016;OI=1;OV=5.0;RM=2048;CP=79;",
    "primo r6": "RE=2019.09;SM=2;WT=164;DS=6.1;SZ=73.5x155.6x8.85;RS=720x1520;OI=1;OV=9;RM=3072;CP=109;",
    "primo e10 plus": "DS=5;RM=1024;OI=1;OV=8.1;RE=2019;SZ=74.2x145x10;WT=156;RS=480x854;CP=;SM=2;"
  },
  "wileyfox": {
    "spark": "DS=5;RS=720x1280;SZ=70.4x143x8.65;WT=134;RE=2016;OI=1;OV=6.0.1;RM=1024;CP=308;",
    "spark +": "DS=5;RS=720x1280;SZ=70.4x143x8.65;WT=134;RE=2016;OI=1;OV=6.0;RM=2048;CP=308;",
    "spark x": "DS=5.5;RS=720x1280;SZ=78.6x154.35x8.75;WT=162;RE=2016;OI=1;OV=6.0;RM=2048;CP=79;",
    "storm": "DS=5.5;RS=1080x1920;SZ=77.3x155.6x9.2;WT=155;RE=2015;OI=1;OV=5.1.1;RM=3072;CP=22;",
    "swift": "DS=5;RS=720x1280;SZ=71x141.15x9.37;WT=135;RE=2015;OI=1;OV=5.1.1;RM=2048;CP=17;",
    "swift 2": "DS=5;RS=720x1280;SZ=71.9x143.7x8.64;WT=158;RE=2016;OI=1;OV=6.0.1;RM=2048;CP=43;",
    "swift 2 plus": "DS=5;RS=720x1280;SZ=71.9x143.7x8.64;WT=158;RE=2016;OI=1;OV=6.0.1;RM=3072;CP=43;",
    "swift 2 x": "DS=5.2;RS=1080x1920;SZ=72.2x144x8.8;WT=155;RE=2017;OI=1;OV=6.0.1;RM=3072;CP=43;",
    "pro": "DS=5;RS=720x1280;SZ=72.5x147x8.2;WT=;RE=2017;OS=Windows 10 Mobile;RM=2048;CP=10;"
  },
  "weimei": {
    "weplus 3": "DS=6;RS=720x1440;SZ=76.6x160x8.2;WT=188;RE=2018.01;CP=292;RM=4096;OI=1;OV=7;"
  },
  "wink": {
    "city s": "DS=5;RS=540x960;SZ=74x144x8.5;WT=139;RE=2015.05;RM=1024;CP=5;OI=1;OV=5.1;",
    "city se": "DS=4.5;RS=480x854;SZ=67.0x136.0x9.5;WT=130;RM=1024;CP=4;OI=1;OV=6;",
    "glory": "DS=5;CP=36;RM=1024;RS=480x854;WT=135;SZ=72x144x8;SM=2;OI=1;OV=4.4.3;",
    "world se": "SZ=64x124x10.9;DS=4;RS=480x800;WT=129;OI=1;OV=5;RM=512;CP=7;SM=2;"
  },
  "nuu mobile": {
    "a1": "DS=4;RS=480x800;SZ=63x121.4x9.9;WT=;RE=2016;OI=1;OV=5.1;RM=512;CP=5;",
    "a3": "DS=5;RS=480x854;SZ=71.6x143.76x9.9;WT=;RE=2016;OI=1;OV=6.0;RM=1024;CP=5;",
    "a6l-c": "",
    "a6l-g": "",
    "g2": "DS=5.99;RS=1080x2160;SZ=73.6x158.5x8.55;WT=;RE=2018;OI=1;OV=8.0;RM=4096;CP=40;",
    "s6001l": "->g2",
    "g3": "DS=5.7;RS=720x1440;SZ=70x153x9;WT=;RE=2018;OI=1;OV=7.1;RM=4096;CP=309;",
    "n5702l": "->g3",
    "m3": "DS=5.5;RS=720x1280;SZ=77.5x151.6x7.9;WT=;RE=2017;OI=1;OV=7.0;RM=2048;CP=86;",
    "n5001l": "DS=5;RS=720x1280;SZ=71.1x142.2x7.9;WT=152;RE=2016;OI=1;OV=5.1 Lollipop;RM=1024;CP=56;",
    "x5": "DS=5.5;RS=1080x1920;SZ=76.45x152.9x7.87;WT=;RE=2017;OI=1;OV=7.0;RM=3072;CP=162;",
    "m2": "DS=5;RS=720x1280;SZ=71.88x143.76x7.37;WT=;RE=2017;OI=1;OV=7.0;RM=2048;CP=86;",
    "m19": "DS=5.5;RS=720x1440;SZ=72x150.3x9.2;WT=165;RE=2020.09;SM=2;OI=1;OV=9.0;RM=3072;CP=8;",
    "s5501l": "->m19"
  },
  "digifors": {
    "400": "->smart 400",
    "smart 400": "SZ=92x92x18;WT=450;RM=2048;OI=1;OV=7.1;CP=314;"
  },
  "pipo": {
    "p9": "DS=10.1;RS=1920x1200;RM=2048;CP=82;OI=1;OV=4.4;SZ=248x174x10;WT=580;RE=2014.06;",
    "m9 pro": "DS=10.1;RS=1920x1200;RM=2048;CP=80;OI=1;OV=4.2;SZ=248x175x12;WT=580;RE=2013.06;",
    "t9": "DS=8.9;RS=1920x1200;RM=2048;CP=2;OI=1;OV=4.2;SZ=218x161x10;WT=470;RE=2014.06;",
    "x9s": "DS=8.9;RS=1920x1200;RE=2016.07;OI=3;OV=10;SZ=217x147.5x60;WT=630;CP=315;RM=2048;",
    "x10": "DS=10.8;RS=1280x800;RE=2016.04;OI=3;OV=10;SZ=178x118x50;WT=850;CP=189;RM=4096"
  },
  "mivo": {
    "caryota 1": "DS=6;RS=720x1280;RM=2048;RE=2017.06;OI=1;OV=5.1;",
    "caryota 2": "DS=6;RS=720x1280;RM=2048;RE=2017.06;OI=1;OV=5.1;",
    "caryota 4": "",
    "caryota 5": "",
    "caryota 7": "",
    "caryota 8": "",
    "caryota 9": "",
    "jazz j3 (2018)": "",
    "jazz j3 2018": "->jazz j3 (2018)",
    "jazz j1 (2018)": "RM=1024;DS=5;OI=1;OV=6.0;SM=2;RS=360x640;",
    "jazz j1 2018": "->jazz j1 (2018)"
  },
  "realme": {
    "1": "RE=2018.05;SZ=75.2x156.5x7.8;WT=155;DS=6.0;RS=1080x2160;OI=1;OV=8.1;CP=290;RM=3072;",
    "2": "RE=2018.08;SZ=75.6x156.2x8.2;WT=168;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=214;RM=3072;",
    "3": "RE=2019.05;SZ=75.6x156.1x8.3;WT=175;DS=6.22;RS=720x1520;OI=1;OV=9.0;CP=290;RM=3072;",
    "5": "RE=2019.08;SZ=75.6x164.4x9.3;WT=198;DS=6.5;RS=720x1600;OI=1;OV=9.0;CP=283;RM=3072;",
    "6": "RE=2020.03;SZ=74.8x162.1x8.9;WT=191;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=325;RM=4096;",
    "7": "RE=2020.09;SZ=75.4x162.3x9.4;WT=196.5;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=321;RM=6144;",
    "8": "RE=2021.03;SZ=73.9x160.6x8;WT=177;DS=6.4;RS=1080x2400;OI=1;OV=11;CP=321;RM=4096;",
    "c30": "RE=2022.06;SZ=75.6x164.1x8.5;WT=182;DS=6.5;RS=720x1600;OI=1;OV=11;CP=366;RM=2048;",
    "rmx3581": "->c30",
    "cph1861": "->1",
    "rmx1809": "->2",
    "rmx1805": "->2",
    "rmx1833": "->3",
    "rmx1821": "->3",
    "rmx1825": "->3",
    "rmx1919": "->5",
    "rmx1911": "->5",
    "rmx1927": "->5",
    "rmx2001": "->6",
    "rmx2151": "->7",
    "rmx2155": "->7",
    "rmx3085": "->8",
    "2 pro": "RE=2018.09;SZ=74x156.7x8.5;WT=174;DS=6.3;RS=1080x2340;OI=1;OV=8.1;CP=61;RM=4096;",
    "rmx1801": "->2 pro",
    "rmx1807": "->2 pro",
    "rmx1803": "->2 pro",
    "3 pro": "RE=2019.04;SZ=74.2x156.8x8.3;WT=172;DS=6.3;RS=1080x2340;OI=1;OV=9.0;CP=62;RM=4096;",
    "rmx1851": "->3 pro",
    "3i": "RE=2019.07;SZ=75.6x156.1x8.3;WT=175;DS=6.2;RS=720x1520;OI=1;OV=9.0;CP=114;RM=3072;",
    "rmx1827": "->3i",
    "5 pro": "RE=2019.08;SZ=74.2x157x8.9;WT=184;DS=6.3;RS=1080x2340;OI=1;OV=9.0;CP=289;RM=4096;",
    "rmx1971": "->5 pro",
    "rmx1973": "->5 pro",
    "5i": "RE=2020.01;SZ=75x164.4x9.3;WT=195;DS=6.52;RS=720x1600;OI=1;OV=9.0;CP=283;RM=3072;",
    "rmx2030": "->5i",
    "rmx2032": "->5i",
    "5s": "RE=2019.11;SZ=75.6x164.4x9.3;WT=198;DS=6.5;RS=720x1600;OI=1;OV=9.0;CP=283;RM=4096;",
    "rmx1925": "->5s",
    "6 pro": "RE=2020.05;SZ=75.8x163.8x8.9;WT=202;DS=6.6;RS=1080x2400;OI=1;OV=10;CP=273;RM=6144;",
    "rmx2063": "->6 pro",
    "rmx2061": "->6 pro",
    "6i": "RE=2020.03;SZ=75.4x164.4x9;WT=199;DS=6.5;RS=720x1600;OI=1;OV=10;CP=226;RM=3072;",
    "rmx2040": "->6i",
    "rmx2042": "->6i",
    "6s": "RE=2020.05;SZ=74.8x162.1x8.9;WT=191;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=325;RM=4096",
    "rmx2002": "->6s",
    "c1": "RE=2018.09;SZ=75.6x156.2x8.2;WT=168;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=214;RM=2048;",
    "rmx1811": "->c1",
    "c11": "RE=2020.06;SZ=75.9x164.4x9.1;WT=196;DS=6.5;RS=720x1560;OI=1;OV=10;CP=323;RM=2048;",
    "rmx2185": "->c11",
    "c12": "RE=2020.08;SZ=75.9x164.5x9.8;WT=209;DS=6.5;RS=720x1560;OI=1;OV=10;CP=323;RM=3072;",
    "rmx2189": "->c12",
    "c15": "RE=2020.07;SZ=75.9x164.5x9.8;WT=209;DS=6.5;RS=720x1600;OI=1;OV=10;CP=323;RM=3072;",
    "rmx2180": "->c15",
    "c2": "RE=2019.04;SZ=73.7x154.3x8.5;WT=166;DS=6.1;RS=720x1560;OI=1;OV=9.0;CP=171;RM=2048;",
    "rmx1943": "->c2",
    "rmx1945": "->c2",
    "rmx1941": "->c2",
    "rmx1942": "->c2",
    "c3": "RE=2020.06;SZ=75x164.4x9;WT=195;DS=6.5;RS=720x1600;OI=1;OV=10;CP=319;RM=2048;",
    "rmx2021": "->c3",
    "rmx2020": "->c3",
    "rmx2027": "->c3",
    "u1": "RE=2018.11;SZ=74x157x8;WT=168;DS=6.3;RS=1080x2340;OI=1;OV=8.1;CP=114;RM=3072;",
    "rmx1831": "->u1",
    "v3 5g": "DS=6.5;RS=720x1600;SZ=76x164.4x8.6;WT=189;RE=2020.09;OI=1;OV=10;RM=8192;CP=274",
    "rmx2200": "->v3 5g",
    "rmx2201": "->v3 5g",
    "v5 5g": "RE=2020.11;SZ=75.1x162.2x9.1;WT=195;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=320;RM=6144;",
    "7 5g": "->v5 5g",
    "rmx2111": "->v5 5g",
    "x": "RE=2019.05;SZ=76x161.2x9.4;WT=191;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=62;RM=4096;",
    "rmx1901": "->x",
    "rmx1903": "->x",
    "x2": "RE=2019.09;SZ=75.2x158.7x8.6;WT=182;DS=6.4;RS=1080x2340;OI=1;OV=9.0;CP=260;RM=4096;",
    "rmx1992": "->x2",
    "rmx1991": "->x2",
    "x2 dual": "->x2;SM=2;",
    "rmx1993": "->x2 dual",
    "x2 pro": "RE=2019.10;SZ=75.7x161x8.7;WT=199;DS=6.5;RS=1080x2400;OI=1;OV=9.0;CP=197;RM=6144;",
    "rmx1931": "->x2 pro",
    "x3": "RE=2020.06;SZ=75.8x163.8x8.9;WT=202;DS=6.6;RS=1080x2400;OI=1;OV=10;CP=197;RM=6144;",
    "rmx2142": "->x3",
    "rmx2081": "->x3",
    "x3 super zoom": "RE=2020.05;SZ=75.8x163.8x8.9;WT=202;DS=6.6;RS=1080x2400;OI=1;OV=10;CP=197;RM=8192;",
    "rmx2086": "->x3 super zoom",
    "x50 5g": "RE=2020.01;SZ=75.8x163.8x8.9;WT=202;DS=6.57;RS=1080x2400;OI=1;OV=10;CP=242;RM=6144;",
    "rmx2025": "->x50 5g",
    "rmx2051": "->x50 5g",
    "x50 pro": "RE=2020.02;SZ=74.2x159x8.9;WT=205;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=141;RM=6144;",
    "rmx2071": "->x50 pro",
    "x50 pro player": "RE=2020.05;SZ=74.2x159x8.9;WT=209;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=207;RM=6144;",
    "rmx2072": "->x50 pro player",
    "x7 5g": "RE=2020.09;SZ=74.4x160.9x8.1;WT=175;DS=6.4;RS=1080x2400;OI=1;OV=10;CP=300;RM=6144;",
    "rmx2176": "->x7 5g",
    "x7 pro 5g": "RE=2020.09;SZ=75.1x160.8x8.5;WT=184;DS=6.55;RS=1080x2400;OI=1;OV=10;CP=296;RM=6144",
    "rmx2121": "->x7 pro 5g",
    "xt": "RE=2019.09;SZ=75.2x158.7x8.6;WT=183;DS=6.4;RS=1080x2340;OI=1;OV=9.0;CP=289;RM=4096;",
    "rmx1921": "->xt",
    "x50 pro 5g": "DS=6.44;RS=1080x2400;SZ=;WT=;RE=2020.05;OI=1;OV=10;RM=6144;CP=207;",
    "rmx2076": "->x50 pro 5g",
    "rmx2075": "->x50 pro 5g",
    "7 pro": "RE=2020.09;SZ=74.3x160.9x8.7;WT=182;DS=6.4;RS=1080x2400;OI=1;OV=10;CP=273;RM=6144;",
    "rmx2170": "->7 pro",
    "rmx2085": "->x3 super zoom",
    "c21": "RE=2021.03;SZ=76.4x165.2x8.9;WT=190;DS=6.5;RS=720x1600;OI=1;OV=10;CP=323;RM=3072;",
    "rmx3201": "->c21",
    "7i": "RE=2020.12;SZ=75.9x164.5x9.8;WT=208;DS=6.5;RS=720x1600;OI=1;OV=10;CP=322;RM=4096;",
    "rmx2103": "->7i",
    "c20": "RE=2021.01;SZ=76.4x165.2x8.9;WT=190;DS=6.5;RS=720x1600;OI=1;OV=10;CP=323;RM=2048;",
    "rmx3063": "->c20",
    "narzo 20 pro": "RE=2020.09;SZ=75.4x162.3x9.4;WT=191;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=321;RM=6144;",
    "rmx2161": "->narzo 20 pro",
    "rmx2163": "->narzo 20 pro",
    "gt neo": "RE=2021.03;SZ=73.3x158.5x8.4;WT=179;DS=6.43;RS=1080x2400;OI=1;OV=11;CP=324;RM=6144;",
    "rmx3031": "->gt neo",
    "8 pro": "RE=2021.03;SZ=73.9x160.6x8.1;WT=176;DS=6.4;RS=1080x2400;OI=1;OV=11;CP=273;RM=6144;",
    "rmx3081": "->8 pro",
    "q2 5g": "RE=2020.10;SZ=75.1x162.2x9.1;WT=194;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=320;RM=4096;",
    "rmx2117": "->q2 5g",
    "c15 qualcomm edition": "RE=2020.10;SZ=75.9x164.5x9.8;WT=209;DS=6.5;RS=720x1600;OI=1;OV=10;CP=244;RM=3072;",
    "rmx2195": "->c15 qualcomm edition",
    "narzo 20": "RE=2020.09;SZ=75.9x164.5x9.8;WT=208;DS=6.5;RS=720x1600;OI=1;OV=10;CP=322;RM=4096;",
    "rmx2193": "->narzo 20",
    "c17": "RE=2020.09;SZ=75.5x164.1x8.9;WT=188;DS=6.5;RS=720x1600;OI=1;OV=10;CP=244;RM=4096;",
    "rmx2101": "->c17",
    "narzo 20a": "RE=2020.09;SZ=75.4x164.4x8.9;WT=195;DS=6.5;RS=720x1600;OI=1;OV=10;CP=283;RM=3072;",
    "rmx2050": "->narzo 20a",
    "rmx2144": "->x50 5g",
    "q2 pro": "RE=2020.10;SZ=74.4x160.9x8.1;WT=175;DS=6.4;RS=1080x2400;OI=1;OV=10;CP=320;RM=8192;",
    "rmx2173": "->q2 pro",
    "v15 5g": "RE=2021.01;SZ=74.4x160.9x8.1;WT=176;DS=6.4;RS=1080x2400;OI=1;OV=10;CP=320;RM=6144;",
    "rmx3092": "->v15 5g",
    "v11 5g": "RE=2021.02;SZ=;WT=186;DS=6.5;RS=720x1600;OI=1;OV=11;CP=302;RM=4096;",
    "rmx3121": "->v11 5g",
    "v13 5g": "RE=2021.03;SZ=74.8x162.5x8.5;WT=185;DS=6.5;RS=1080x2400;OI=1;OV=11;CP=302;RM=8192;",
    "rmx3041": "->v13 5g",
    "rmx3093": "->v15 5g",
    "gt 5g": "RE=2021.03;SZ=73.3x158.5x8.4;WT=186;DS=6.43;RS=1080x2400;OI=1;OV=11;CP=305;RM=8192;",
    "rmx2202": "->gt 5g",
    "c11 (2021)": "RE=2021.06;SZ=76.4x165.2x8.9;WT=190;DS=6.52;RS=720x1600;OI=1;OV=11;CP=109;RM=2048;",
    "rmx3231": "->c11 (2021)",
    "c25": "RE=2021.03;SZ=75.9x164.5x9.6;WT=209;DS=6.5;RS=720x1600;OI=1;OV=11;CP=319;RM=4096;",
    "rmx3191": "->c25",
    "c21y": "RE=2021.06;SZ=76x164.5x9.1;WT=200;DS=6.5;RS=720x1600;OI=1;OV=10;CP=318;RM=3072;",
    "rmx3261": "->c21y",
    "8 5g": "RE=2021.04;SZ=74.8x162.5x8.5;WT=185;DS=6.5;RS=1080x2400;OI=1;OV=11;CP=302;RM=4096;",
    "rmx3241": "->8 5g",
    "narzo 30 5g": "RE=2021.05;SZ=74.8x162.5x8.5;WT=185;DS=6.5;RS=1080x2400;OI=1;OV=11;CP=302;RM=4096;",
    "rmx3242": "->narzo 30 5g",
    "gt neo flash": "RE=2021.05;SZ=73.3x158.5x8.4;WT=179;DS=6.43;RS=1080x2400;OI=1;OV=11;CP=324;RM=8192;",
    "rmx3350": "->gt neo flash",
    "x7 pro extreme edition": "DS=6.55;RS=1080x2400;SZ=73.4x159.9x7.8;WT=170;RE=2021;OI=1;OV=11;RM=8192;CP=296;",
    "rmx3115": "->x7 pro extreme edition",
    "q3 pro 5g": "RE=2021.04;SZ=73.3x158.5x8.4;WT=179;DS=6.43;RS=1080x2400;OI=1;OV=11;CP=326;RM=6144;",
    "rmx2205": "->q3 pro 5g",
    "narzo 30a": "RE=2021.02;SZ=75.9x164.5x9.8;WT=205;DS=6.5;RS=720x1600;OI=1;OV=10;CP=322;RM=3072;",
    "rmx3171": "->narzo 30a",
    "c25s": "RE=2021.06;SZ=75.9x164.5x9.6;WT=209;DS=6.5;RS=720x1600;OI=1;OV=11;CP=322;RM=4096;",
    "rmx3195": "->c25s",
    "narzo 50": "RE=2022.02;SZ=75.5x164.1x8.5;WT=194;DS=6.6;RS=1080x2412;OI=1;OV=11;CP=350;RM=4096;",
    "rmx3286": "->narzo 50"
  },
  "lg": {
    "v30": "RE=2017.08;SZ=75.4x151.7x7.3;WT=158;DS=6.0;RS=1440x2880;OI=1;OV=7.1.2;CP=25;RM=4096;",
    "lg-h933": "->v30",
    "lg-h932pr": "->v30",
    "lg-ls998": "->v30",
    "lg-us998": "->v30",
    "lg-as998": "->v30",
    "lg-h930": "->v30",
    "lg-h931": "->v30",
    "lg-h932": "->v30",
    "lgm-v300k": "->v30",
    "lgm-v300l": "->v30",
    "lgm-v300s": "->v30",
    "lg-h930re": "->v30",
    "v30 plus": "RE=2018.05;SZ=75.4x151.7x7.3;WT=157;DS=6.0;RS=1440x2880;OI=1;OV=8.0;CP=198;RM=6144;",
    "lgv35": "->v30 plus",
    "g6": "RE=2017.02;SZ=71.9x148.9x7.9;WT=163;DS=5.7;RS=1440x2880;OI=1;OV=7.0;CP=208;RM=4096;",
    "lg-h873": "->g6",
    "lg-h872": "->g6",
    "lgus997": "->g6",
    "lg-h871": "->g6",
    "lg-h870s": "->g6",
    "lg-as993": "->g6",
    "lg-h870ar": "->g6",
    "lg-h870i": "->g6",
    "lgm-g600s": "->g6",
    "lgm-g600l": "->g6",
    "lg-us997": "->g6",
    "lg-h870": "->g6",
    "lg-ls993": "->g6",
    "lg-h870ds": "->g6",
    "vs988": "->g6",
    "k52": "RE=2020.09;SZ=76.7x165x8.4;WT=188;DS=6.6;RS=720x1600;OI=1;OV=10;CP=38;RM=4096;",
    "lm-k520": "->k52",
    "w10": "RE=2019.06;SZ=76.2x156x8.5;WT=164;DS=6.19;RS=720x1512;OI=1;OV=9.0;CP=171;RM=3072;",
    "lmx130im": "->w10",
    "k40s": "RE=2019.08;SZ=73.9x156.3x8.6;WT=186;DS=6.1;RS=720x1520;OI=1;OV=9.0;CP=171;RM=2048;",
    "lm-x430": "->k40s",
    "v50 thinq": "RE=2019.02;SZ=76.1x159.2x8.3;WT=183;DS=6.4;RS=1440x3120;OI=1;OV=9.0;CP=234;RM=6144;",
    "lm-v450": "->v50 thinq",
    "lm-v500n": "->v50 thinq",
    "v60 thinq 5g": "RE=2020.02;SZ=77.6x169.3x8.9;WT=213;DS=6.8;RS=1080x2460;OI=1;OV=10;CP=207;RM=8192;",
    "a001lg": "->v60 thinq 5g",
    "lm-v600": "->v60 thinq 5g",
    "v40 thinq": "RE=2018.10;SZ=75.7x158.8x7.6;WT=169;DS=6.4;RS=1440x3120;OI=1;OV=8.1;CP=198;RM=6144;",
    "lm-v405ebw": "->v40 thinq",
    "lm-v405": "->v40 thinq",
    "lm-v409n": "->v40 thinq",
    "lm-v40": "->v40 thinq",
    "fortune": "DS=5;RS=480x854;SZ=72.4x144.78x7.9;WT=;RE=2017.02;OI=1;OV=6.0.1;RM=1536;CP=10;",
    "lg-m153": "->fortune",
    "g4": "RE=2015.04;SZ=76.1x148.9x6.3;WT=155;DS=5.5;RS=1440x2560;OI=1;OV=5.1.1;CP=157;RM=3072;",
    "lg-h812": "->g4",
    "lgh810": "->g4",
    "lgls991": "->g4",
    "lgus991": "->g4",
    "lg-f500l": "->g4",
    "lg-h810": "->g4",
    "lg-f500s": "->g4",
    "lg-f500k": "->g4",
    "lg-h811": "->g4",
    "lg-h815": "->g4",
    "lg-h818": "->g4",
    "lgv32": "->g4",
    "lg-h819": "->g4",
    "stylo 3": "RE=2016.12;SZ=79.8x155.6x7.4;WT=149;DS=5.7;RS=720x1280;OI=1;OV=7.0;CP=40;RM=3072;",
    "lg-ls777": "->stylo 3;CP=35;",
    "lgl84vl": "->stylo 3",
    "lg-m430": "->stylo 3",
    "lgl83bl": "->stylo 3",
    "k4 lte": "RE=2016.01;SZ=66.7x131.9x8.9;WT=120;DS=4.5;RS=480x854;OI=1;OV=5.1.1;CP=10;RM=1024;",
    "lg-k130": "->k4 lte",
    "k20 plus": "RE=2016.12;SZ=75.2x148.6x7.9;WT=140;DS=5.3;RS=720x1280;OI=1;OV=7.0;CP=20;RM=2048;",
    "lg-tp260": "->k20 plus",
    "lgmp260": "->k20 plus",
    "k40": "RE=2019.02;SZ=71.9x153x8.3;WT=144;DS=5.7;RS=720x1440;OI=1;OV=8.1;CP=171;RM=2048;",
    "lm-x420": "->k40",
    "k7": "RE=2016.01;SZ=72.5x143.6x9.1;WT=161;DS=5.0;RS=480x854;OI=1;OV=5.1;CP=58;RM=1024;",
    "lg-x210": "->k7",
    "lgms330": "->k7",
    "lg-k330": "->k7",
    "lg-k332": "->k7",
    "g3 beat": "RE=2014.07;SZ=69.6x137.7x10.3;WT=134;DS=5.0;RS=720x1280;OI=1;OV=4.4.2;CP=111;RM=1024;",
    "lg-d723": "->g3 beat",
    "lg-d725": "->g3 beat",
    "lg-d725pr": "->g3 beat",
    "lg-d726": "->g3 beat",
    "lg-d728": "->g3 beat",
    "stylus 2": "RE=2016.02;SZ=79.6x155x7.4;WT=142;DS=5.7;RS=720x1280;OI=1;OV=6.0;CP=17;RM=1536;",
    "lg-k520": "->stylus 2",
    "lg-f720s": "->stylus 2",
    "stylus 2 plus": "RE=2016.05;SZ=79.6x155x7.4;WT=146;DS=5.7;RS=1080x1920;OI=1;OV=6.0;CP=43;RM=2048;",
    "lg-k530": "->stylus 2 plus",
    "lg-k535": "->stylus 2 plus",
    "lg-k550": "->stylo 2 plus",
    "v10": "RE=2016.01;SZ=74.8x146x8.8;WT=142;DS=5.3;RS=720x1280;OI=1;OV=5.1.1;CP=17;RM=1024",
    "lg-h900": "->v10",
    "lg-h960": "->v10",
    "lg-k428": "->v10",
    "lg-h962": "->v10",
    "lg-h961s": "->v10",
    "lg-h961n": "->v10",
    "lg-h901": "->v10",
    "rs987": "->v10",
    "vs990": "->v10",
    "spirit": "RE=2015.02;SZ=66.1x133.3x10;WT=124.4;DS=4.7;RS=720x1280;OI=1;OV=5.0.1;CP=17;RM=1024;",
    "lg-h440n": "->spirit",
    "lg-h440ar": "->spirit",
    "lg-h440": "->spirit",
    "lg-h420": "->spirit;CP=7;",
    "risio 2": "RS=480x854;DS=5;CP=10;RM=2048;OI=1;OV=6.0.1;SZ=73.66x144.78x7.62;WT=136.08;RE=2017.04;",
    "lg-m154": "->risio 2",
    "k30": "RE=2018.05;SZ=75x148.6x8.6;WT=168.1;DS=5.3;RS=720x1280;OI=1;OV=7.1;CP=20;RM=2048;",
    "lgl322dl": "->k30",
    "lm-x410um": "->k30",
    "lm-x320": "->k30",
    "k3": "RE=2016.08;SZ=67.7x133.8x9.4;WT=127;DS=4.5;RS=480x854;OI=1;OV=6.0;CP=10;RM=1024;",
    "lgls450": "->k3",
    "lg-k100": "->k3;CP=86;",
    "k3 (2017)": "RE=2016.12;SZ=69.8x133.9x9.5;WT=132;DS=4.5;RS=480x854;OI=1;OV=6.0.1;CP=10;RM=1024;",
    "lg-as110": "->k3 (2017)",
    "x300": "DS=5;RS=720x1280;SZ=72.1x144.8x8.09;WT=142;RE=2016;OI=1;OV=7.0;RM=2048;CP=20;",
    "lgm-k120l": "->x300",
    "lgm-k120s": "->x300",
    "lgm-k120k": "->x300",
    "q6": "RE=2017.07;SZ=69.3x142.5x8.1;WT=149;DS=5.5;RS=1080x2160;OI=1;OV=7.1.1;CP=35;RM=2048;",
    "lgm-x600s": "->q6",
    "lg-m700": "->q6",
    "lgm-x600l": "->q6",
    "lgm-x600k": "->q6",
    "lg-us700": "->q6",
    "q61": "RE=2020.05;SZ=77.5x164.5x8.3;WT=;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=38;RM=4096;",
    "lm-q630": "->q61",
    "lm-q630n": "->q61",
    "stylo 6": "RE=2020.05;SZ=77.7x171.2x8.6;WT=219;DS=6.8;RS=1080x2460;OI=1;OV=10;CP=38;RM=3072;",
    "lm-q730": "->stylo 6",
    "q51": "RE=2020.02;SZ=77.5x164.5x8.4;WT=;DS=6.5;RS=720x1520;OI=1;OV=10;CP=171;RM=3072;",
    "lm-q510n": "->q51",
    "stylo 5": "RE=2019.06;SZ=77.7x160x8.4;WT=179;DS=6.2;RS=1080x2160;OI=1;OV=9.0;CP=214;RM=3072;",
    "lgl722dl": "->stylo 5",
    "lm-q720": "->stylo 5",
    "k50": "RE=2019.02;SZ=77x161.3x8.7;WT=170;DS=6.26;RS=720x1520;OI=1;OV=9.0;CP=171;RM=3072;",
    "802lg": "->k50",
    "lm-x520": "->k50",
    "jojo": "RS=1440x2880;DS=6;CP=25;RM=4096;OI=1;OV=7.1.2;SZ=75.4x151.7x7.4;WT=158;RE=2017.09;",
    "l-02k": "->jojo",
    "k31": "RE=2020.08;SZ=71.1x147.8x8.6;WT=146;DS=5.7;RS=720x1520;OI=1;OV=10;CP=171;RM=2048;",
    "lm-k300": "->k31",
    "k22": "RE=2020.09;SZ=75.4x155.7x8.4;WT=169.5;DS=6.2;RS=720x1520;OI=1;OV=10;CP=229;RM=2048;",
    "lm-k200": "->k22",
    "k42": "RE=2020.09;SZ=76.7x165x8.4;WT=182;DS=6.6;RS=720x1600;OI=1;OV=10;CP=171;RM=3072;",
    "lm-k420": "->k42",
    "k51": "RE=2020.02;SZ=76.7x165.2x8.3;WT=194.5;DS=6.55;RS=720x1600;OI=1;OV=9.0;CP=38;RM=3072;",
    "lm-k500": "->k51",
    "lgl555dl": "->k51",
    "velvet 4g": "RE=2020.07;SZ=74.1x167.1x7.9;WT=180;DS=6.8;RS=1080x2460;OI=1;OV=10;CP=198;RM=6144;",
    "lm-g910": "->velvet 4g",
    "k8 (2017)": "RE=2016.12;SZ=72.1x144.5x8;WT=144;DS=5.0;RS=720x1280;OI=1;OV=7.0;CP=209;RM=1536;",
    "lg-m200": "->k8 (2017)",
    "x charge": "DS=5.5;RS=720x1280;WR=164.1;CP=40;RM=2048;RE=2017.11;SZ=78.1x154.7x8.4;",
    "lg-sp320": "->x charge",
    "lg-m327": "->x charge",
    "velvet 5g": "RE=2020.08;SZ=74.1x167.2x7.9;WT=;DS=6.8;RS=1080x2460;OI=1;OV=10;CP=242;RM=6144;",
    "l-52a": "->velvet 5g",
    "lm-g900": "->velvet 5g",
    "lm-g900n": "->velvet 5g",
    "lm-g900tm": "->velvet 5g",
    "v50s thinq": "RE=2019.10;SZ=75.8x159.3x8.4;WT=192;DS=6.4;RS=1080x2340;OI=1;OV=9.0;CP=234;RM=8192;",
    "lm-v510n": "->v50s thinq",
    "wing": "RE=2020.09;SZ=74.5x169.5x10.9;WT=260;DS=6.8;RS=1080x2460;OI=1;OV=10;CP=242;RM=8192",
    "lm-f100": "->wing",
    "lm-f100n": "->wing",
    "g8x thinq": "RE=2019.09;SZ=75.8x159.3x8.4;WT=192;DS=6.4;RS=1080x2340;OI=1;OV=9.0;CP=234;RM=6144;",
    "lm-g850": "->g8x thinq",
    "901lg": "->g8x thinq",
    "q70": "RE=2019.09;SZ=76.8x162.1x8.3;WT=198;DS=6.4;RS=1080x2310;OI=1;OV=9.0;CP=258;RM=4096;",
    "lm-q730n": "->q70",
    "lm-q620": "->q70",
    "q92": "RE=2020.08;SZ=77.3x166.5x8.5;WT=193;DS=6.67;RS=1080x2400;OI=1;OV=10;CP=242;RM=6144;",
    "q92 5g": "->q92",
    "lm-q920n": "->q92",
    "q9 one": "RE=2018.08;SZ=71.9x153.2x7.9;WT=156;DS=6.1;RS=1440x3120;OI=1;OV=8.1;CP=25;RM=4096;",
    "lm-q927l": "->q9 one",
    "qua tad px": "",
    "lgt31": "->qua tad px",
    "qua tad pz": "",
    "lgt32": "->qua tad pz",
    "lancet": "OS=Windows Phone 8.1;RE=2015;CP=17;RM=1024;DS=4.5;SZ=64.7x129.8x10.7;WT=143;RS=480x854;",
    "vs820": "->lancet",
    "lucid 2": "RE=2013.04;SZ=62.8x122.5x9.8;WT=129;DS=4.3;RS=540x960;OI=1;OV=4.1.2;CP=14;RM=1024;",
    "vs870 4g": "->lucid 2",
    "g2": "RE=2013.08;SZ=70.9x138.5x8.9;WT=143;DS=5.2;RS=1080x1920;OI=1;OV=4.2.2;CP=94;RM=2048;",
    "l-01f": "->g2",
    "lg-f320l": "->g2",
    "g5": "RE=2016.02;SZ=73.9x149.4x7.7;WT=159;DS=5.3;RS=1440x2560;OI=1;OV=6.0.1;CP=173;RM=4096;",
    "lg-h831": "->g5",
    "lg-h868": "->g5",
    "lg-f700k": "->g5",
    "lgls992": "->g5",
    "lg-h830": "->g5",
    "lg-f700l": "->g5",
    "lg-h850": "->g5",
    "lg-h860": "->g5",
    "vs987": "->g5",
    "rs988": "->g5",
    "lgus992": "->g5",
    "lg-h820": "->g5",
    "lg-f700s": "->g5",
    "k20": "RE=2019.08;SZ=71.9x148.6x8.3;WT=152;DS=5.45;RS=480x960;OI=1;OV=9.0;CP=8;RM=1024;",
    "lm-x120": "->k20",
    "vs501": "->k20",
    "lg-m255": "->k20",
    "k41s": "RE=2020.02;SZ=76.5x165.8x8.3;WT=191.7;DS=6.55;RS=720x1600;OI=1;OV=9.0;CP=171;RM=3072;",
    "lm-k410": "->k41s",
    "g3": "RE=2014.05;SZ=74.6x146.3x8.9;WT=149;DS=5.5;RS=1440x2560;OI=1;OV=4.4.2;CP=243;RM=2048;",
    "lg-d858hk": "->g3",
    "lgls990": "->g3",
    "vs985 4g": "->g3",
    "lg-d855": "->g3",
    "lg-d852": "->g3",
    "l90": "RE=2014.02;SZ=66x131.6x9.7;WT=126;DS=4.7;RS=540x960;OI=1;OV=4.4.2;CP=78;RM=1024;",
    "lg-d405": "->l90",
    "lg-d415": "->l90",
    "v20": "RE=2016.09;SZ=78.1x159.7x7.6;WT=174;DS=5.7;RS=1440x2560;OI=1;OV=7.0;CP=173;RM=4096;",
    "lg-h918": "->v20",
    "lg-us996": "->v20",
    "lg-f800k": "->v20",
    "lg-h990": "->v20",
    "lgv34": "->v20",
    "lg-ls997": "->v20",
    "lg-h910": "->v20",
    "lg-f800l": "->v20",
    "lg-f800s": "->v20",
    "vs995": "->v20",
    "vs996": "->v30",
    "rebel 3": "DS=5;RS=480x854;SZ=73.66x144.78x7.9;WT=136;RE=2018;OI=1;OV=6.0.1;RM=2048;CP=10;",
    "lgl158vl": "->rebel 3",
    "k51s": "RE=2020.02;SZ=76.7x165.2x8.3;WT=194.5;DS=6.55;RS=720x1600;OI=1;OV=9.0;CP=38;RM=3072;",
    "lm-k510": "->k51s",
    "q stylus plus": "RE=2018.06;SZ=77.7x160.1x8.1;WT=171;DS=6.2;RS=1080x2160;OI=1;OV=8.1;CP=40;RM=3072;",
    "lm-q710(fgn)": "->q stylus plus;CP=214;RM=2048;",
    "lm-q710fgn": "->q stylus plus;CP=214;RM=2048;",
    "lm-q710.fgn": "->q stylus plus;CP=214;RM=2048;",
    "k4 (2017)": "RE=2016.12;SZ=72.6x144.7x7.9;WT=138;DS=5.0;RS=480x854;OI=1;OV=6.0.1;CP=10;RM=1024;",
    "lg-m160": "->k4 (2017)",
    "lgl57bl": "->k4 (2017)",
    "k8 (2018)": "RE=2016.12;SZ=72.1x144.5x8;WT=144;DS=5.0;RS=720x1280;OI=1;OV=7.0;CP=209;RM=1536;",
    "lgus215": "->k8 (2018)",
    "q7": "RE=2018.05;SZ=69.3x143.8x8.4;WT=145;DS=5.5;RS=1080x2160;OI=1;OV=8.1;CP=312;RM=3072;",
    "lm-q610.fg": "->q7",
    "lm-q710.fg": "->q7",
    "q7+": "->q7;CP=214;",
    "lm-q610.fgn": "->q7+",
    "lm-q725k": "->q7+",
    "lm-q725l": "->q7+",
    "v35": "RE=2018.05;SZ=75.4x151.7x7.3;WT=157;DS=6.0;RS=1440x2880;OI=1;OV=8.0;CP=198;RM=6144;",
    "lm-v350": "->v35",
    "lm-v350n": "->v35",
    "v60": "RE=2020.02;SZ=77.6x169.3x8.9;WT=213;DS=6.8;RS=1080x2460;OI=1;OV=10;CP=207;RM=8192;",
    "l-51a": "->v60",
    "q stylus": "RE=2018.06;SZ=77.7x160.1x8.1;WT=171;DS=6.2;RS=1080x2160;OI=1;OV=8.1;CP=40;RM=3072",
    "801lg": "->q stylus",
    "stylo 3 plus": "RE=2017.05;SZ=79.8x155.7x7.4;WT=150;DS=5.7;RS=1080x1920;OI=1;OV=7.0;CP=35;RM=2048;",
    "lg-tp450": "->stylo 3 plus",
    "lgmp450": "->stylo 3 plus",
    "stylus 3": "RE=2016.12;SZ=79.8x155.6x7.4;WT=149;DS=5.7;RS=720x1280;OI=1;OV=7.0;CP=40;RM=3072;",
    "lg-m400": "->stylus 3",
    "stylo 4": "RE=2018.06;SZ=77.7x160x8.1;WT=172;DS=6.2;RS=1080x2160;OI=1;OV=8.1;CP=214;RM=2048;",
    "lg-q710al": "->stylo 4",
    "lg-q710pl": "->stylo 4",
    "lml713dl": "->stylo 4",
    "x screen": "RE=2016.02;SZ=71.8x142.6x7.1;WT=120;DS=4.93;RS=720x1280;OI=1;OV=6.0;CP=17;RM=2048;",
    "lg-k500": "->x screen",
    "ray": "RE=2015.11;SZ=76.4x152.5x9.3;WT=158;DS=5.5;RS=720x1280;OI=1;OV=5.1;CP=313;RM=1024;",
    "lg-x190": "->ray",
    "it": "DS=5;RS=720x1280;SZ=72x145x8;WT=140;RE=2018;OI=1;OV=8.1;RM=3072;CP=20;",
    "lgv36": "->it",
    "q9": "RE=2019.01;SZ=71.9x153.2x7.9;WT=159;DS=6.1;RS=1440x3120;OI=1;OV=8.1;CP=208;RM=4096;",
    "lm-q925k": "->q9",
    "lm-q925l": "->q9",
    "lm-q925s": "->q9",
    "solo lte": "DS=5.7;RS=720x1440;SZ=71.9x153x8.3;WT=150;RE=2019;OI=1;OV=8.1;RM=2048;CP=214;",
    "lgl423dl": "->solo lte",
    "l bello": "RE=2014.08;SZ=70.6x138.1x10.7;WT=137;DS=5.0;RS=480x854;OI=1;OV=4.4.2;CP=7;RM=1024;",
    "lg-d335": "->l bello",
    "lg-d335e": "->l bello",
    "lg-d331": "->l bello",
    "bello 2": "RE=2015.07;SZ=71.6x140.8x9.6;WT=155;DS=5.0;RS=480x854;OI=1;OV=5.1.1;RM=1024;",
    "lg-x150": "->bello 2",
    "lg-x155": "->bello 2",
    "lg-x165g": "->bello 2",
    "x5": "DS=5.5;RS=720x1280;SZ=78.1x154.7x8.9;WT=171;RE=2018.08;OI=1;OV=8.0;RM=2048;CP=40;",
    "x5-lg": "->x5",
    "lm-x510k": "->x5",
    "lm-x510l": "->x5",
    "lm-x510s": "->x5",
    "x4+": "RE=2018.01;SZ=75.1x148.6x8.6;WT=172.3;DS=5.3;RS=720x1280;OI=1;OV=7.0;CP=20;RM=2048;",
    "lm-x410l": "->x4+",
    "lm-x415l": "->x4+",
    "lm-x415s": "->x4+",
    "x4": "DS=5.3;RS=720x1280;SZ=75.1x148.6x8.6;WT=164;RE=2018;OI=1;OV=7.1.2;RM=2048;CP=20;",
    "lm-x410k": "->x4",
    "lm-x415k": "->x4",
    "lm-x410s": "->x4",
    "x4 (2019)": "DS=5.7;RS=720x1440;SZ=71.9x153x8.3;WT=150;RE=2019;OI=1;OV=8.1;RM=2048;CP=171;",
    "lm-x420n": "->x4 (2019)",
    "x6": "DS=6.26;RS=720x1520;SZ=77x161.3x8.7;WT=172;RE=2019;OI=1;OV=9.0;RM=3072;CP=171;",
    "lm-x625n1": "->x6",
    "x6 (2019)": "->x6",
    "lm-x625n": "->x6 (2019)",
    "zero": "RE=2015.09;SZ=71.8x142x7.4;WT=150;DS=5.0;RS=720x1280;OI=1;OV=5.1.1;CP=17;RM=1536",
    "lg-h650": "->zero",
    "zone 4": "RE=2018.03;SZ=71.9x144.8x7.9;WT=140.3;DS=5.0;RS=720x1280;OI=1;OV=7.1.2;CP=20;RM=2048;",
    "lm-x210vpp": "->zone 4",
    "x2": "RE=2018.06;SZ=73.2x146.3x8.2;WT=152;OI=1;OV=7.1.2;CP=10;RM=2048;DS=5;RS=720x1280;SM=1;",
    "lm-x210k": "->x2",
    "x power 3": "RE=2017.07;SZ=78.1x154.7x8.9;WT=171;DS=5.5;RS=720x1280;OI=1;OV=8.1;CP=20;RM=2048;",
    "lm-x510wm": "->x power 3",
    "x power 2": "RE=2017.02;SZ=78.1x154.7x8.4;WT=164;DS=5.5;RS=720x1280;OI=1;OV=7.0;CP=40;RM=1536;",
    "lg-m320": "->x power 2",
    "x power": "RE=2016.05;SZ=74.9x148.9x7.9;WT=139;DS=5.3;RS=720x1280;OI=1;OV=6.0.1;CP=79;RM=2048;",
    "lgls755": "->x power;CP=117;",
    "lg-k220": "->x power",
    "style 3": "DS=6.1;RE=2020.06;WY=160;SZ=72x152x8.7;RS=1440x3120;RM=4096;CP=198;OI=1;OV=10;SM=1;",
    "l-41a": "->style 3",
    "wine smart h410": "SM=1;OI=1;OV=5.1;WT=143;SZ=58.7x117.7x16.6;DS=3.2;RS=320x480;RM=1024;",
    "lg-h410": "->wine smart h410",
    "l60": "RE=2014.08;SZ=66.3x124.1x11.8;WT=119;DS=4.3;RS=480x800;OI=1;OV=4.4.2;RM=512;",
    "lg-x145": "->l60",
    "x style": "RE=2016.05;SZ=71.4x144.8x6.9;WT=121;DS=5.0;RS=720x1280;OI=1;OV=6.0.1;CP=10;RM=1536;",
    "lg-k200": "->x style",
    "g3 stylus": "RE=2014.08;SZ=75.9x149.3x10.2;WT=163;DS=5.5;RS=540x960;OI=1;OV=4.4.2;CP=7;RM=1024;",
    "lg-d693n": "->g3 stylus",
    "lg-d693": "->g3 stylus",
    "lg-d693tr": "->g3 stylus",
    "lg-d690n": "->g3 stylus",
    "g4 stylus": "RE=2015.04;SZ=79.2x154.3x9.6;WT=163;DS=5.7;RS=720x1280;OI=1;OV=5.0;CP=17;RM=1024;",
    "lg-h542": "->g4 stylus",
    "lg-h630": "->g4 stylus",
    "lg-h540": "->g4 stylus;CP=313;",
    "g8": "RE=2019.02;SZ=71.8x151.9x8.4;WT=167;DS=6.1;RS=1440x3120;OI=1;OV=9.0;CP=234;RM=6144;",
    "lm-g820": "->g8",
    "lm-g820n": "->g8",
    "g7 thinq": "RE=2018.05;SZ=71.9x153.2x7.9;WT=162;DS=6.1;RS=1440x3120;OI=1;OV=8.0;CP=198;RM=4096;",
    "lg-g710": "->g7 thinq",
    "lm-g710n": "->g7 thinq",
    "lm-g710": "->g7 thinq",
    "lm-g710vm": "->g7 thinq",
    "g3 cat. 6": "RE=2014.01;SZ=74.6x146.3x9.1;WT=154;DS=5.5;RS=1440x2560;OI=1;OV=4.4.2;CP=276;RM=3072;",
    "lg-f460": "->g3 cat. 6",
    "lg-f460s": "->g3 cat. 6",
    "lg-f460l": "->g3 cat. 6",
    "lg-f460k": "->g3 cat. 6",
    "l fino": "RE=2014.08;SZ=67.9x127.5x11.9;WT=145;DS=4.5;RS=480x800;OI=1;OV=4.4.2;CP=33;RM=1024;",
    "lg-d290": "->l fino",
    "lg-d295": "->l fino",
    "f70": "RE=2014.02;SZ=66.4x127.2x10;WT=129.6;DS=4.5;RS=480x800;OI=1;OV=4.4.2;CP=111;RM=1024;",
    "lg-d315": "->f70",
    "l70": "RE=2014.02;SZ=66.8x127.2x9.5;WT=124;DS=4.5;RS=480x800;OI=1;OV=4.4.2;CP=119;RM=1024;SM=1;",
    "lg-d320": "->l70",
    "stylo 2": "RE=2016.04;SZ=79.5x155x7.4;WT=144.6;DS=5.7;RS=720x1280;OI=1;OV=6.0;CP=17;RM=2048;",
    "lgls775": "->stylo 2",
    "aka": "RE=2015.03;SZ=71.9x138.7x10;WT=135;DS=5.0;RS=720x1280;OI=1;OV=4.4.2;CP=78;RM=1536;",
    "lg-h788": "->aka",
    "g4 beat": "RE=2015.07;SZ=72.6x142.7x9.9;WT=139;DS=5.2;RS=1080x1920;OI=1;OV=5.1.1;CP=22;RM=1536;",
    "lg-h735": "->g4 beat",
    "g8s thinq": "RE=2019.02;SZ=76.6x155.3x8;WT=181;DS=6.21;RS=1080x2248;OI=1;OV=9.0;CP=234;RM=6144;",
    "lm-g810": "->g8s thinq",
    "g2 mini": "RE=2014.02;SZ=66x129.6x9.8;WT=121;DS=4.7;RS=540x960;OI=1;OV=4.4.2;CP=111;RM=1024;",
    "lg-d618": "->g2 mini",
    "lg-d620": "->g2 mini",
    "g4c": "RE=2015.05;SZ=69.8x139.7x10.2;WT=136;DS=5.0;RS=720x1280;OI=1;OV=5.0.2;CP=17;RM=1024;",
    "lg-h525n": "->g4c",
    "g4s": "DS=4.7;RS=720x1280;SZ=65x133x10;WT=150;RE=2014;OI=1;OV=4.2.2 Jelly Bean;RM=2048;CP=2;",
    "lg-h736": "->g4s",
    "k10": "RE=2016.01;SZ=74.8x146x8.8;WT=142;DS=5.3;RS=720x1280;OI=1;OV=5.1.1;CP=17;RM=1024;",
    "lg-f670s": "->k10",
    "lg-k410": "->k10",
    "lg-k420": "->k10",
    "lg-f670l": "->k10",
    "lg-k430": "->k10;CP=118;",
    "lgms428": "->k10",
    "k8": "RE=2016.02;SZ=71.5x144.6x8.7;WT=157;DS=5.0;RS=720x1280;OI=1;OV=6.0;CP=79;RM=1536;",
    "lg-k350": "->k8",
    "leon": "RE=2015.02;SZ=64.9x129.9x10.9;WT=140;DS=4.5;RS=480x854;OI=1;OV=5.0.1;CP=17;RM=1024;",
    "lg-h324": "->leon",
    "lg-h340n": "->leon",
    "rebel 4": "DS=5;RS=720x1280;SZ=71.9x144.8x7.9;WT=140;RE=2018;OI=1;OV=8.1;RM=2048;CP=20;",
    "lml211bl": "->rebel 4",
    "lml212vl": "->rebel 4",
    "risio": "RS=480x854;DS=4.5;CP=17;RM=2048;OI=1;OV=5.1;SZ=65.02x129.79x10.92;WT=136.93;RE=2015.06;",
    "lg-h343": "->risio",
    "phoenix": "RS=320x480;DS=3.2;CP=145;OI=1;OV=2.2;SZ=58.93x113.28x13.21;WT=129;RE=2011.04;",
    "lg-p505r": "->phoenix",
    "phoenix 2": "DS=5;RS=720x1280;SZ=71.4x144.5x8.6;WT=114;RE=2016;OI=1;OV=6.0;RM=1536;CP=10;",
    "lg-k371": "->phoenix 2",
    "phoenix 3": "RS=480x854;DS=5;CP=10;RM=1536;OI=1;OV=6.0.1;SZ=73.66x144.78x7.62;WT=136.08;RE=2017.05;",
    "lg-m150": "->phoenix 3",
    "phoenix 4": "RS=720x1280;DS=5;CP=20;RM=2048;OI=1;OV=7.1.2;SZ=71.9x144.8x7.9;WT=140;RE=2018.09;",
    "lm-x210apm": "->phoenix 4",
    "k5": "RE=2016.03;SZ=71.6x145x8.9;WT=128;DS=5.0;RS=480x854;OI=1;OV=5.1;CP=7;RM=1024;",
    "lg-x220": "->k5",
    "tribute 5": "DS=5;RS=480x854;SZ=72.4x143.5x8.9;WT=138;RE=2016;OI=1;OV=5.1.1;RM=1024;CP=10;",
    "lgls675": "->tribute 5",
    "tribute empire": "DS=5;RS=720x1280;SZ=71.9x144.8x8.1;WT=140;RE=2019;OI=1;OV=8.1;RM=2048;CP=40;",
    "lm-x220pm": "->tribute empire"
  },
  "minix": {
    "neo u1": "RM=2048;OI=1;OV=5.1.1;CP=333;",
    "neo-u1": "->neo u1"
  },
  "blu": {
    "vivo 8l": "DS=5.3;RS=720x1280;SZ=74.4x150.5x8;WT=161;RE=2017.08;OI=1;OV=7.0;RM=3072;CP=118;",
    "8l": "->vivo 8l",
    "xl2": "->vivo xl2",
    "vivo xl2": "RE=2017.01;SZ=76.6x154x9.2;WT=175;DS=5.5;RS=720x1280;OI=1;OV=6.0;CP=69;RM=3072;",
    "xl3": "->vivo xl3",
    "vivo xl3": "RE=2018.02;SZ=70.7x147.7x8;WT=143;DS=5.5;RS=720x1440;OI=1;OV=8.0;CP=9;RM=3072;",
    "xl4": "->vivo xl4",
    "vivo go": "RE=2018.12;SZ=75.2x156.7x8.4;WT=155;DS=6.0;RS=720x1440;OI=1;OV=9.0;CP=8;RM=1024;",
    "go": "->vivo go",
    "vivo xl4": "RE=2018.12;SZ=76.2x157.2x8;WT=178;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=171;RM=3072;",
    "vivo 5r": "RE=2016.09;SZ=75.6x154.5x8.3;WT=166;DS=5.5;RS=1080x1920;OI=1;OV=6.0;CP=118;RM=3072;",
    "5r": "->vivo 5r",
    "vivo one plus": "RE=2018.04;SZ=76.7x164.8x8.6;WT=173;DS=6.0;RS=720x1440;OI=1;OV=7.1;CP=8;RM=2048;",
    "one plus": "->vivo one plus",
    "vivo one plus (2019)": "RE=2019.01;SZ=75.2x156.7x8.3;WT=178;DS=6.2;RS=720x1500;OI=1;OV=8.1;CP=8;RM=2048;",
    "one plus 2019": "->vivo one plus (2019)",
    "vivo xl3 plus": "RE=2018.03;SZ=76.5x158.1x8.2;WT=169;DS=6.0;RS=720x1440;OI=1;OV=7.1.2;CP=20;RM=3072;",
    "xl3 plus": "->vivo xl3 plus",
    "vivo xi": "RE=2018.09;SZ=72.9x149x7.9;WT=187;DS=5.9;RS=720x1520;OI=1;OV=8.1;CP=171;RM=3072;",
    "xi": "->vivo xi",
    "vivo xi plus": "RE=2018.08;SZ=75.5x155x7.8;WT=199;DS=6.2;RS=1080x2246;OI=1;OV=8.1;CP=114;RM=4096;",
    "vivo xi+": "->vivo xi plus",
    "xi plus": "->vivo xi plus",
    "xi+": "->vivo xi plus",
    "c6 (2020)": "RE=2020.10;SZ=78.4x160.7x10.3;WT=230;DS=6.0;RS=720x1440;OI=1;OV=10;RM=1024;",
    "c6 2020": "->c6 (2020)",
    "view mega": "RE=2020.04;SZ=78x167.5x9.4;WT=189;DS=6.0;RS=720x1440;OI=1;OV=9.0;CP=44;RM=2048;",
    "b110dl": "->view mega"
  },
  "noa": {
    "vivo": "DS=5.45;RS=640x1280;SZ=70.9x148.7x8.9;WT=155;RE=2018.12;OI=1;OV=8.1;RM=2048;CP=83;"
  },
  "oukitel": {
    "c22": "RE=2020.10;SZ=73.7x150.5x8.9;WT=178;DS=5.86;RS=720x1520;CP=44;RM=4096;OI=1;OV=10;",
    "c15 pro": "RE=2019.03;SZ=73.7x156.1x9.5;WT=168;DS=6.09;RS=600x1280;CP=44;RM=2048;OI=1;OV=9;",
    "c16 pro": "RE=2019.06;SZ=70.3x146.4x9.5;WT=153;DS=5.71;RS=720x1520;CP=44;RM=3072;OI=1;OV=9;",
    "k10000": "RE=2015.12;SZ=75.0x157.0x14.0;WT=320;DS=5.5;RS=720x1280;CP=56;RM=2048;OI=1;OV=5.1;",
    "k10000 max": "RE=2017.03;SZ=75.0x157.0x14.0;WT=320;DS=5.5;RS=1080x1920;CP=39;RM=3072;OI=1;OV=7;",
    "k10000 pro": "RE=2017.05;SZ=77.8x161.7x14.0;WT=292;DS=5.5;RS=1080x1920;CP=162;RM=3072;OI=1;OV=7;",
    "k4000": "RE=2015.08;SZ=70.6x143.0x11.0;WT=208;DS=5;RS=720x1280;CP=56;RM=2048;OI=1;OV=5.1;",
    "k4000 lite": "DS=5;RS=540x960;SZ=72x145.6x12.5;WT=208;RE=2016.01;OI=1;OV=5.1;RM=2048;CP=56;"
  },
  "we": {
    "a10": "RE=2018.08;OI=1;OV=8.1;RS=480x640;WT=115.5;SZ=63.3x123x10.1;DS=4;RM=512;",
    "a50": "RE=2018.11;OI=1;OV=8.1;RS=480x854;WT=225;SZ=73.5x146x10.1;DS=5;RM=512;",
    "f10": "RE=2018.08;OI=1;OV=8.1;RS=480x960;WT=161;SZ=65x138x9.6;DS=4.95;RM=1024;SM=2;",
    "f20": "RE=2018.08;OI=1;OV=8.1;RS=480x960;WT=175.7;SZ=72x148x9.2;DS=5.45;RM=1024;SM=2;",
    "l9": "RE=2019.08;OI=1;OV=8.1;RS=720x1080;WT=159.7;SZ=72x143.5x9.5;DS=5;RM=1024;SM=2;",
    "r4": "RE=2019.05;OI=1;OV=8.1;RS=720x1080;WT=159.7;SZ=72x143.5x9.5;DS=5;RM=1024;SM=2;",
    "v4": "RE=2019.06;OI=1;OV=8.1;RS=720x1440;WT=154;SZ=70.36x148.76x8.95;DS=5.5;RM=2048;SM=2;",
    "l3": "RE=2017.01;OI=1;OV=6.0;RS=480x854;WT=146;SZ=65.8x133x9.5;DS=4.5;RM=1024;SM=2;CP=58;",
    "l4": "",
    "r3": "",
    "s1": "",
    "x1": "",
    "v3": "",
    "b3": "",
    "e2": "",
    "l6": "",
    "e1": "",
    "t1": "",
    "x3": ""
  },
  "supra": {
    "m74c 4g": "RE=2015;OI=1;OV=5.1;RS=1024x600;WT=247;DS=7;SZ=188x107.8x10.3;CP=335;RM=512;SM=2;"
  },
  "tecno mobile": {
    "spark go (2022)": "RE=2021.12;SZ=76x164.5x9;WT=199;DS=6.52;RS=720x1600;OI=1;OV=11;RM=2048;",
    "tecno kg5m": "->spark go (2022)",
    "r7+": "DS=5.5;RM=1024;RS=720x1280;OI=1;OV=10;CP=293;",
    "tecno rc6": "->r7+",
    "pop 5 go": "DS=6.1;RS=720x1520;RE=2022.04;SZ=164.87x76.09x9.52;OI=1;OV=11;CP=44;RM=3072;SM=2;",
    "tecno bd1": "->pop 5 go",
    "f2 lte": "RE=2018.05;SZ=74x143.8x9.6;WT=168.5;DS=5;RS=480x854;OI=1;OV=8.1;CP=293;RM=1024;SM=2;",
    "tecno f2lte": "->f2 lte",
    "s9": "DS=7;RS=1024x600;OI=1;OV=4.2;RM=1024;SZ=193x111x9.5;SM=2;",
    "tecno s9": "->s9",
    "pova": "RE=2020.10;SZ=77.6x171.2x9.4;WT=;DS=6.8;RS=720x1640;OI=1;OV=10;CP=226;RM=4096;",
    "tecno ld7j": "->pova",
    "tecno ld7": "->pova",
    "pova 2": "RE=2021.06;SZ=78.8x173.3x9.6;WT=;DS=6.95;RS=1080x2460;OI=1;OV=11;CP=322;RM=4096;",
    "tecno le7n": "->pova 2",
    "pova neo": "RE=2021.12;SZ=77.3x171.4x9.1;WT=;DS=6.8;RS=720x1640;OI=1;OV=11;CP=284;RM=4096;",
    "tecno le6": "->pova neo",
    "camon i4": "RE=2019.05;SZ=75.80x156.90x7.96;WT=141;DS=6.22;RS=720x1520;SM=2;OI=1;OV=9;CP=44;RM=4096;",
    "tecno cb7j": "->camon i4",
    "tecno cb7": "->camon i4",
    "camon iclick 2": "RE=2018.10;SZ=75.6x156.8x7.8;WT=145;DS=6.2;RS=720x1500;OI=1;OV=8.1;CP=171;RM=4096;SM=2;",
    "tecno id6": "->camon iclick 2",
    "d1": "OI=1;OV=4.2;SZ=69.3x114.9x11.7;DS=2.6;RS=320x480;CP=24;RM=512;",
    "tecno d1": "->d1",
    "d3": "OI=1;OV=2.3;SZ=62.5x115.5x11.3;DS=3.5;RS=320x480;RM=512;SM=2;",
    "tecno d3": "->d3",
    "f4 pro": "RE=2018.06;SZ=71.9x151.5x8.5;WT=140;DS=5.45;RS=480x960;OI=1;OV=7;CP=336;RM=1024",
    "tecno f4 pro": "->f4 pro",
    "phantom 8": "RE=2018.04;SZ=79.5x160x7.9;WT=185;DS=5.7;RS=1080x1920;OI=1;OV=7;CP=353;RM=6144;",
    "tecno ax8": "->phantom 8",
    "phantom x": "RE=2021.06;SZ=73.8x163.5x8.7;WT=201;DS=6.7;RS=1080x2340;OI=1;OV=11;CP=321;RM=8192;",
    "tecno ac8": "->phantom x",
    "camon i": "SZ=71.7x152.2x7.5;WT=172;DS=5.65;RS=720x1440;RM=3072;OI=1;OV=7;SM=2;CP=9;",
    "tecno in5": "->camon i",
    "camon 18 premier": "RE=2021.10;SZ=75.9x163.8x8.2;WT=200.6;DS=6.7;RS=1080x2400;OI=1;OV=11;CP=350;RM=8192;SM=2;",
    "tecno ch9n": "->camon 18 premier",
    "camon 16": "RE=2020.10;SZ=77.2x170.9x9.2;WT=207;DS=6.8;RS=720x1640;OI=1;OV=10;CP=319;RM=4096;",
    "tecno ce9h": "->camon 16",
    "tecno ce7": "->camon 16",
    "camon 16 premier": "RE=2020.09;SZ=77.2x170.6x9.1;WT=210;DS=6.85;RS=1080x2460;OI=1;OV=10;CP=325;RM=8192;",
    "tecno ce9": "->camon 16 premier",
    "camon 16 pro": "RE=2020.09;SZ=77.2x170.9x9.2;WT=207;DS=6.8;RS=720x1640;OI=1;OV=10;CP=319;RM=4096;",
    "tecno ce8": "->camon 16 pro",
    "camon 16 s": "RE=2020.11;SZ=76.5x164.5x9;WT=207;DS=6.6;RS=720x1600;OI=1;OV=10;CP=171;RM=4096;SM=2;",
    "tecno cd6j": "->camon 16 s",
    "camon 17": "RE=2021.05;SZ=76.5x164.5x9;WT=201;DS=6.6;RS=720x1600;OI=1;OV=11;CP=322;RM=4096;",
    "tecno cg6": "->camon 17",
    "tecno cg6j": "->camon 17",
    "camon 15": "RE=2020.02;SZ=76.4x164.1x8.8;WT=196;DS=6.6;RS=720x1600;OI=1;OV=10;CP=171;RM=4096;",
    "tecno cd7": "->camon 15",
    "spark 6 go": "RE=2020.11;SZ=76.3x165.6x9.1;WT=193.5;DS=6.52;RS=720x1600;OI=1;OV=10;CP=337;RM=3072;",
    "tecno ke5k": "->spark 6 go",
    "tecno ke5": "->spark 6 go",
    "tecno ke5j": "->spark 6 go",
    "spark 6": "RE=2020.09;SZ=77.3x170.8x9.2;WT=193.5;DS=6.8;RS=720x1640;OI=1;OV=10;CP=319;RM=4096;",
    "tecno ke7": "->spark 6",
    "camon 16 se": "RE=2020.10;SZ=77.2x170.9x9.2;WT=207;DS=6.8;RS=720x1640;OI=1;OV=10;CP=319;RM=4096;",
    "tecno ce7j": "->camon 16 se",
    "camon 15 pro": "RE=2020.02;SZ=77.7x163.3x9.1;WT=203;DS=6.6;RS=1080x2340;OI=1;OV=10;CP=38;RM=6144;",
    "tecno cd8": "->camon 15 pro",
    "spark 5 air": "RE=2020.05;SZ=79.3x174.6x9.1;WT=175;DS=7.0;RS=720x1640;OI=1;OV=10;CP=44;RM=2048;SM=2;",
    "tecno kd6": "->spark 5 air",
    "tecno kd6a": "->spark 5 air",
    "spark 6 air": "RE=2020.07;SZ=79.4x174.7x9.3;WT=193.5;DS=7.0;RS=720x1640;OI=1;OV=10;CP=44;RM=2048;SM=2;",
    "tecno ke6": "->spark 6 air",
    "tecno ke6j": "->spark 6 air",
    "camon isky": "RE=2018.04;SZ=147.5x70.8x8.3;WT=137;RS=480x960;DS=5.45;OI=1;OV=8.1;CP=293;RM=2048;SM=2;",
    "tecno in2": "->camon isky",
    "camon i sky": "->camon isky",
    "camon 12 air": "RE=2019.10;SZ=76.1x164.2x8.2;WT=181;DS=6.55;RS=720x1600;OI=1;OV=9;CP=44;RM=3072;",
    "tecno cc6": "->camon 12 air",
    "tecno kc3": "->camon 12 air",
    "camon 11": "RE=2018.09;SZ=76x156.5x5.6;WT=150;DS=6.2;RS=720x1500;OI=1;OV=8.1;CP=44;RM=3072;",
    "tecno cf7k": "->camon 11",
    "tecno cf7": "->camon 11",
    "camon 11 pro": "RE=2018.09;SZ=76x156.5x5.6;WT=155;DS=6.2;RS=720x1500;OI=1;OV=8.1;CP=171;RM=6144;",
    "tecno cf8": "->camon 11 pro",
    "wx3 pro": "RE=2017.07;DS=5;RS=480x854;SZ=73x145x10.9;SM=2;RM=1024;OI=1;OV=7;CP=5;",
    "tecno wx3p": "->wx3 pro",
    "pop 1": "RE=2018.03;SZ=71.9x151.5x8.5;WT=140;DS=5.45;RS=480x960;OI=1;OV=7.0;CP=336;RM=1024;",
    "tecno f3": "->pop 1",
    "camon x": "RE=2018.05;SZ=75.8x158.6x7.75;WT=154;DS=6.0;RS=720x1440;OI=1;OV=8.1;RM=3072;CP=166;SM=2;",
    "tecno ca7": "->camon x",
    "pop 3": "RE=2019.12;OI=1;OV=8.1;RM=1024;SZ=75.4x154.3x9.95;WT=;DS=5.7;RS=480x960;SM=2;",
    "tecno bb2": "->pop 3",
    "f7": "RE=2013.04;SM=2;SZ=70.8x143.5x9.1;WT=;DS=5;RS=720x1280;CP=;RM=1024;OI=1;OV=4.2;",
    "tecno f7": "->f7",
    "f7 phantom": "->f7",
    "pouvoir 3 plus": "RE=2019.08;SZ=76x159x9.2;WT=180;DS=6.35;RS=720x1548;OI=1;OV=9;CP=171;RM=6144;",
    "tecno lb8": "->pouvoir 3 plus",
    "tecno lb8a": "->pouvoir 3 plus",
    "camon cx": "RE=2017.03;SZ=75.8x152.8x5.6;WT=150;DS=5.5;RS=1080x1920;OI=1;OV=7;CP=162;RM=2048;",
    "tecno camon cx": "->camon cx",
    "wx4": "DS=5;RS=720x1280;SZ=71.2x143.1x8.5;WT=164;RE=2017.08;OI=1;OV=7;RM=1024;CP=9;",
    "tecno wx4": "->wx4",
    "s6": "DS=5;RS=480x854;SZ=72x145x9.6;WT=127;RE=2017.09;OI=1;OV=7;RM=1024;CP=5;SM=2;",
    "tecno s6": "->s6",
    "l9": "RE=2017.05;SM=2;WT=170;SZ=76.2x151x8.2;DS=5.5;RS=720x1280;OI=1;OV=7;RM=1024;",
    "tecno l9": "->l9",
    "h5": "OI=1;OV=4.2;SZ=65.8x124x11.6;WT=;DS=4;RS=480x800;RM=512;CP=24;RE=2014.05;",
    "tecno h5": "->h5",
    "droidpad 7g": "DS=7;SZ=181.25x102.8x9.7;WT=208;RS=1024x600;RM=2048;CP=230;OI=1;OV=10;SM=1;",
    "tecno p704a": "->droidpad 7g",
    "droidpad 7d": "RE=2017.04;DS=7;RS=1024x600;WT=268;RM=1024;CP=163;SM=2;OI=1;OV=7;",
    "tecno p701": "->droidpad 7d",
    "droidpad 8d": "DS=8;RS=1280x800;SZ=211x123x8.3;WT=;SM=2;RM=1024;CP=339;OI=1;OV=5.1;RE=2016.04;",
    "tecno dp8d": "->droidpad 8d",
    "camon i2x": "DS=6.2;RS=720x1520;SZ=76x156.6x7.9;WT=;RE=2018;OI=1;OV=8.1;RM=4096;CP=171;SM=2;",
    "tecno id5b": "->camon i2x",
    "camon 12": "RE=2019.08;SZ=75.8x166x8.2;WT=170.7;DS=6.52;RS=720x1600;OI=1;OV=9;CP=171;RM=4096;",
    "tecno cc7": "->camon 12",
    "tecno cc7s": "->camon 12",
    "camon 12 pro": "RE=2019.09;SZ=75.5x158.6x7.8;WT=161;DS=6.35;RS=720x1600;OI=1;OV=9;CP=171;RM=6144;",
    "tecno cc9": "->camon 12 pro",
    "spark 4": "RE=2019.09;SZ=75.9x165.3x8.2;WT=;DS=6.52;RS=720x1600;OI=1;OV=9.0;RM=2048;SM=2;",
    "tecno kc8": "->spark 4",
    "tecno kc2": "->spark 4",
    "tecno kc2j": "->spark 4",
    "tecno kc8s": "->spark 4",
    "spark 5": "RE=2020.05;SZ=76.3x164.7x8.8;WT=194;DS=6.6;RS=720x1600;OI=1;OV=10;CP=44;RM=2048;SM=2;",
    "tecno kd7h": "->spark 5",
    "tecno kd7s": "->spark 5",
    "spark 5 pro": "RE=2020.07;SZ=76.3x164.7x8.8;WT=;DS=6.6;RS=720x1600;OI=1;OV=10;CP=340;RM=3072;",
    "tecno kd7": "->spark 5 pro",
    "spark 2": "RE=2018.06;SZ=76.2x159.4x7.8;WT=175;DS=6.0;RS=720x1440;OI=1;OV=8.1;CP=5;RM=1024;",
    "tecno ka7": "->spark 2",
    "tecno ka7o": "->spark 2",
    "spark 3": "RE=2019.04;SZ=75.8x155.3x8.2;WT=;DS=6.2;RS=720x1500;OI=1;OV=9;CP=44;RM=2048;",
    "tecno kb7": "->spark 3",
    "tecno kb7j": "->spark 3",
    "spark 3 pro": "RE=2019.04;SZ=75.5x154.3x7.9;WT=;DS=6.2;RS=720x1500;OI=1;OV=9;CP=44;RM=2048;",
    "tecno kb8": "->spark 3 pro",
    "camon 15 air": "RE=2020.04;SZ=76.5x164.5x9;WT=196;DS=6.6;RS=720x1600;OI=1;OV=10;CP=171;RM=3072;",
    "tecno cd6s": "->camon 15 air",
    "tecno cd6": "->camon 15 air",
    "camon 15 premier": "RE=2020.04;SZ=77.7x163.3x9.1;WT=203;DS=6.6;RS=1080x2340;OI=1;OV=10;CP=38;RM=6144;",
    "tecno cd8j": "->camon 15 premier",
    "spark youth": "RE=2019.03;OI=1;OV=8.1;SZ=76.6x156.95x8.1;WT=155;CP=5;DS=6;RM=1024;SM=2;",
    "tecno ka6": "->spark youth",
    "spark 4 air": "DS=6.1;RS=720x1560;SZ=75x156.1x9.2;WT=;RE=2019.08;OI=1;OV=9;RM=2048;CP=44;",
    "tecno kc6": "->spark 4 air",
    "tecno kc6s": "->spark 4 air",
    "tecno kc1j": "->spark 4 air",
    "camon x pro": "RE=2018.03;SZ=75.2x158x5.2;WT=148;DS=6.0;RS=1080x2160;OI=1;OV=8.1;CP=166;RM=4096;",
    "tecno ca8": "->camon x pro",
    "r6s": "DS=5;RS=480x854;SZ=72x145x8.6;WT=133;RM=1024;RE=2017.07;CP=86;OI=1;OV=7;",
    "tecno r6s": "->r6s",
    "spark air": "RE=2019.08;SZ=75x156.1x9.2;WT=174;DS=6.1;RS=720x1560;OI=1;OV=9;CP=44;RM=1024;",
    "tecno kc1": "->spark air",
    "spark go": "->spark air",
    "tecno kc1h": "->spark go",
    "spark 7": "RE=2021.04;SZ=76.1x164.8x9.5;WT=203;DS=6.5;RS=720x1600;OI=1;OV=11;CP=340;RM=2048;",
    "tecno kf6n": "->spark 7",
    "tecno kf6m": "->spark 7",
    "tecno kf6k": "->spark 7",
    "tecno kf6j": "->spark 7",
    "tecno kf6": "->spark 7",
    "tecno pr651h": "->spark 7",
    "tecno pr651": "->spark 7",
    "tecno kf6i": "->spark 7",
    "tecno pr651e": "->spark 7",
    "spark 7t": "RE=2021.06;SZ=76.1x164.8x9.5;WT=205;DS=6.52;RS=720x1600;OI=1;OV=11;CP=348;RM=4096;",
    "tecno kf6p": "->spark 7t",
    "spark 8p": "RE=2021.10;SZ=76.1x164.5x8.9;WT=203;DS=6.6;RS=1080x2408;OI=1;OV=11;CP=164;RM=4096;",
    "tecno kg7n": "->spark 8p",
    "tecno kg7": "->spark 8p",
    "spark 8 pro": "RE=2021.12;SZ=76.8x169x8.8;WT=;DS=6.8;RS=1080x2460;OI=1;OV=11;CP=322;RM=4096;",
    "tecno kg8": "->spark 8 pro",
    "spark 8": "RE=2021.09;SZ=76.1x164.8x9.2;WT=200;DS=6.52;RS=720x1600;OI=1;OV=11;CP=171;RM=2048;",
    "tecno kg6": "->spark 8",
    "tecno kg6k": "->spark 8",
    "spark 8t": "RE=2021.12;SZ=75.9x164.3x8.9;WT=;DS=6.6;RS=1080x2408;OI=1;OV=11;CP=348;RM=4096;",
    "tecno kg6p": "->spark 8t",
    "spark 8c": "RE=2022.01;SZ=76x164.6x8.95;WT=193;DS=6.6;RS=720x1612;OI=1;OV=11;CP=354;RM=4096;SM=2;",
    "tecno kg5n": "->spark 8c",
    "spark 7 pro": "RE=2021.04;SZ=76.2x164.9x8.8;WT=193;DS=6.6;RS=720x1600;OI=1;OV=11;CP=226;RM=4096;",
    "tecno kf8": "->spark 7 pro",
    "camon 17 pro": "RE=2021.05;SZ=77x168.9x9;WT=201;DS=6.8;RS=1080x2460;OI=1;OV=11;CP=321;RM=8192;",
    "tecno cg8": "->camon 17 pro",
    "camon 17p": "RE=2021.05;SZ=76.4x168.7x8.8;WT=;DS=6.8;RS=1080x2460;OI=1;OV=11;CP=322;RM=6144;",
    "tecno cg7n": "->camon 17p",
    "tecno cg7": "->camon 17p",
    "camon 18": "RE=2021.10;SZ=76.7x168.9x8.8;WT=;DS=6.8;RS=1080x2460;OI=1;OV=11;CP=349;RM=4096;",
    "tecno ch6n": "->camon 18",
    "camon 18p": "RE=2021.10;SZ=76.7x168.9x8.8;WT=;DS=6.8;RS=1080x2460;OI=1;OV=11;CP=350;RM=8192;",
    "tecno ch7n": "->camon 18p",
    "camon cm": "DS=5.7;RS=720x1440;SZ=71.7x152.5x5.6;WT=150;RE=2018.01;OI=1;OV=7;RM=2048;CP=9;SM=2;",
    "tecno ca6": "->camon cm",
    "camon c9": "DS=5.5;RS=1080x1920;SZ=76.4x153x10.35;WT=;RE=2016.06;OI=1;OV=6;RM=2048;CP=118;",
    "tecno-c9": "->camon c9",
    "spark 7p": "RE=2021.04;SZ=77.9x171.9x9.2;WT=;DS=6.8;RS=720x1640;OI=1;OV=11;CP=319;RM=4096;",
    "tecno kf7j": "->spark 7p",
    "pop 4 air": "RE=2021.05;DS=6.52;RS=540x1200;SZ=73.8x144x10.6;WT=183;SM=2;RM=1024;CP=341;OI=1;OV=10;",
    "tecno bc1": "->pop 4 air",
    "camon isky 3": "RE=2029.03;DS=6.2;RM=2048;CP=44;SZ=75.54x154.29x7.9;RS=720x1500;WT=150;OI=1;OV=9;SM=2;",
    "tecno kb3": "->camon isky 3",
    "spark 4 lite": "RE=2019.12;SZ=75.8x166.7x8.4;WT=;DS=6.52;RS=720x1600;OI=1;OV=9.0;CP=44;RM=2048;",
    "tecno bb4k": "->spark 4 lite",
    "pop 5": "RE=2021.07;SZ=75.7x157.7x9.6;WT=;DS=6.1;RS=720x1560;OI=1;OV=10;RM=1024;",
    "tecno bd2p": "->pop 5",
    "pop 4 lte": "->pop 4",
    "tecno bc1s": "->pop 4 lte",
    "pop 4": "RE=2020.07;SZ=77.3x160x9.5;WT=;DS=6.0;RS=480x960;OI=1;OV=10;RM=2048;CP=8;SM=2;",
    "tecno bc2c": "->pop 4",
    "pop 4 pro": "DS=6.52;RS=540x1200;SZ=77.58x167.11x9.55;RE=2020.07;OI=1;OV=10;RM=2048;CP=8;",
    "tecno bc3": "->pop 4 pro",
    "pouvoir 4": "RE=2020.07;SZ=79.6x174.9x9.2;WT=;DS=7.0;RS=720x1640;OI=1;OV=10;CP=44;RM=3072;",
    "tecno lc7s": "->pouvoir 4",
    "tecno lc7": "->pouvoir 4",
    "pouvoir 4 pro": "RE=2020.07;SZ=79.6x174.9x9.2;WT=;DS=7.0;RS=720x1640;OI=1;OV=10;CP=171;RM=4096;",
    "tecno lc8": "->pouvoir 4 pro",
    "pouvoir 3": "RE=2019.06;SZ=76.7x157.6x8.5;WT=168;DS=6.2;RS=720x1500;OI=1;OV=8.1;CP=293;RM=2048;",
    "tecno lb7": "->pouvoir 3",
    "pouvoir 3 air": "RE=2019.08;SZ=78.2x163x9.3;WT=;DS=6.0;RS=480x960;OI=1;OV=9;CP=44;RM=1024;",
    "tecno lc6a": "->pouvoir 3 air",
    "tecno lc6": "->pouvoir 3 air",
    "pouvoir 2": "RE=2018.05;SZ=76.8x159.8x8.5;WT=167;DS=6.0;RS=720x1440;OI=1;OV=8.1;RM=2048;CP=9;",
    "tecno la7": "->pouvoir 2",
    "pouvoir 2 pro": "RE=2018.08;SZ=76.8x159.8x8.2;WT=171;DS=6.0;RS=720x1440;OI=1;OV=8.1;RM=3072;CP=9;",
    "tecno la7 pro": "->pouvoir 2 pro",
    "pouvoir 1": "RE=2018.05;SZ=77.2x154.5x8.8;WT=164;DS=5.5;RS=720x1280;OI=1;OV=7;CP=336;RM=1024;",
    "tecno la6": "->pouvoir 1"
  },
  "wiko": {
    "u feel": "RE=2016.05;SZ=70.7x143x8.6;WT=145;DS=5;RS=720x1280;CP=79;RM=3072;OI=1;OV=6;",
    "fever": "RE=2015.09;SZ=73.8x148x8.3;WT=143;DS=5.2;RS=1080x1920;CP=118;RM=2048;OI=1;OV=6;",
    "barry": "RE=2014.03;SZ=73.8x145.8x9.7;WT=155;DS=5;RS=480x854;CP=24;RM=512;OI=1;OV=4;",
    "birdy": "RE=2014.05;SZ=67x132x9.6;WT=141;DS=4.5;RS=480x854;CP=7;RM=1024;OI=1;OV=4;",
    "view 3 lite": "RE=2019.05;SZ=70.7x148.4x8.5;WT=147;DS=6.09;RS=720x1520;CP=109;RM=2048;OI=1;OV=9;",
    "w-v800-tvm": "->view 3 lite",
    "w-v800-eea": "->view 3 lite",
    "w-v800-ope": "->view 3 lite",
    "view 3": "RE=2019.05;SZ=76.5x159.0x8.2;WT=178;DS=6.26;RS=720x1520;CP=171;RM=3072;OI=1;OV=9;",
    "w-p311-ope": "->view 3",
    "w-p311-eea": "->view 3",
    "y60": "RE=2019.04;SZ=71.8x146.8x9.3;WT=177.7;DS=5.45;RS=480x960;OI=1;OV=9.0;CP=293;RM=1024;TT=32000;",
    "w-k510-eea": "->y60",
    "w-k510-tvm": "->y60",
    "w-k510-sun": "->y60",
    "w-k510-ope": "->y60",
    "w-k510-byt": "->y60",
    "w-k510-th": "->y60",
    "w-k510s-swi": "->y60"
  },
  "inoi": {
    "2": "DS=5;RS=480x854;SZ=73.1x144x9.45;WT=162;RE=2018.04;OI=1;OV=7;RM=1024;CP=9;",
    "3": "DS=5;RS=480x960;SZ=67x139x10;WT=141;RE=2018.07;OI=1;OV=7;RM=1024;CP=8;",
    "3 power": "DS=5;RS=480x960;SZ=67x139x13;WT=163;RE=2018.04;OI=1;OV=7;RM=1024;CP=5;",
    "3 lite": "DS=5;RS=480x960;SZ=67x139x10;WT=141;RE=2018.04;OI=1;OV=7;RM=1024;CP=5;"
  },
  "hisense": {
    "infinity h40 rock": "SZ=76.4x166.2x9.47;WT=201;DS=6.52;RS=1080x2340;RM=4096;OI=1;OV=10;CP=290;RE=2020.04;",
    "h30 lite": "OI=1;OV=9;SZ=73.6x153.7x9;WT=156;RE=2019.07;CP=109;DS=6.1;RM=3072;"
  },
  "poco": {
    "m4 pro 5g": "RE=2021.11;SZ=75.8x163.6x8.8;WT=195;DS=6.6;RS=1080x2400;OI=1;OV=11;CP=365;RM=6144;",
    "21091116ag": "->m4 pro 5g",
    "f2 pro": "RE=2020.05;SZ=75.4x163.3x8.9;WT=219;DS=6.67;RS=1080x2400;OI=1;OV=10;CP=207;RM=6144;TT=538221;",
    "m2004j11g": "->f2 pro",
    "m3": "RE=2020.11;SZ=77.3x162.3x9.6;WT=198;DS=6.53;RS=1080x2340;OI=1;OV=10;CP=285;RM=4096;TT=177904;",
    "m2010j19cg": "->m3",
    "m2010j19ci": "->m3",
    "x3 nfc": "RE=2020.09;SZ=76.8x165.3x9.4;WT=215;DS=6.67;RS=1080x2400;OI=1;OV=10;CP=364;RM=6144;TT=283750;",
    "m2007j20ct": "->x3 nfc",
    "m2007j20cg": "->x3 nfc",
    "x4 pro 5g": "RE=2022.02;SZ=76.1x164.2x8.1;WT=205;DS=6.67;RS=1080x2400;OI=1;OV=11;CP=358;RM=6144;TT=335353;",
    "2201116pi": "->x4 pro 5g",
    "x3": "RE=2020.09;SZ=76.8x165.3x10.1;WT=225;DS=6.67;RS=1080x2400;OI=1;OV=10;CP=364;RM=6144;TT=301581;",
    "x3 sn": "->x3",
    "m2007j20ci": "->x3",
    "c3": "RE=2020.10;SZ=77.1x164.9x9;WT=194;DS=6.43;RS=720x1600;OI=1;OV=10;CP=348;RM=3072;TT=196042;",
    "m2006c3mi": "->c3",
    "m3 pro 5g": "RE=2021.05;SZ=75.3x161.8x8.9;WT=190;DS=6.5;RS=1080x2400;OI=1;OV=11;CP=302;RM=4096;TT=302680",
    "m2103k19pg": "->m3 pro 5g",
    "m2103k19py": "->m3 pro 5g",
    "camellia": "->m3 pro 5g",
    "m2103k19pi": "->m3 pro 5g",
    "c51": "RE=2023.04;SZ=76.8x164.9x9.1;WT=192;DS=6.52;RS=720x1600;OI=1;OV=13;CP=367;",
    "2305epcc4g": "->c51"
  },
  "oppo": {
    "f17": "RE=2020.09;SZ=72.8x159.8x7.5;WT=163;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=285;RM=4096;",
    "cph2095": "->f17",
    "cph2643": "->f27 pro+",
    "f27 pro+": "RE=2024.06;SZ=74.3x162.7x7.9;WT=177;DS=6.7;RS=1080x2412;OI=1;OV=14;CP=368;RM=8192;",
    "a15": "RE=2020.10;SZ=75.4x164x7.9;WT=175;DS=6.52;RS=720x1600;OI=1;OV=10;CP=38;RM=2048;",
    "a31c": "RE=2015.04;SZ=65.5x131.9x8;WT=135;DS=4.5;RS=480x854;OI=1;OV=4.4;CP=17;RM=1024;",
    "f5 youth": "RE=2017.11;SZ=76x156.5x7.5;WT=152;DS=6.0;RS=1080x2160;OI=1;OV=7.1;CP=166;RM=3072;",
    "cph1725": "->f5 youth",
    "ob-oppo a31c": "->a31c",
    "cph2185": "->a15",
    "k5": "RE=2019.10;SZ=75.2x158.7x8.6;WT=182;DS=6.4;RS=1080x2340;OI=1;OV=9.0;CP=260;RM=6144",
    "pcnm00": "->k5",
    "find x5 pro": "RE=2022.02;SZ=;WT=218;DS=6.7;RS=1440x3216;OI=1;OV=12;CP=357;RM=8192;",
    "pffm20": "->find x5 pro",
    "pfem10": "->find x5 pro",
    "find x3 pro": "RE=2021.03;SZ=74x163.6x8.3;WT=193;DS=6.7;RS=1440x3216;OI=1;OV=11;CP=305;RM=8192;",
    "cph2305": "->find x5 pro;CP=362;",
    "opg03": "->find x3 pro",
    "cph2173": "->find x3 pro",
    "peem00": "->find x3 pro",
    "find x2 pro": "RE=2020.03;SZ=74.4x165.2x8.8;WT=217;DS=6.7;RS=1440x3168;OI=1;OV=10;CP=207;RM=12288;",
    "opg01": "->find x2 pro",
    "cph2025": "->find x2 pro",
    "pdem30": "->find x2 pro",
    "find x2 lite": "RE=2020.04;SZ=74.3x160.3x8;WT=180;DS=6.4;RS=1080x2400;OI=1;OV=10;CP=242;RM=8192;",
    "cph2005": "->find x2 lite",
    "pad air": "RE=2022.05;SZ=245.1x154.8x6.9;WT=440;DS=10.36;RS=2000x1200;OI=1;OV=12;CP=363;RM=4096;",
    "opd2102a": "->pad air",
    "opd2102": "->pad air",
    "a15s": "RE=2020.12;SZ=75.4x164x7.9;WT=177;DS=6.52;RS=720x1600;OI=1;OV=10;CP=38;RM=4096;",
    "cph2179": "->a15s",
    "a11": "RE=2019.10;SZ=75.6x163.6x9.1;WT=195;DS=6.5;RS=720x1600;OI=1;OV=9;CP=283;RM=4096;",
    "pchm10": "->a11",
    "a11x": "DS=6.5;RS=720x1600;SZ=75.6x163.6x9.1;WT=195;RE=2019.10;OI=1;OV=9;RM=8192;CP=283;",
    "pchm00": "->a11x",
    "find x2": "RE=2020.03;SZ=74.5x164.9x8;WT=209;DS=6.7;RS=1440x3168;OI=1;OV=10;CP=207;RM=8192;",
    "pdem10": "->find x2",
    "cph2023": "->find x2",
    "pdet10": "->find x2",
    "find 7a": "RE=2014.03;SZ=75x152.6x9.2;WT=170;DS=5.5;RS=1080x1920;OI=1;OV=4.3;CP=139;RM=2048;",
    "x9007": "->find 7a",
    "x9006": "->find 7a",
    "x9000": "->find 7a",
    "find 7": "RE=2014.03;SZ=75x152.6x9.2;WT=171;DS=5.5;RS=1440x2560;OI=1;OV=4.3;CP=243;RM=3072;",
    "x9076": "->find 7",
    "x9070": "->find 7",
    "x9077": "->find 7",
    "a12": "RE=2020.04;SZ=75.5x155.9x8.3;WT=165;DS=6.22;RS=720x1520;OI=1;OV=9;CP=38;RM=3072;",
    "cph2077": "->a12",
    "cph2083": "->a12",
    "a3": "RE=2018.04;SZ=75.3x156x7.8;WT=159;DS=6.2;RS=1080x2280;OI=1;OV=8.1;CP=290;RM=4096;",
    "padm00": "->a3",
    "cph1837": "->a3",
    "padt00": "->a3",
    "a31": "RE=2020.02;SZ=75.5x163.9x8.3;WT=180;DS=6.5;RS=720x1600;OI=1;OV=9;CP=342;RM=4096;",
    "cph2015": "->a31",
    "cph2031": "->a31",
    "cph2073": "->a31",
    "cph2081": "->a31",
    "a3s": "RE=2017.07;SZ=75.6x156.2x8.2;WT=168;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=214;RM=2048;",
    "cph1805": "->a3s",
    "cph1803": "->a3s",
    "cph1853": "->a3s",
    "pbbm30": "->a3s",
    "a5": "RE=2017.07;SZ=75.6x156.2x8.2;WT=168;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=214;RM=3072;",
    "pbam00": "->a5",
    "pbbt30": "->a5",
    "cph1809": "->a5",
    "pbat00": "->a5",
    "ax5": "RE=2017.07;SZ=75.6x156.2x8.2;WT=168;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=214;RM=3072;",
    "cph1851": "->ax5",
    "ax5s": "RE=2019.03;SZ=75.4x155.9x8.2;WT=170;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=38;RM=2048;",
    "cph1920": "->ax5s",
    "a5 (2020)": "RE=2019.09;SZ=75.4x163.6x9.1;WT=195;DS=6.5;RS=720x1600;OI=1;OV=9;CP=283;RM=4096;",
    "cph1943": "->a5 (2020)",
    "cph1931": "->a5 (2020)",
    "cph1933": "->a5 (2020)",
    "a52": "RE=2020.04;SZ=75.5x162x8.9;WT=192;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=283;RM=4096;",
    "cph2061": "->a52",
    "pdam10": "->a52",
    "cph2069": "->a52",
    "a32": "RE=2020.09;SZ=75.1x163.9x8.4;WT=186;DS=6.5;RS=720x1600;OI=1;OV=10;CP=244;RM=4096;",
    "pdvm00": "->a32",
    "a53": "RE=2020.08;SZ=75.1x163.9x8.4;WT=186;DS=6.5;RS=720x1600;OI=1;OV=10;CP=244;RM=4096;",
    "cph2127": "->a53",
    "a57": "RE=2016.11;SZ=72.9x149.1x7.7;WT=147;DS=5.2;RS=720x1280;OI=1;OV=6;CP=35;RM=3072;",
    "cph1701": "->a57",
    "a5s": "RE=2019.03;SZ=75.4x155.9x8.2;WT=170;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=38;RM=2048;",
    "cph1912": "->a5s",
    "cph1910": "->a5s",
    "cph1909": "->a5s",
    "a7": "RE=2018.11;SZ=75.4x155.9x8.1;WT=168;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=214;RM=3072;",
    "cph1905": "->a7",
    "pbfm00": "->a7",
    "cph1901": "->a7",
    "pbft00": "->a7",
    "a7x": "RE=2018.09;SZ=74x156.7x8;WT=169;DS=6.3;RS=1080x2340;OI=1;OV=8.1;CP=290;RM=4096;",
    "pbbt00": "->a7x",
    "pbbm00": "->a7x",
    "a71": "RE=2017.09;SZ=73.8x148.1x7.6;WT=137;DS=5.2;RS=720x1280;OI=1;OV=7.1;CP=40;RM=3072;",
    "cph1717": "->a71",
    "cph1801": "->a71 (2018)",
    "a71 (2018)": "RE=2018.02;SZ=73.8x148.1x7.6;WT=137;DS=5.2;RS=720x1280;OI=1;OV=7.1;CP=214;RM=2048;",
    "a72": "RE=2020.04;SZ=75.5x162x8.9;WT=192;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=283;RM=4096;",
    "pdyt20": "->pdym20",
    "pdym20": "->a72;RE=2020.07;SZ=75x162.2x7.9;WT=175;CP=274;",
    "cph2067": "->a72",
    "a77": "RE=2017.07;SZ=75.9x153.3x7.4;WT=148;DS=5.5;RS=1080x1920;OI=1;OV=7.1;CP=204;RM=4096;",
    "cph1715": "->a77",
    "a7n": "RE=2019.04;SZ=75.4x155.9x8.2;WT=170;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=38;RM=4096;",
    "pcdm00": "->a7n",
    "pcdt00": "->a7n",
    "a83": "RE=2018.01;SZ=73.1x150.5x7.7;WT=143;DS=5.7;RS=720x1440;OI=1;OV=7.1;CP=166;RM=2048;",
    "cph1729": "->a83",
    "a83 (2018)": "->a83;SM=2;RM=3072;",
    "cph1827": "->a83 (2018)",
    "a9 (2020)": "RE=2019.09;SZ=75.6x163.6x9.1;WT=195;DS=6.5;RS=720x1600;OI=1;OV=9;CP=283;RM=4096;",
    "cph1937": "->a9 (2020)",
    "pchm30": "->a9 (2020)",
    "cph1941": "->a9 (2020)",
    "a9": "RE=2019.04;SZ=76.1x162x8.3;WT=190;DS=6.53;RS=1080x2340;OI=1;OV=9;CP=114;RM=4096;",
    "pcam10": "->a9",
    "pcat10": "->a9",
    "cph1938": "->a9",
    "a16": "RE=2021.07;SZ=75.6x163.8x8.4;WT=190;DS=6.52;RS=720x1600;OI=1;OV=11;CP=348;RM=3072;",
    "cph2269": "->a16",
    "a16s": "SZ=75.6x163.78x8.4;WT=190;RS=720x1600;DS=6.52;OI=1;OV=11;CP=348;RM=4096;RE=2021.08;",
    "cph2271": "->a16s",
    "a9x": "RE=2019.05;SZ=76.1x162x8.3;WT=190;DS=6.53;RS=1080x2340;OI=1;OV=9.0;CP=114;RM=6144;",
    "pcem00": "->a9x",
    "pcet00": "->a9x",
    "a8": "RE=2019.12;SZ=75.5x163.9x8.3;WT=180;DS=6.5;RS=720x1600;OI=1;OV=9;CP=342;RM=4096;",
    "pdbm00": "->a8",
    "a91": "RE=2019.12;SZ=73.3x160.2x7.9;WT=172;DS=6.4;RS=1080x2400;OI=1;OV=9;CP=114;RM=4096;",
    "pcpm00": "->a91",
    "cph2001": "->a91",
    "cph2021": "->a91",
    "a92": "RE=2020.05;SZ=75.5x162x8.9;WT=192;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=283;RM=6144;",
    "cph2059": "->a92",
    "a92s": "RE=2020.04;SZ=75.5x163.8x8.1;WT=184;DS=6.57;RS=1080x2400;OI=1;OV=10;CP=344;RM=6144;",
    "pdkt00": "->a92s",
    "pdkm00": "->a92s",
    "ax7": "RE=2018.11;SZ=75.4x155.9x8.1;WT=168;DS=6.2;RS=720x1520;OI=1;OV=8.1;CP=214;RM=3072;SM=2;",
    "cph1903": "->ax7",
    "f11 pro": "RE=2019.03;SZ=76.1x161.3x8.8;WT=190;DS=6.53;RS=1080x2340;OI=1;OV=9;CP=114;RM=4096;",
    "cph1969": "->f11 pro",
    "cph1987": "->f11 pro",
    "f11": "RE=2019.03;SZ=76.1x162x8.3;WT=188;DS=6.53;RS=1080x2340;OI=1;OV=9;CP=114;RM=4096;",
    "cph1911": "->f11",
    "cph1913": "->f11",
    "cph1915": "->f11",
    "r17 pro": "RE=2018.08;SZ=74.6x157.6x7.9;WT=183;DS=6.4;RS=1080x2340;OI=1;OV=8.1;CP=62;RM=6144;",
    "cph1877": "->r17 pro",
    "pbdm00": "->r17 pro",
    "pbdt00": "->r17 pro",
    "r17": "RE=2018.08;SZ=74.9x157.5x7.5;WT=182;DS=6.4;RS=1080x2340;OI=1;OV=8.1;CP=298;RM=6144;",
    "pbem00": "->r17",
    "cph1879": "->r17",
    "pbet00": "->r17",
    "f17 pro": "RE=2020.09;SZ=73.8x160.1x7.5;WT=164;DS=6.43;RS=1080x2400;OI=1;OV=10;CP=345;RM=8192;",
    "cph2119": "->f17 pro",
    "reno 2z": "RE=2019.08;SZ=75.8x162.4x8.7;WT=195;DS=6.53;RS=1080x2340;OI=1;OV=9;CP=346;RM=8192;",
    "cph1945": "->reno 2z",
    "pckm80": "->reno 2z",
    "cph1951": "->reno 2z",
    "reno 2": "RE=2019.08;SZ=74.3x160x9.5;WT=189;DS=6.5;RS=1080x2400;OI=1;OV=9;CP=260;RM=8192;",
    "cph1907": "->reno 2",
    "pckm00": "->reno 2",
    "reno 2f": "RE=2019.08;SZ=75.8x162.4x8.7;WT=195;DS=6.5;RS=1080x2340;OI=1;OV=9;CP=114;RM=8192;",
    "cph1989": "->reno 2f",
    "reno": "RE=2019.04;SZ=74.3x156.6x9;WT=185;DS=6.4;RS=1080x2340;OI=1;OV=9;CP=62;RM=6144;",
    "cph1917": "->reno",
    "pcat00": "->reno",
    "pcam00": "->reno",
    "pccm00": "->reno 10x zoom",
    "reno 10x zoom": "RE=2019.04;SZ=77.2x162x9.3;WT=210;DS=6.6;RS=1080x2340;OI=1;OV=9;CP=234;RM=6144;",
    "cph1919": "->reno 10x zoom",
    "reno 3": "RE=2020.03;SZ=73.3x160.2x7.9;WT=170;DS=6.4;RS=1080x2400;OI=1;OV=10;CP=346;RM=8192;",
    "cph2043": "->reno 3",
    "reno 3 5g": "RE=2019.12;SZ=74.3x160.3x8;WT=181;DS=6.4;RS=1080x2400;OI=1;OV=10;CP=347;RM=8192;",
    "pdcm00": "->reno 3 5g",
    "reno 3 pro": "RE=2020.03;SZ=73.4x158.8x8.1;WT=175;DS=6.4;RS=1080x2400;OI=1;OV=10;CP=345;RM=8192;",
    "cph2035": "->reno 3 pro",
    "cph2037": "->reno 3 pro",
    "cph2009": "->reno 3 pro",
    "pcrm00": "->reno 3 pro",
    "cph2036": "->reno 3 pro",
    "reno 3 lite": "RE=2020.02;SZ=74.3x160.3x8.0;WT=180;DS=6.4;RS=1080x2400;CP=242;RM=8192;OI=1;OV=10;",
    "pcrt01": "->reno 3 lite",
    "reno 3a": "SZ=74x161x8.2;WT=175;DS=6.44;CP=283;RS=1080x2400;RM=6144;OI=1;OV=10;RE-2020.06;",
    "cph2013": "->reno 3a",
    "reno 4 4g": "RE=2020.07;SZ=73.9x160.3x7.7;WT=165;DS=6.4;RS=1080x2400;OI=1;OV=10;CP=273;RM=8192",
    "cph2113": "->reno 4 4g",
    "a1k": "RE=2019.04;SZ=73.8x154.5x8.4;WT=170;DS=6.1;RS=720x1560;OI=1;OV=9;CP=171;RM=2048;",
    "cph1923": "->a1k",
    "f7": "RE=2018.03;SZ=75.3x156x7.8;WT=158;DS=6.23;RS=1080x2280;OI=1;OV=8.1;CP=290;RM=4096;",
    "cph1859": "->f7",
    "cph1821": "->f7",
    "cph1819": "->f7",
    "f9": "RE=2018.08;SZ=74x156.7x8;WT=169;DS=6.3;RS=1080x2340;OI=1;OV=8.1;CP=290;RM=4096;",
    "cph1881": "->f9",
    "cph1825": "->f9",
    "f9 pro": "->f9;RM=6144;",
    "cph1823": "->f9 pro",
    "find x": "RE=2018.06;SZ=74.2x156.7x9.4;WT=186;DS=6.42;RS=1080x2340;OI=1;OV=8.1;CP=198;RM=8192;",
    "paft00": "->find x",
    "cph1871": "->find x",
    "cph1875": "->find x",
    "pafm00": "->find x",
    "f5": "RE=2017.10;SZ=76x156.5x7.5;WT=152;DS=6.0;RS=1080x2160;OI=1;OV=7.1.1;CP=166;RM=4096;",
    "cph1723": "->f5",
    "cph1727": "->f5",
    "k1": "RE=2018.10;SZ=75.5x158.3x7.4;WT=156;DS=6.4;RS=1080x2340;OI=1;OV=8.1;CP=61;RM=4096;",
    "pbcm30": "->k1",
    "pbct10": "->k1",
    "k3": "RE=2019.05;SZ=76x161.2x9.4;WT=191;DS=6.5;RS=1080x2340;OI=1;OV=9;CP=62;RM=6144",
    "cph1955": "->k3",
    "reno 4 5g": "RE=2020.06;SZ=74x159.3x7.8;WT=183;DS=6.43;RS=1080x2400;OI=1;OV=10;CP=242;RM=8192;",
    "pdpt00": "->reno 4 5g",
    "pdpm00": "->reno 4 5g",
    "cph2091": "->reno 4 5g",
    "reno 5": "RE=2020.12;SZ=73.3x159.1x7.7;WT=171;DS=6.4;RS=1080x2400;OI=1;OV=11;CP=273;RM=8192;",
    "cph2159": "->reno 5",
    "reno 5a": "OI=1;OV=11;RM=6144;WT=182;SZ=74.60x162x8.20;DS=6.5;RS=1080x2400;CP=242;RE=2021.12;",
    "cph2199": "->reno 5a",
    "a101op": "->reno 5a",
    "reno 6": "RE=2021.07;SZ=73.3x159.1x7.8;WT=173;DS=6.4;RS=1080x2400;OI=1;OV=11;CP=273;RM=8192;",
    "cph2235": "->reno 6",
    "peqm00": "->reno 6",
    "cph2251": "->reno 6",
    "reno 6 pro": "RE=2021.05;SZ=73.1x160x7.6;WT=177;DS=6.55;RS=1080x2400;OI=1;OV=11;CP=324;RM=8192;",
    "pepm00": "->reno 6 pro",
    "reno 6 pro 5g": "RE=2021.05;SZ=73.1x160x7.6;WT=177;DS=6.55;RS=1080x2400;OI=1;OV=11;CP=324;RM=8192;",
    "cph2249": "->reno 6 pro 5g",
    "cph2247": "->reno 6 pro 5g",
    "reno 6 pro+": "->reno 6 pro plus",
    "penm00": "->reno 6 pro plus",
    "reno 6 pro plus": "RE=2021.05;SZ=72.5x160.8x8;WT=188;DS=6.55;RS=1080x2400;OI=1;OV=11;CP=304;RM=8192;",
    "reno 5z": "RE=2021.04;SZ=73.4x160.1x7.8;WT=173;DS=6.43;RS=1080x2400;OI=1;OV=11;CP=300;RM=8192;",
    "cph2211": "->reno 5z",
    "reno5 pro 5g": "->reno 5 pro 5g",
    "reno 5 pro 5g": "RE=2020.12;SZ=73.2x159.7x7.6;WT=173;DS=6.55;RS=1080x2400;OI=1;OV=11;CP=296;RM=8192;",
    "pdsm00": "->reno 5 pro 5g",
    "pdst00": "->reno 5 pro 5g",
    "cph2207": "->reno 5 pro 5g",
    "cph2201": "->reno 5 pro 5g",
    "reno 5 5g": "RE=2020.12;SZ=73.4x159.1x7.9;WT=172;DS=6.43;RS=1080x2400;OI=1;OV=11;CP=242;RM=8192;",
    "pegt00": "->reno 5 5g",
    "pegm00": "->reno 5 5g",
    "cph2145": "->reno 5 5g",
    "reno 5 lite": "RE=2021.03;SZ=73.2x160.1x7.8;WT=172;DS=6.43;RS=1080x2400;OI=1;OV=11;CP=345;RM=8192;",
    "cph2205": "->reno 5 lite",
    "reno 5 pro plus 5g": "RE=2020.12;SZ=72.5x159.9x8;WT=184;DS=6.55;RS=1080x2400;OI=1;OV=11;CP=207;RM=8192;",
    "pdrm00": "->reno 5 pro plus 5g",
    "pdrt00": "->reno 5 pro plus 5g",
    "reno 5f": "RE=2021.03;SZ=73.2x160.1x7.8;WT=172;DS=6.43;RS=1080x2400;OI=1;OV=11;CP=345;RM=8192;",
    "cph2217": "->reno 5f",
    "a94": "RE=2021.03;SZ=73.2x160.1x7.8;WT=172;DS=6.43;RS=1080x2400;OI=1;OV=11;CP=345;RM=8192;",
    "cph2203": "->a94",
    "a74 5g": "RE=2021.04;SZ=74.7x162.9x8.4;WT=190;DS=6.5;RS=1080x2400;OI=1;OV=11;CP=307;RM=6144;",
    "cph2197": "->a74 5g",
    "a54 5g": "RE=2021.04;SZ=74.7x162.9x8.4;WT=190;DS=6.5;RS=1080x2400;OI=1;OV=11;CP=307;RM=4096;",
    "cph2195": "->a54 5g",
    "cph2303": "->a54 5g",
    "opg02": "->a54 5g",
    "a55s 5g": "RE=2021.11;SZ=74.7x162.1x8.2;WT=178;DS=6.5;RS=1080x2400;CP=307;RM=4096;OI=1;OV=11;",
    "a102op": "->a55s 5g",
    "cph2309": "->a55s 5g",
    "a55": "RE=2021.10;SZ=75.7x163.6x8.4;WT=193;DS=6.51;RS=720x1600;CP=323;RM=4096;OI=1;OV=11;",
    "cph2325": "->a55",
    "a55 5g": "RE=2021.01;SZ=75.7x163.9x8.4;WT=186;DS=6.5;RS=720x1600;CP=302;RM=6144;OI=1;OV=11;",
    "pemm00": "->a55 5g",
    "pemt00": "->a55 5g",
    "pemm20": "->a55 5g",
    "r11 plus": "RE=2017.06;SZ=81.5x165.8x7.8;WT=188;DS=6.0;RS=1080x1920;OI=1;OV=7.1.1;CP=61;RM=6144;",
    "r11 pluskt": "->r11 plus",
    "r15": "RE=2018.03;SZ=75.2x155.1x7.4;WT=175;DS=6.28;RS=1080x2280;OI=1;OV=8.1;CP=290;RM=4096;",
    "pact00": "->r15",
    "cph1835": "->r15",
    "pacm00": "->r15",
    "paam00": "->r15",
    "r15 pro": "RE=2018.03;SZ=75.2x156.5x8;WT=180;DS=6.28;RS=1080x2280;OI=1;OV=8.1;CP=61;RM=6144;",
    "cph1831": "->r15 pro",
    "cph1833": "->r15 pro",
    "paat00": "->r15 pro",
    "find muse": "RE=2013.03;SZ=63.5x123x9.9;WT=;DS=4.0;RS=480x800;OI=1;OV=4.2;CP=24;RM=512;",
    "r821": "->find muse",
    "r821t": "->find muse",
    "reno 6 z 5g": "RE=2021.07;SZ=73.4x160.2x7.9;WT=173;DS=6.4;RS=1080x2400;OI=1;OV=11;CP=300;RM=8192;",
    "cph2237": "->reno 6 z 5g",
    "reno 4 pro 5g": "RE=2020.06;SZ=72.5x159.6x7.6;WT=172;DS=6.55;RS=1080x2400;OI=1;OV=10;CP=242;RM=8192;",
    "pdnt00": "->reno 4 pro 5g",
    "pdnm00": "->reno 4 pro 5g",
    "cph2089": "->reno 4 pro 5g",
    "reno 5g": "RE=2019.04;SZ=77.2x162x9.3;WT=215;DS=6.6;RS=1080x2340;OI=1;OV=9.0;CP=234;RM=8192;",
    "cph1921": "->reno 5g",
    "reno 4z 5g": "RE=2020.09;SZ=75.5x163.8x8.1;WT=184;DS=6.57;RS=1080x2400;OI=1;OV=10;CP=344;RM=8192;TT=295562;",
    "cph2065": "->reno 4z 5g",
    "reno4 z 5g": "->reno 4z 5g",
    "reno 4 se 5g": "RE=2020.09;SZ=73.9x160.5x7.8;WT=169;DS=6.43;RS=1080x2400;CP=274;RM=8192;OI=1;OV=10;TT=301000;",
    "peam00": "->reno 4 se 5g",
    "peat00": "->reno 4 se 5g",
    "k9 pro 5g": "RE=2021.09;SZ=73.5x158.7x8.5;WT=180;DS=6.43;RS=1080x2400;OI=1;OV=11;CP=324;RM=8192;",
    "peym00": "->k9 pro 5g",
    "k9 5g": "RE=2021.05;SZ=73.4x159.1x7.9;WT=172;DS=6.43;RS=1080x2400;OI=1;OV=11;CP=242;RM=8192;TT=449750;",
    "pexm00": "->k9 5g",
    "cph2123": "->a93",
    "a93": "RE=2021.01;SZ=74.7x162.9x8.4;WT=188;DS=6.5;RS=1080x2400;OI=1;OV=11;CP=307;RM=8192;",
    "pehm00": "->a93",
    "peht00": "->a93",
    "cph2099": "->a73 5g;RE=2020.10;SZ=72.9x159.8x7.5;WT=163;DS=6.44;RS=1080x2400;OI=1;OV=10;CP=285;RM=4096;",
    "a73 5g": "RE=2020.11;SZ=75x162.2x7.9;WT=177;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=274;RM=8192;",
    "cph2161": "->a73 5g",
    "find x3": "RE=2021.03;SZ=74x163.6x8.3;WT=193;DS=6.7;RS=1440x3216;OI=1;OV=11;CP=207;RM=8192;",
    "pedm00": "->find x3",
    "find x3 lite": "RE=2021.03;SZ=73.4x159.1x7.9;WT=172;DS=6.43;RS=1080x2400;OI=1;OV=11;CP=242;RM=8192;",
    "find x3 neo": "RE=2021.03;SZ=72.5x159.9x8;WT=184;DS=6.55;RS=1080x2400;OI=1;OV=11;CP=207;RM=12288;",
    "reno 8 pro": "RE=2022.07;SZ=73.4x160.6x7.7;WT=179;DS=6.4;RS=1080x2400;OI=1;OV=12;CP=369;RM=8192;TG=2694;TT=619610;",
    "cph2359": "->reno 8 pro",
    "pgam10": "->reno 8 pro;RE=2022.05;SZ=74.2x161x7.6;WT=188;DS=6.62;RS=1080x2400;OI=1;OV=12;CP=370;RM=8192;",
    "cph2357": "->reno 8 pro;RE=2022.07;SZ=74.2x161.2x7.3;WT=183;DS=6.7;RS=1080x2412;OI=1;OV=12;CP=371;RM=8192;TT=718351;TG=3395;",
    "a97": "RE=2022.07;SZ=75.1x163.8x8;WT=188;DS=6.6;RS=1080x2408;OI=1;OV=12;CP=372;RM=12288;",
    "pftm10": "->a97"
  },
  "maze": {
    "alpha": "DS=6;RS=1080x1920;SZ=82.5x159.8x8.1;WT=225;RE=2017.06;OI=1;OV=7;RM=6144;CP=343;",
    "alpha x": "DS=6;RS=1080x2160;SZ=74.6x156.4x8.1;WT=209;RE=2017.10;OI=1;OV=7;RM=6144;CP=343;",
    "comet": "DS=5.7;RS=720x1440;SZ=73.5x158.8x9.9;WT=234;RE=2017.11;OI=1;OV=7;RM=4096;CP=162;",
    "blade": "DS=5.5;RS=1080x1920;SZ=77x158.5x8;WT=190;RE=2017.04;OI=1;OV=6;RM=3072;CP=118;"
  },
  "nomi": {
    "3": "",
    "libra 4 lte": "DS=8;RS=1280x800;SZ=212x124x9.5;WT=350;OI=1;OV=8.1;RM=1024;CP=163;",
    "c080034": "->libra 4 lte"
  },
  "i-mobile": {
    "i-note 2": "RS=800x480;DS=7;CP=219;OI=1;OV=4.1;SZ=194.8x123x12.8;WT=300;"
  },
  "kiowa": {
    "s5 cristal": "RE=2018.07;WT=;DS=5;RS=480x854;OI=1;OV=8.1;CP=6;RM=1024;SM=2"
  },
  "cgv": {
    "ultimate t8": "RE=2015.04;SZ=77.1x151.4x12.2;WT=210;DS=5;RS=720x1280;OI=1;OV=8.1;CP=;RM=2048;SM=2"
  },
  "hoffmann": {
    "x play": "RE=;SZ=71.3x142.5x9.2;WT=165.3;DS=5;RS=;OI=1;OV=6;CP=;RM=2048;SM=2",
    "x-play": "->x play"
  },
  "cyrus": {
    "apel": "RE=2013;SZ=66x132x9.8;WT=130;DS=4.5;RS=540x960;OI=1;OV=4.1;CP=235;RM=512;"
  },
  "xiaomi": {
    "m2006c3mg": "->redmi 9c",
    "redmi 9c": "RE=2020.06;SZ=77x164.9x9;WT=196;DS=6.53;RS=720x1600;OI=1;OV=10;CP=348;RM=2048;",
    "22011119uy": "->redmi 10 (2022)",
    "redmi 10 (2022)": "RE=2022.02;SZ=75.5x162x8.9;WT=181;DS=6.5;RS=1080x2400;OI=1;OV=10;CP=349;RM=4096;",
    "23053rn02y": "->redmi 12",
    "redmi 12": "RE=2023.06;SZ=76.3x168.6x8.2;WT=198.5;DS=6.79;RS=1080x2460;OI=1;OV=13;CP=349;RM=2048;TT=258006;TG=1303;",
    "redmi 9c nfc": "RE=2020.08;SZ=77.1x164.9x9;WT=196;DS=6.53;RS=720x1600;OI=1;OV=10;CP=348;RM=2048;",
    "m2006c3mng": "->redmi 9c nfc",
    "redmi note 8 pro": "RE=2019.08;SZ=76.4x161.4x8.8;WT=200;DS=6.53;RS=1080x2340;OI=1;OV=9;CP=325;GP=120;RM=4096;TT=224759;TG=6999;",
    "redmi note 9 pro": "RE=2020.04;SZ=76.7x165.8x8.8;WT=209;DS=6.67;RS=1080x2400;OI=1;OV=10;CP=273;RM=6144;TT=279625;TG=1785;",
    "m2003j6b2g": "->redmi note 9 pro",
    "redmi 10": "RE=2021.08;SZ=75.5x162x8.9;WT=181;DS=6.5;RS=1080x2400;OI=1;OV=11;CP=349;RM=4096;",
    "21061119dg": "->redmi 10",
    "redmi 10c": "RE=2022.03;SZ=76.6x169.6x8.3;WT=190;DS=6.71;RS=720x1650;OI=1;OV=11;CP=363;RM=3072;",
    "220333qny": "->redmi 10c",
    "mi 11 lite": "RE=2021.03;SZ=75.7x160.5x6.8;WT=157;DS=6.55;RS=1080x2400;OI=1;OV=11;CP=364;RM=4096;TT=290172;TG=1796;",
    "m2101k9ag": "->mi 11 lite"
  }
};
