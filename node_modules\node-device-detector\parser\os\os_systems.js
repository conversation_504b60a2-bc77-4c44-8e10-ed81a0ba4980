// prettier-ignore
module.exports = {
  'AIX': 'AIX',
  'AND': 'Android',
  'ADR': 'Android TV',
  'ALP': 'Alpine Linux',
  'AMZ': 'Amazon Linux',
  'AMG': 'AmigaOS',
  'ARM': 'Armadillo OS',
  'ARO': 'AROS',
  'ATV': 'tvOS',
  'ARL': 'Arch Linux',
  'AOS': 'AOSC OS',
  'ASP': 'ASPLinux',
  'AZU': 'Azure Linux',
  'BTR': 'BackTrack',
  'SBA': 'Bada',
  'BYI': '<PERSON><PERSON>',
  'BEO': 'BeOS',
  'BLB': 'BlackBerry OS',
  'QNX': 'BlackBerry Tablet OS',
  'PAN': 'blackPanther OS',
  'BOS': 'Bliss OS',
  'BMP': 'Brew',
  'BSN': 'BrightSignOS',
  'CAI': 'Caixa Mágica',
  'CES': 'CentOS',
  'CST': 'CentOS Stream',
  'CLO': 'Clear Linux OS',
  'CLR': 'ClearOS Mobile',
  'COS': 'Chrome OS',
  'CRS': 'Chromium OS',
  'CHN': 'China OS',
  'COL': 'Coolita OS',
  'CYN': 'CyanogenMod',
  'DEB': 'Debian',
  'DEE': 'Deepin',
  'DFB': 'DragonFly',
  'DVK': 'DVKBuntu',
  'ELE': 'ElectroBSD',
  'EUL': 'EulerOS',
  'FED': 'Fedora',
  'FEN': 'Fenix',
  'FOS': 'Firefox OS',
  'FIR': 'Fire OS',
  'FOR': 'Foresight Linux',
  'FRE': 'Freebox',
  'BSD': 'FreeBSD',
  'FRI': 'FRITZ!OS',
  'FYD': 'FydeOS',
  'FUC': 'Fuchsia',
  'GNT': 'Gentoo',
  'GNX': 'GENIX',
  'GEO': 'GEOS',
  'GNS': 'gNewSense',
  'GRI': 'GridOS',
  'GTV': 'Google TV',
  'HPX': 'HP-UX',
  'HAI': 'Haiku OS',
  'IPA': 'iPadOS',
  'HAR': 'HarmonyOS',
  'HAS': 'HasCodingOS',
  'HEL': 'HELIX OS',
  'IRI': 'IRIX',
  'INF': 'Inferno',
  'JME': 'Java ME',
  'JOL': 'Joli OS',
  'KOS': 'KaiOS',
  'KAL': 'Kali',
  'KAN': 'Kanotix',
  'KIN': 'KIN OS',
  'KNO': 'Knoppix',
  'KTV': 'KreaTV',
  'KBT': 'Kubuntu',
  'LIN': 'GNU/Linux',
  'LEA': 'LeafOS',
  'LND': 'LindowsOS',
  'LNS': 'Linspire',
  'LEN': 'Lineage OS',
  'LIR': 'Liri OS',
  'LOO': 'Loongnix',
  'LBT': 'Lubuntu',
  'LOS': 'Lumin OS',
  'LUN': 'LuneOS',
  'VLN': 'VectorLinux',
  'MAC': 'Mac',
  'MAE': 'Maemo',
  'MAG': 'Mageia',
  'MDR': 'Mandriva',
  'SMG': 'MeeGo',
  'MET': 'Meta Horizon',
  'MCD': 'MocorDroid',
  'MON': 'moonOS',
  'EZX': 'Motorola EZX',
  'MIN': 'Mint',
  'MLD': 'MildWild',
  'MOR': 'MorphOS',
  'NBS': 'NetBSD',
  'MTK': 'MTK / Nucleus',
  'MRE': 'MRE',
  'NXT': 'NeXTSTEP',
  'NWS': 'NEWS-OS',
  'WII': 'Nintendo',
  'NDS': 'Nintendo Mobile',
  'NOV': 'Nova',
  'OS2': 'OS/2',
  'T64': 'OSF1',
  'OBS': 'OpenBSD',
  'OVS': 'OpenVMS',
  'OVZ': 'OpenVZ',
  'OWR': 'OpenWrt',
  'OTV': 'Opera TV',
  'ORA': 'Oracle Linux',
  'ORD': 'Ordissimo',
  'PAR': 'Pardus',
  'PCL': 'PCLinuxOS',
  'PIC': 'PICO OS',
  'PLA': 'Plasma Mobile',
  'PSP': 'PlayStation Portable',
  'PS3': 'PlayStation',
  'PVE': 'Proxmox VE',
  'PUF': 'Puffin OS',
  'PUR': 'PureOS',
  'QTP': 'Qtopia',
  'PIO': 'Raspberry Pi OS',
  'RAS': 'Raspbian',
  'RHT': 'Red Hat',
  'RST': 'Red Star',
  'RED': 'RedOS',
  'REV': 'Revenge OS',
  'RIS': 'risingOS',
  'ROS': 'RISC OS',
  'ROC': 'Rocky Linux',
  'ROK': 'Roku OS',
  'RSO': 'Rosa',
  'ROU': 'RouterOS',
  'REM': 'Remix OS',
  'RRS': 'Resurrection Remix OS',
  'REX': 'REX',
  'RZD': 'RazoDroiD',
  'RXT': 'RTOS & Next',
  'SAB': 'Sabayon',
  'SSE': 'SUSE',
  'SAF': 'Sailfish OS',
  'SCI': 'Scientific Linux',
  'SEE': 'SeewoOS',
  'SER': 'SerenityOS',
  'SIR': 'Sirin OS',
  'SLW': 'Slackware',
  'SOS': 'Solaris',
  'SBL': 'Star-Blade OS',
  'SYL': 'Syllable',
  'SYM': 'Symbian',
  'SYS': 'Symbian OS',
  'S40': 'Symbian OS Series 40',
  'S60': 'Symbian OS Series 60',
  'SY3': 'Symbian^3',
  'TEN': 'TencentOS',
  'TDX': 'ThreadX',
  'TIZ': 'Tizen',
  'TIV': 'TiVo OS',
  'TOS': 'TmaxOS',
  'TUR': 'Turbolinux',
  'UBT': 'Ubuntu',
  'ULT': 'ULTRIX',
  'UOS': 'UOS',
  'VID': 'VIDAA',
  'VIZ': 'ViziOS',
  'WAS': 'watchOS',
  'WER': 'Wear OS',
  'WTV': 'WebTV',
  'WHS': 'Whale OS',
  'WIN': 'Windows',
  'WCE': 'Windows CE',
  'WIO': 'Windows IoT',
  'WMO': 'Windows Mobile',
  'WPH': 'Windows Phone',
  'WRT': 'Windows RT',
  'WPO': 'WoPhone',
  'XBX': 'Xbox',
  'XBT': 'Xubuntu',
  'YNS': 'YunOS',
  'ZEN': 'Zenwalk',
  'ZOR': 'ZorinOS',
  'IOS': 'iOS',
  'POS': 'palmOS',
  'WEB': 'Webian',
  'WOS': 'webOS',
  
};
