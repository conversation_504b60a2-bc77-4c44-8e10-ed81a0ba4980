{"name": "mailtrap", "description": "Official mailtrap.io API client", "version": "4.1.0", "author": "Railsware Products Studio LLC", "dependencies": {"axios": ">=0.27"}, "devDependencies": {"@babel/core": "^7.20.5", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "@jest/globals": "^29.3.1", "@types/jest": "^29.5.3", "@types/node": "^18.15.11", "@types/nodemailer": "^6.4.9", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "axios-mock-adapter": "^1.21.2", "babel-jest": "^29.3.1", "eslint": "^7.32.0 || ^8.2.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.3.1", "nodemailer": "^6.9.4", "prettier": "^2.6.2", "ts-node": "^10.2.1", "typescript": "^5.0.3"}, "engines": {"node": ">=16.20.1", "yarn": ">=1.22.17"}, "files": ["dist/**/*"], "license": "MIT", "main": "dist/index.js", "peerDependencies": {"@types/nodemailer": "^6.4.9", "nodemailer": "^6.9.4"}, "peerDependenciesMeta": {"nodemailer": {"optional": true}, "@types/nodemailer": {"optional": true}}, "repository": "https://github.com/railsware/mailtrap-nodejs", "scripts": {"lint": "yarn lint:eslint && yarn lint:tsc", "lint:eslint": "yarn run eslint . --ext .js,.ts", "lint:tsc": "tsc -p . --noEmit --incremental false", "prepublish": "rm -rf dist && tsc --project tsconfig.build.json", "test": "jest --verbose", "coverage": "jest --coverage"}, "types": "dist/index.d.ts"}