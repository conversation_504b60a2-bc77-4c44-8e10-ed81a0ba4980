<!doctype html>
<html>
  <title>npm-view</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/api/npm-view.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../api/npm-view.html">npm-view</a></h1> <p>View registry info</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm.commands.view(args, [silent,] callback)
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>This command shows data about a package and prints it to the stream
referenced by the <code>outfd</code> config, which defaults to stdout.</p>
<p>The &quot;args&quot; parameter is an ordered list that closely resembles the command-line
usage. The elements should be ordered such that the first element is
the package and version (package@version). The version is optional. After that,
the rest of the parameters are fields with optional subfields (&quot;field.subfield&quot;)
which can be used to get only the information desired from the registry.</p>
<p>The callback will be passed all of the data returned by the query.</p>
<p>For example, to get the package registry entry for the <code>connect</code> package,
you can do this:</p>
<pre><code>npm.commands.view([&quot;connect&quot;], callback)
</code></pre><p>If no version is specified, &quot;latest&quot; is assumed.</p>
<p>Field names can be specified after the package descriptor.
For example, to show the dependencies of the <code>ronn</code> package at version
0.3.5, you could do the following:</p>
<pre><code>npm.commands.view([&quot;ronn@0.3.5&quot;, &quot;dependencies&quot;], callback)
</code></pre><p>You can view child field by separating them with a period.
To view the git repository URL for the latest version of npm, you could
do this:</p>
<pre><code>npm.commands.view([&quot;npm&quot;, &quot;repository.url&quot;], callback)
</code></pre><p>For fields that are arrays, requesting a non-numeric field will return
all of the values from the objects in the list.  For example, to get all
the contributor names for the &quot;express&quot; project, you can do this:</p>
<pre><code>npm.commands.view([&quot;express&quot;, &quot;contributors.email&quot;], callback)
</code></pre><p>You may also use numeric indices in square braces to specifically select
an item in an array field.  To just get the email address of the first
contributor in the list, you can do this:</p>
<pre><code>npm.commands.view([&quot;express&quot;, &quot;contributors[0].email&quot;], callback)
</code></pre><p>Multiple fields may be specified, and will be printed one after another.
For exampls, to get all the contributor names and email addresses, you
can do this:</p>
<pre><code>npm.commands.view([&quot;express&quot;, &quot;contributors.name&quot;, &quot;contributors.email&quot;], callback)
</code></pre><p>&quot;Person&quot; fields are shown as a string if they would be shown as an
object.  So, for example, this will show the list of npm contributors in
the shortened string format.  (See <code>npm help json</code> for more on this.)</p>
<pre><code>npm.commands.view([&quot;npm&quot;, &quot;contributors&quot;], callback)
</code></pre><p>If a version range is provided, then data will be printed for every
matching version of the package.  This will show which version of jsdom
was required by each matching version of yui3:</p>
<pre><code>npm.commands.view([&quot;yui3@&gt;0.5.4&quot;, &quot;dependencies.jsdom&quot;], callback)
</code></pre><h2 id="output">OUTPUT</h2>
<p>If only a single string field for a single version is output, then it
will not be colorized or quoted, so as to enable piping the output to
another command.</p>
<p>If the version range matches multiple versions, than each printed value
will be prefixed with the version it applies to.</p>
<p>If multiple fields are requested, than each of them are prefixed with
the field name.</p>
<p>Console output can be disabled by setting the &#39;silent&#39; parameter to true.</p>
<h2 id="return-value">RETURN VALUE</h2>
<p>The data returned will be an object in this formation:</p>
<pre><code>{ &lt;version&gt;:
  { &lt;field&gt;: &lt;value&gt;
  , ... }
, ... }
</code></pre><p>corresponding to the list of fields selected.</p>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-view &mdash; npm@2.15.12</p>

