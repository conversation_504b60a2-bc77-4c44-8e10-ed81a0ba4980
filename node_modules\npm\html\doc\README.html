<!doctype html>
<html>
  <title>README</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/README.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="cli/npm.html">npm</a></h1> <p>a JavaScript package manager</p>
<p><a href="https://travis-ci.org/npm/npm"><img src="https://img.shields.io/travis/npm/npm/master.svg" alt="Build Status"></a></p>
<h2 id="synopsis">SYNOPSIS</h2>
<p>This is just enough info to get you up and running.</p>
<p>Much more info available via <code>npm help</code> once it&#39;s installed.</p>
<h2 id="important">IMPORTANT</h2>
<p><strong>You need node v0.10 or higher to run this program.</strong></p>
<p>To install an old <strong>and unsupported</strong> version of npm that works on node 0.3
and prior, clone the git repo and dig through the old tags and branches.</p>
<p><strong>npm is configured to use npm, Inc.&#39;s public package registry at
<a href="https://registry.npmjs.org">https://registry.npmjs.org</a> by default.</strong></p>
<p>You can configure npm to use any compatible registry you
like, and even run your own registry. Check out the <a href="https://docs.npmjs.com/misc/registry">doc on
registries</a>.</p>
<p>Use of someone else&#39;s registry may be governed by terms of use. The
terms of use for the default public registry are available at
<a href="https://www.npmjs.com">https://www.npmjs.com</a>.</p>
<h2 id="super-easy-install">Super Easy Install</h2>
<p>npm is bundled with <a href="http://nodejs.org/download/">node</a>.</p>
<h3 id="windows-computers">Windows Computers</h3>
<p><a href="http://nodejs.org/download/">Get the MSI</a>.  npm is in it.</p>
<h3 id="apple-macintosh-computers">Apple Macintosh Computers</h3>
<p><a href="http://nodejs.org/download/">Get the pkg</a>.  npm is in it.</p>
<h3 id="other-sorts-of-unices">Other Sorts of Unices</h3>
<p>Run <code>make install</code>.  npm will be installed with node.</p>
<p>If you want a more fancy pants install (a different version, customized
paths, etc.) then read on.</p>
<h2 id="fancy-install-unix-">Fancy Install (Unix)</h2>
<p>There&#39;s a pretty robust install script at
<a href="https://www.npmjs.com/install.sh">https://www.npmjs.com/install.sh</a>.  You can download that and run it.</p>
<p>Here&#39;s an example using curl:</p>
<pre><code class="lang-sh">curl -L https://www.npmjs.com/install.sh | sh
</code></pre>
<h3 id="slightly-fancier">Slightly Fancier</h3>
<p>You can set any npm configuration params with that script:</p>
<pre><code class="lang-sh">npm_config_prefix=/some/path sh install.sh
</code></pre>
<p>Or, you can run it in uber-debuggery mode:</p>
<pre><code class="lang-sh">npm_debug=1 sh install.sh
</code></pre>
<h3 id="even-fancier">Even Fancier</h3>
<p>Get the code with git.  Use <code>make</code> to build the docs and do other stuff.
If you plan on hacking on npm, <code>make link</code> is your friend.</p>
<p>If you&#39;ve got the npm source code, you can also semi-permanently set
arbitrary config keys using the <code>./configure --key=val ...</code>, and then
run npm commands by doing <code>node cli.js &lt;cmd&gt; &lt;args&gt;</code>.  (This is helpful
for testing, or running stuff without actually installing npm itself.)</p>
<h2 id="windows-install-or-upgrade">Windows Install or Upgrade</h2>
<p>You can download a zip file from <a href="https://github.com/npm/npm/releases">https://github.com/npm/npm/releases</a>, and
unpack it in the <code>node_modules\npm\</code> folder inside node&#39;s installation folder.</p>
<p>To upgrade to npm 2, follow the Windows upgrade instructions in
the npm Troubleshooting Guide:</p>
<p><a href="https://github.com/npm/npm/wiki/Troubleshooting#upgrading-on-windows">https://github.com/npm/npm/wiki/Troubleshooting#upgrading-on-windows</a></p>
<p>If that&#39;s not fancy enough for you, then you can fetch the code with
git, and mess with it directly.</p>
<h2 id="installing-on-cygwin">Installing on Cygwin</h2>
<p>No.</p>
<h2 id="uninstalling">Uninstalling</h2>
<p>So sad to see you go.</p>
<pre><code class="lang-sh">sudo npm uninstall npm -g
</code></pre>
<p>Or, if that fails,</p>
<pre><code class="lang-sh">sudo make uninstall
</code></pre>
<h2 id="more-severe-uninstalling">More Severe Uninstalling</h2>
<p>Usually, the above instructions are sufficient.  That will remove
npm, but leave behind anything you&#39;ve installed.</p>
<p>If you would like to remove all the packages that you have installed,
then you can use the <code>npm ls</code> command to find them, and then <code>npm rm</code> to
remove them.</p>
<p>To remove cruft left behind by npm 0.x, you can use the included
<code>clean-old.sh</code> script file.  You can run it conveniently like this:</p>
<pre><code class="lang-sh">npm explore npm -g -- sh scripts/clean-old.sh
</code></pre>
<p>npm uses two configuration files, one for per-user configs, and another
for global (every-user) configs.  You can view them by doing:</p>
<pre><code class="lang-sh">npm config get userconfig   # defaults to ~/.npmrc
npm config get globalconfig # defaults to /usr/local/etc/npmrc
</code></pre>
<p>Uninstalling npm does not remove configuration files by default.  You
must remove them yourself manually if you want them gone.  Note that
this means that future npm installs will not remember the settings that
you have chosen.</p>
<h2 id="more-docs">More Docs</h2>
<p>Check out the <a href="https://docs.npmjs.com/">docs</a>,
especially the <a href="https://docs.npmjs.com/misc/faq">faq</a>.</p>
<p>You can use the <code>npm help</code> command to read any of them.</p>
<p>If you&#39;re a developer, and you want to use npm to publish your program,
you should <a href="https://docs.npmjs.com/misc/developers">read this</a></p>
<h2 id="bugs">BUGS</h2>
<p>When you find issues, please report them:</p>
<ul>
<li>web:
<a href="https://github.com/npm/npm/issues">https://github.com/npm/npm/issues</a></li>
</ul>
<p>Be sure to include <em>all</em> of the output from the npm command that didn&#39;t work
as expected.  The <code>npm-debug.log</code> file is also helpful to provide.</p>
<p>You can also look for isaacs in #node.js on irc://irc.freenode.net.  He
will no doubt tell you to put the output in a gist or email.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="cli/npm.html">npm(1)</a></li>
<li><a href="misc/npm-faq.html">npm-faq(7)</a></li>
<li><a href="cli/npm-help.html">npm-help(1)</a></li>
<li><a href="misc/npm-index.html">npm-index(7)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer"><a href="../doc/README.html">README</a> &mdash; npm@2.15.12</p>

