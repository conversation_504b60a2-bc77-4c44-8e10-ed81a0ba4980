{"name": "acme-client", "description": "Simple and unopinionated ACME client", "author": "<PERSON><PERSON><PERSON>", "version": "5.4.0", "main": "src/index.js", "types": "types/index.d.ts", "license": "MIT", "homepage": "https://github.com/publishlab/node-acme-client", "engines": {"node": ">= 16"}, "files": ["src", "types"], "dependencies": {"@peculiar/x509": "^1.11.0", "asn1js": "^3.0.5", "axios": "^1.7.2", "debug": "^4.3.5", "node-forge": "^1.3.1"}, "devDependencies": {"@types/node": "^20.14.10", "chai": "^4.4.1", "chai-as-promised": "^7.1.2", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.1", "jsdoc-to-markdown": "^8.0.1", "mocha": "^10.6.0", "nock": "^13.5.4", "tsd": "^0.31.1"}, "scripts": {"build-docs": "jsdoc2md src/client.js > docs/client.md && jsdoc2md src/crypto/index.js > docs/crypto.md && jsdoc2md src/crypto/forge.js > docs/forge.md", "lint": "eslint .", "lint-types": "tsd", "prepublishOnly": "npm run build-docs", "test": "mocha -t 60000 \"test/setup.js\" \"test/**/*.spec.js\""}, "repository": {"type": "git", "url": "https://github.com/publishlab/node-acme-client"}, "keywords": ["acme", "client", "lets", "encrypt", "acmev2", "boulder"], "bugs": {"url": "https://github.com/publishlab/node-acme-client/issues"}}