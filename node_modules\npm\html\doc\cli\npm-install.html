<!doctype html>
<html>
  <title>npm-install</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-install.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-install.html">npm-install</a></h1> <p>Install a package</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm install (with no args in a package dir)
npm install &lt;tarball file&gt;
npm install &lt;tarball url&gt;
npm install &lt;folder&gt;
npm install [@&lt;scope&gt;/]&lt;name&gt; [--save|--save-dev|--save-optional] [--save-exact] [--save-bundle]
npm install [@&lt;scope&gt;/]&lt;name&gt;@&lt;tag&gt;
npm install [@&lt;scope&gt;/]&lt;name&gt;@&lt;version&gt;
npm install [@&lt;scope&gt;/]&lt;name&gt;@&lt;version range&gt;
npm i (with any of the previous argument usage)
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>This command installs a package, and any packages that it depends on. If the
package has a shrinkwrap file, the installation of dependencies will be driven
by that. See <a href="../cli/npm-shrinkwrap.html">npm-shrinkwrap(1)</a>.</p>
<p>A <code>package</code> is:</p>
<ul>
<li>a) a folder containing a program described by a <code><a href="../files/package.json.html">package.json(5)</a></code> file</li>
<li>b) a gzipped tarball containing (a)</li>
<li>c) a url that resolves to (b)</li>
<li>d) a <code>&lt;name&gt;@&lt;version&gt;</code> that is published on the registry (see <code><a href="../misc/npm-registry.html">npm-registry(7)</a></code>) with (c)</li>
<li>e) a <code>&lt;name&gt;@&lt;tag&gt;</code> (see <code><a href="../cli/npm-dist-tag.html">npm-dist-tag(1)</a></code>) that points to (d)</li>
<li>f) a <code>&lt;name&gt;</code> that has a &quot;latest&quot; tag satisfying (e)</li>
<li>g) a <code>&lt;git remote url&gt;</code> that resolves to (b)</li>
</ul>
<p>Even if you never publish your package, you can still get a lot of
benefits of using npm if you just want to write a node program (a), and
perhaps if you also want to be able to easily install it elsewhere
after packing it up into a tarball (b).</p>
<ul>
<li><p><code>npm install</code> (in package directory, no arguments):</p>
<p>  Install the dependencies in the local node_modules folder.</p>
<p>  In global mode (ie, with <code>-g</code> or <code>--global</code> appended to the command),
  it installs the current package context (ie, the current working
  directory) as a global package.</p>
<p>  By default, <code>npm install</code> will install all modules listed as dependencies
  in <code><a href="../files/package.json.html">package.json(5)</a></code>.</p>
<p>  With the <code>--production</code> flag (or when the <code>NODE_ENV</code> environment variable
  is set to <code>production</code>), npm will not install modules listed in
  <code>devDependencies</code>.</p>
</li>
<li><p><code>npm install &lt;folder&gt;</code>:</p>
<p>  Install a package that is sitting in a folder on the filesystem.</p>
</li>
<li><p><code>npm install &lt;tarball file&gt;</code>:</p>
<p>  Install a package that is sitting on the filesystem.  Note: if you just want
  to link a dev directory into your npm root, you can do this more easily by
  using <code>npm link</code>.</p>
<p>  Example:</p>
<pre><code>    npm install ./package.tgz
</code></pre></li>
<li><p><code>npm install &lt;tarball url&gt;</code>:</p>
<p>  Fetch the tarball url, and then install it.  In order to distinguish between
  this and other options, the argument must start with &quot;http://&quot; or &quot;https://&quot;</p>
<p>  Example:</p>
<pre><code>    npm install https://github.com/indexzero/forever/tarball/v0.5.6
</code></pre></li>
<li><p><code>npm install [@&lt;scope&gt;/]&lt;name&gt; [--save|--save-dev|--save-optional]</code>:</p>
<p>  Do a <code>&lt;name&gt;@&lt;tag&gt;</code> install, where <code>&lt;tag&gt;</code> is the &quot;tag&quot; config. (See
  <code><a href="../misc/npm-config.html">npm-config(7)</a></code>. The config&#39;s default value is <code>latest</code>.)</p>
<p>  In most cases, this will install the latest version
  of the module published on npm.</p>
<p>  Example:</p>
<pre><code>    npm install sax
</code></pre><p>  <code>npm install</code> takes 3 exclusive, optional flags which save or update
  the package version in your main package.json:</p>
<ul>
<li><p><code>--save</code>: Package will appear in your <code>dependencies</code>.</p>
</li>
<li><p><code>--save-dev</code>: Package will appear in your <code>devDependencies</code>.</p>
</li>
<li><p><code>--save-optional</code>: Package will appear in your <code>optionalDependencies</code>.</p>
<p>When using any of the above options to save dependencies to your
package.json, there are two additional, optional flags:</p>
</li>
<li><p><code>--save-exact</code>: Saved dependencies will be configured with an
exact version rather than using npm&#39;s default semver range
operator.</p>
</li>
<li><p><code>-B, --save-bundle</code>: Saved dependencies will also be added to your <code>bundleDependencies</code> list.</p>
<p>Note: if you do not include the @-symbol on your scope name, npm will
interpret this as a GitHub repository instead, see below. Scopes names
must also be followed by a slash.</p>
<p>Examples:</p>
<pre><code>npm install sax --save
npm install githubname/reponame
npm install @myorg/privatepackage
npm install node-tap --save-dev
npm install dtrace-provider --save-optional
npm install readable-stream --save --save-exact
npm install ansi-regex --save --save-bundle
</code></pre></li>
</ul>
</li>
</ul>
<pre><code>**Note**: If there is a file or folder named `&lt;name&gt;` in the current
working directory, then it will try to install that, and only try to
fetch the package by name if it is not valid.
</code></pre><ul>
<li><p><code>npm install [@&lt;scope&gt;/]&lt;name&gt;@&lt;tag&gt;</code>:</p>
<p>  Install the version of the package that is referenced by the specified tag.
  If the tag does not exist in the registry data for that package, then this
  will fail.</p>
<p>  Example:</p>
<pre><code>    npm install sax@latest
    npm install @myorg/mypackage@latest
</code></pre></li>
<li><p><code>npm install [@&lt;scope&gt;/]&lt;name&gt;@&lt;version&gt;</code>:</p>
<p>  Install the specified version of the package.  This will fail if the
  version has not been published to the registry.</p>
<p>  Example:</p>
<pre><code>    npm install sax@0.1.1
    npm install @myorg/privatepackage@1.5.0
</code></pre></li>
<li><p><code>npm install [@&lt;scope&gt;/]&lt;name&gt;@&lt;version range&gt;</code>:</p>
<p>  Install a version of the package matching the specified version range.  This
  will follow the same rules for resolving dependencies described in <code><a href="../files/package.json.html">package.json(5)</a></code>.</p>
<p>  Note that most version ranges must be put in quotes so that your shell will
  treat it as a single argument.</p>
<p>  Example:</p>
<pre><code>    npm install sax@&quot;&gt;=0.1.0 &lt;0.2.0&quot;
    npm install @myorg/privatepackage@&quot;&gt;=0.1.0 &lt;0.2.0&quot;
</code></pre></li>
<li><p><code>npm install &lt;git remote url&gt;</code>:</p>
<p>  Install a package by cloning a git remote url.  The format of the git
  url is:</p>
<pre><code>    &lt;protocol&gt;://[&lt;user&gt;[:&lt;password&gt;]@]&lt;hostname&gt;[:&lt;port&gt;][:/]&lt;path&gt;[#&lt;commit-ish&gt;]
</code></pre><p>  <code>&lt;protocol&gt;</code> is one of <code>git</code>, <code>git+ssh</code>, <code>git+http</code>, or
  <code>git+https</code>.  If no <code>&lt;commit-ish&gt;</code> is specified, then <code>master</code> is
  used.</p>
<p>  The following git environment variables are recognized by npm and will be added
  to the environment when running git:</p>
<ul>
<li><code>GIT_ASKPASS</code></li>
<li><code>GIT_EXEC_PATH</code></li>
<li><code>GIT_PROXY_COMMAND</code></li>
<li><code>GIT_SSH</code></li>
<li><code>GIT_SSH_COMMAND</code></li>
<li><code>GIT_SSL_CAINFO</code></li>
<li><p><code>GIT_SSL_NO_VERIFY</code></p>
<p>See the git man page for details.</p>
<p>Examples:</p>
<pre><code>npm install git+ssh://**************:npm/npm.git#v1.0.27
npm install git+https://<EMAIL>/npm/npm.git
npm install git://github.com/npm/npm.git#v1.0.27
GIT_SSH_COMMAND=&#39;ssh -i ~/.ssh/custom_ident&#39; npm install git+ssh://**************:npm/npm.git
</code></pre></li>
</ul>
</li>
<li><p><code>npm install &lt;githubname&gt;/&lt;githubrepo&gt;[#&lt;commit-ish&gt;]</code>:</p>
</li>
<li><p><code>npm install github:&lt;githubname&gt;/&lt;githubrepo&gt;[#&lt;commit-ish&gt;]</code>:</p>
<p>  Install the package at <code>https://github.com/githubname/githubrepo</code> by
  attempting to clone it using <code>git</code>.</p>
<p>  If you don&#39;t specify a <em>commit-ish</em> then <code>master</code> will be used.</p>
<p>  Examples:</p>
<pre><code>    npm install mygithubuser/myproject
    npm install github:mygithubuser/myproject
</code></pre></li>
<li><p><code>npm install gist:[&lt;githubname&gt;/]&lt;gistID&gt;[#&lt;commit-ish&gt;]</code>:</p>
<p>  Install the package at <code>https://gist.github.com/gistID</code> by attempting to
  clone it using <code>git</code>. The GitHub username associated with the gist is
  optional and will not be saved in <code>package.json</code> if <code>--save</code> is used.</p>
<p>  If you don&#39;t specify a <em>commit-ish</em> then <code>master</code> will be used.</p>
<p>  Example:</p>
<pre><code>    npm install gist:101a11beef
</code></pre></li>
<li><p><code>npm install bitbucket:&lt;bitbucketname&gt;/&lt;bitbucketrepo&gt;[#&lt;commit-ish&gt;]</code>:</p>
<p>  Install the package at <code>https://bitbucket.org/bitbucketname/bitbucketrepo</code>
  by attempting to clone it using <code>git</code>.</p>
<p>  If you don&#39;t specify a <em>commit-ish</em> then <code>master</code> will be used.</p>
<p>  Example:</p>
<pre><code>    npm install bitbucket:mybitbucketuser/myproject
</code></pre></li>
<li><p><code>npm install gitlab:&lt;gitlabname&gt;/&lt;gitlabrepo&gt;[#&lt;commit-ish&gt;]</code>:</p>
<p>  Install the package at <code>https://gitlab.com/gitlabname/gitlabrepo</code>
  by attempting to clone it using <code>git</code>.</p>
<p>  If you don&#39;t specify a <em>commit-ish</em> then <code>master</code> will be used.</p>
<p>  Example:</p>
<pre><code>    npm install gitlab:mygitlabuser/myproject
</code></pre></li>
</ul>
<p>You may combine multiple arguments, and even multiple types of arguments.
For example:</p>
<pre><code>npm install sax@&quot;&gt;=0.1.0 &lt;0.2.0&quot; bench supervisor
</code></pre><p>The <code>--tag</code> argument will apply to all of the specified install targets. If a
tag with the given name exists, the tagged version is preferred over newer
versions.</p>
<p>The <code>--force</code> argument will force npm to fetch remote resources even if a
local copy exists on disk.</p>
<pre><code>npm install sax --force
</code></pre><p>The <code>--global</code> argument will cause npm to install the package globally
rather than locally.  See <code><a href="../files/npm-folders.html">npm-folders(5)</a></code>.</p>
<p>The <code>--ignore-scripts</code> argument will cause npm to not execute any
scripts defined in the package.json. See <code><a href="../misc/npm-scripts.html">npm-scripts(7)</a></code>.</p>
<p>The <code>--link</code> argument will cause npm to link global installs into the
local space in some cases.</p>
<p>The <code>--no-bin-links</code> argument will prevent npm from creating symlinks for
any binaries the package might contain.</p>
<p>The <code>--no-optional</code> argument will prevent optional dependencies from
being installed.</p>
<p>The <code>--no-shrinkwrap</code> argument, which will ignore an available
shrinkwrap file and use the package.json instead.</p>
<p>The <code>--nodedir=/path/to/node/source</code> argument will allow npm to find the
node source code so that npm can compile native modules.</p>
<p>See <code><a href="../misc/npm-config.html">npm-config(7)</a></code>.  Many of the configuration params have some
effect on installation, since that&#39;s most of what npm does.</p>
<h2 id="algorithm">ALGORITHM</h2>
<p>To install a package, npm uses the following algorithm:</p>
<pre><code>install(where, what, family, ancestors)
fetch what, unpack to &lt;where&gt;/node_modules/&lt;what&gt;
for each dep in what.dependencies
  resolve dep to precise version
for each dep@version in what.dependencies
    not in &lt;where&gt;/node_modules/&lt;what&gt;/node_modules/*
    and not in &lt;family&gt;
  add precise version deps to &lt;family&gt;
  install(&lt;where&gt;/node_modules/&lt;what&gt;, dep, family)
</code></pre><p>For this <code>package{dep}</code> structure: <code>A{B,C}, B{C}, C{D}</code>,
this algorithm produces:</p>
<pre><code>A
+-- B
`-- C
    `-- D
</code></pre><p>That is, the dependency from B to C is satisfied by the fact that A
already caused C to be installed at a higher level.</p>
<p>See <a href="../files/npm-folders.html">npm-folders(5)</a> for a more detailed description of the specific
folder structures that npm creates.</p>
<h3 id="limitations-of-npm-s-install-algorithm">Limitations of npm&#39;s Install Algorithm</h3>
<p>There are some very rare and pathological edge-cases where a cycle can
cause npm to try to install a never-ending tree of packages.  Here is
the simplest case:</p>
<pre><code>A -&gt; B -&gt; A&#39; -&gt; B&#39; -&gt; A -&gt; B -&gt; A&#39; -&gt; B&#39; -&gt; A -&gt; ...
</code></pre><p>where <code>A</code> is some version of a package, and <code>A&#39;</code> is a different version
of the same package.  Because <code>B</code> depends on a different version of <code>A</code>
than the one that is already in the tree, it must install a separate
copy.  The same is true of <code>A&#39;</code>, which must install <code>B&#39;</code>.  Because <code>B&#39;</code>
depends on the original version of <code>A</code>, which has been overridden, the
cycle falls into infinite regress.</p>
<p>To avoid this situation, npm flat-out refuses to install any
<code>name@version</code> that is already present anywhere in the tree of package
folder ancestors.  A more correct, but more complex, solution would be
to symlink the existing version into the new location.  If this ever
affects a real use-case, it will be investigated.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../files/npm-folders.html">npm-folders(5)</a></li>
<li><a href="../cli/npm-update.html">npm-update(1)</a></li>
<li><a href="../cli/npm-link.html">npm-link(1)</a></li>
<li><a href="../cli/npm-rebuild.html">npm-rebuild(1)</a></li>
<li><a href="../misc/npm-scripts.html">npm-scripts(7)</a></li>
<li><a href="../cli/npm-build.html">npm-build(1)</a></li>
<li><a href="../cli/npm-config.html">npm-config(1)</a></li>
<li><a href="../misc/npm-config.html">npm-config(7)</a></li>
<li><a href="../files/npmrc.html">npmrc(5)</a></li>
<li><a href="../misc/npm-registry.html">npm-registry(7)</a></li>
<li><a href="../cli/npm-tag.html">npm-tag(1)</a></li>
<li><a href="../cli/npm-uninstall.html">npm-uninstall(1)</a></li>
<li><a href="../cli/npm-shrinkwrap.html">npm-shrinkwrap(1)</a></li>
<li><a href="../files/package.json.html">package.json(5)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-install &mdash; npm@2.15.12</p>

