module.exports = [
  {
    "regex": "AltiBrowser/([\\d.]+)",
    "name": "AltiBrowser",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Maple (?!III)(\\d+[.\\d]+)|Maple\\d{4}",
    "name": "Maple",
    "version": "$1",
    "engine": {
      "default": "Maple"
    }
  },
  {
    "regex": "Singlebox/(\\d+\\.[\\.\\d]+)",
    "name": "Singlebox",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "RCATorExplorer",
    "name": "RCA Tor Explorer",
    "version": ""
  },
  {
    "regex": "TQBrowser",
    "name": "TQ Browser",
    "version": ""
  },
  {
    "regex": "XXXAndroidApp",
    "name": "XnBrowse",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "ProxyFox",
    "name": "ProxyFox",
    "version": ""
  },
  {
    "regex": "PrivacyBrowser",
    "name": "Privacy Browser",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "TUSK/(\\d+[.\\d]+)",
    "name": "TUSK",
    "version": "$1"
  },
  {
    "regex": "Dezor/(\\d+[.\\d]+)",
    "name": "Dezor",
    "version": "$1"
  },
  {
    "regex": "OJR Browser/([\\d.]+)",
    "name": "OJR Browser",
    "version": "$1"
  },
  {
    "regex": "SecureBrowser/([\\d.]+)",
    "name": "AppTec Secure Browser",
    "version": "$1"
  },
  {
    "regex": "Veera/([\\d.]+)",
    "name": "Veera",
    "version": "$1"
  },
  {
    "regex": "Ninesky(?:-android-mobile)?/([\\d.]+)",
    "name": "Ninesky",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Perk/([\\d.]+)",
    "name": "Perk",
    "version": "$1"
  },
  {
    "regex": "Presearch \\(Tempest\\)",
    "name": "Presearch",
    "version": ""
  },
  {
    "regex": "QtWeb Internet Browser(?:/(\\d+[.\\d]+))?",
    "name": "QtWeb",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "UPhoneWebBrowser(\\d+[.\\d]+)",
    "name": "UPhone Browser",
    "version": "$1"
  },
  {
    "regex": "(?:MIB|MotorolaWebKit.*Version)/(\\d+[.\\d]+)",
    "name": "Motorola Internet Browser",
    "version": "$1"
  },
  {
    "regex": "iNet Browser(?: (\\d+[.\\d]+))?",
    "name": "iNet Browser",
    "version": "$1"
  },
  {
    "regex": "Prism/([\\d.]+)",
    "name": "Prism",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Awesomium/([\\d.]+)",
    "name": "Awesomium",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Roccat(?:/(\\d+[.\\d]+))?",
    "name": "Roccat",
    "version": "$1"
  },
  {
    "regex": "Swiftweasel(?:/(\\d+[.\\d]+))?",
    "name": "Swiftweasel",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "wkbrowser (\\d+[.\\d]+)",
    "name": "Wukong Browser",
    "version": "$1"
  },
  {
    "regex": "KUN/(\\d+[.\\d]+)",
    "name": "KUN",
    "version": "$1"
  },
  {
    "regex": "NaenaraBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Naenara Browser",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "nook browser(?:/(\\d+[.\\d]+))?",
    "name": "NOOK Browser",
    "version": "$1"
  },
  {
    "regex": "xChaos_Arachne/5\\.(\\d+\\.[.\\d]+)",
    "name": "Arachne",
    "version": "$1"
  },
  {
    "regex": "WeltweitimnetzBrowser/(\\d+\\.[.\\d]+)",
    "name": "Weltweitimnetz Browser",
    "version": "$1"
  },
  {
    "regex": "(?:Ladybird|LibWeb\\+LibJS/.*Browser)/(\\d+\\.[.\\d]+)",
    "name": "Ladybird",
    "version": "$1",
    "engine": {
      "default": "LibWeb"
    }
  },
  {
    "regex": "Kitt/(\\d+\\.[.\\d]+)",
    "name": "Kitt",
    "version": "$1"
  },
  {
    "regex": "sppm_bizbrowser",
    "name": "BizBrowser",
    "version": ""
  },
  {
    "regex": "SkyLeap/(\\d+\\.[.\\d]+)",
    "name": "SkyLeap",
    "version": "$1"
  },
  {
    "regex": "MaxBrowser/(\\d+\\.[.\\d]+)",
    "name": "MaxBrowser",
    "version": "$1"
  },
  {
    "regex": "YouBrowser/(\\d+\\.[.\\d]+)",
    "name": "YouBrowser",
    "version": "$1"
  },
  {
    "regex": "MixerBox-Browser",
    "name": "MixerBox AI",
    "version": ""
  },
  {
    "regex": "EudoraWeb (\\d+[.\\d]+)",
    "name": "EudoraWeb",
    "version": "$1"
  },
  {
    "regex": "Eolie",
    "name": "Eolie",
    "version": ""
  },
  {
    "regex": "^w3m/(\\d+[.\\d]+)",
    "name": "w3m",
    "version": "$1",
    "engine": {
      "default": "Text-based"
    }
  },
  {
    "regex": "Classilla/",
    "name": "Classilla",
    "version": "$1",
    "engine": {
      "default": "Clecko"
    }
  },
  {
    "regex": "WebianShell/(\\d+[.\\d]+)",
    "name": "Webian Shell",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Vonkeror(?:/(\\d+[.\\d]+))?",
    "name": "Vonkeror",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Wyzo/(\\d+\\.[.\\d]+)",
    "name": "Wyzo",
    "version": "$1"
  },
  {
    "regex": "Liri/(\\d+\\.[.\\d]+)",
    "name": "Liri Browser",
    "version": "$1"
  },
  {
    "regex": "Columbus/(\\d+\\.[.\\d]+)",
    "name": "Columbus Browser",
    "version": "$1"
  },
  {
    "regex": "GreenBrowser",
    "name": "GreenBrowser",
    "version": "",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "K-Ninja/(\\d+\\.[.\\d]+)",
    "name": "K-Ninja",
    "version": "$1"
  },
  {
    "regex": "^PB(\\d+\\.[.\\d]+)",
    "name": "PirateBrowser",
    "version": "$1"
  },
  {
    "regex": "EastBrowser/(\\d+\\.[.\\d]+)",
    "name": "East Browser",
    "version": "$1"
  },
  {
    "regex": "Qiyu/(\\d+\\.[.\\d]+)",
    "name": "Qiyu",
    "version": "$1"
  },
  {
    "regex": "WebDiscover/(\\d+\\.[.\\d]+)",
    "name": "WebDiscover",
    "version": "$1"
  },
  {
    "regex": "LeganBrowser/(\\d+\\.[.\\d]+)",
    "name": "Legan Browser",
    "version": "$1"
  },
  {
    "regex": "Acoo Browser",
    "name": "Acoo Browser",
    "version": "",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "Aplix_.*_browser/(\\d+\\.[.\\d]+)",
    "name": "Aplix",
    "version": "$1"
  },
  {
    "regex": "Mogok/(\\d+\\.[.\\d]+)",
    "name": "Mogok Browser",
    "version": "$1"
  },
  {
    "regex": "(?:IOS)?TrueLocationBrowser/(\\d+\\.[.\\d]+)",
    "name": "TrueLocation Browser",
    "version": "$1"
  },
  {
    "regex": "DiigoBrowser$",
    "name": "Diigo Browser",
    "version": ""
  },
  {
    "regex": ".*OnBrowserLite(\\d+\\.[.\\d]+)",
    "name": "OnBrowser Lite",
    "version": "$1"
  },
  {
    "regex": "Bluefy/(\\d+\\.[.\\d]+)",
    "name": "Bluefy",
    "version": "$1"
  },
  {
    "regex": "(?:Novarra-Vision|Vision-Browser)(?:/(\\d+[.\\d]+))?",
    "name": "Vision Mobile Browser",
    "version": "$1"
  },
  {
    "regex": "SurfyBrowser/(\\d+[.\\d]+)",
    "name": "Surfy Browser",
    "version": "$1"
  },
  {
    "regex": "18\\+/([\\d.]+)",
    "name": "18+ Privacy Browser",
    "version": "$1"
  },
  {
    "regex": "GoKu-iOS/(\\d+[.\\d]+)",
    "name": "GoKu",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Ask\\.com Mobile Browser",
    "name": "Ask.com",
    "version": ""
  },
  {
    "regex": "Bang/(\\d+[.\\d]+)",
    "name": "Bang",
    "version": "$1"
  },
  {
    "regex": "ManagedBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Intune Managed Browser",
    "version": "$1"
  },
  {
    "regex": "Lotus/(\\d+[.\\d]+)",
    "name": "Lotus",
    "version": "$1"
  },
  {
    "regex": "JuziBrowser",
    "name": "JUZI Browser",
    "version": ""
  },
  {
    "regex": "Ninetails(?:/(\\d+[.\\d]+))?",
    "name": "Ninetails",
    "version": "$1"
  },
  {
    "regex": "Wexond(?:/(\\d+[.\\d]+))?",
    "name": "Wexond",
    "version": "$1"
  },
  {
    "regex": "catalyst(?:/(\\d+[.\\d]+))?",
    "name": "Catalyst",
    "version": "$1"
  },
  {
    "regex": "Impervious(?:/(\\d+[.\\d]+))?",
    "name": "Impervious Browser",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "RakutenBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Rakuten Browser",
    "version": "$1"
  },
  {
    "regex": "RakutenWebSearch(?:/(\\d+[.\\d]+))?",
    "name": "Rakuten Web Search",
    "version": "$1"
  },
  {
    "regex": "VibeMate(?:/(\\d+[.\\d]+))?",
    "name": "VibeMate",
    "version": "$1"
  },
  {
    "regex": "yixia\\.browser/com\\.donerbrowser\\.app/",
    "name": "Colom Browser",
    "version": ""
  },
  {
    "regex": "tararia/(\\d+\\.[.\\d]+)",
    "name": "tararia",
    "version": "$1"
  },
  {
    "regex": "SberBrowser/(\\d+\\.[.\\d]+)",
    "name": "SberBrowser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Raspbian Chromium/(?:(\\d+[.\\d]+))?",
    "name": "Raspbian Chromium",
    "version": "$1"
  },
  {
    "regex": "Quick Search TV(?:/(?:Wild Moon Edition )?(\\d+[.\\d]+))?",
    "name": "Quick Search TV",
    "version": "$1"
  },
  {
    "regex": "Skye/(\\d+\\.[.\\d]+)",
    "name": "Skye",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "VD/\\d+",
    "name": "VD Browser",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "\\[(?:HB/29|PB/(?:66|81))\\]",
    "name": "SecureX",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "\\[HS/\\d+\\]",
    "name": "HotBrowser",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "\\[PB/\\d+\\]",
    "name": "Proxy Browser",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "^Normalized (?:iPad|iPhone) \\(iOS Safari\\)",
    "name": "Onion Browser",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "fGet/",
    "name": "fGet",
    "version": ""
  },
  {
    "regex": "Nuviu/(?:(\\d+[.\\d]+))?",
    "name": "Nuviu",
    "version": "$1"
  },
  {
    "regex": "DoCoMo/(?:(\\d+[.\\d]+))?",
    "name": "DoCoMo",
    "version": "$1"
  },
  {
    "regex": "com\\.airfind\\.browser/(?:(\\d+[.\\d]+))?",
    "name": "Airfind Secure Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "ArcMobile2(?:/(\\d+\\.[.\\d]+);)?",
    "name": "Arc Search",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Nuanti(?:Meta)?/(\\d+\\.[.\\d]+)",
    "name": "Nuanti Meta",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "RokuBrowser/(\\d+\\.[.\\d]+)",
    "name": "Roku Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "PicoBrowser/(\\d+\\.[.\\d]+)",
    "name": "PICO Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Alva/(\\d+\\.[.\\d]+)",
    "name": "ALVA",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Norton/(\\d+\\.[.\\d]+)",
    "name": "Norton Private Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Odd/(\\d+\\.[.\\d]+)",
    "name": "Odd Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Safari/537\\.36 (?:Browser|Navegador)",
    "name": "APN Browser",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "YAGI/(\\d+\\.[.\\d]+)",
    "name": "YAGI",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "InspectBrowser",
    "name": "Inspect Browser",
    "version": ""
  },
  {
    "regex": "Keepsafe Browser(?:/(\\d+[.\\d]+))?",
    "name": "Keepsafe Browser",
    "version": "$1"
  },
  {
    "regex": "(.*)Vast Browser/(\\d+\\.[.\\d]+)",
    "name": "Vast Browser",
    "version": "$2",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "bloket",
    "name": "Bloket",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "(.*)U Browser(\\d+\\.[.\\d]+)",
    "name": "U Browser",
    "version": "$2",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Chrome/(\\d+\\.[.\\d]+).+TeslaBrowser/",
    "name": "Tesla Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Chrome/(\\d+\\.[.\\d]+).+Sparrow",
    "name": "Viasat Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "Sparrow/.+CFNetwork",
    "name": "Viasat Browser",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Lilo/(\\d+\\.[.\\d]+)",
    "name": "Lilo",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Lilo/.+CFNetwork",
    "name": "Lilo",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "lexi/(\\d+[.\\d]+)",
    "name": "Lexi Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Floorp/(\\d+[.\\d]+)",
    "name": "Floorp",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "SurfBrowser/(\\d+[.\\d]+)",
    "name": "Surf Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Decentr",
    "name": "Decentr",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "youcare-android-app",
    "name": "YouCare",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "youcare-ios-app",
    "name": "YouCare",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "ABB/(\\d+[.\\d]+)",
    "name": "AdBlock Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "\\d+/tclwebkit(?:\\d+[.\\d]*)",
    "name": "BrowseHere",
    "version": ""
  },
  {
    "regex": "HiBrowser/v?(\\d+[.\\d]+)",
    "name": "Hi Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "CYBrowser/(\\d+[.\\d]+)",
    "name": "CyBrowser",
    "version": "$1"
  },
  {
    "regex": "Chrome/.+ SiteKiosk (\\d+[.\\d]+)",
    "name": "SiteKiosk",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "SiteKiosk (\\d+[.\\d]+)",
    "name": "SiteKiosk",
    "version": "$1"
  },
  {
    "regex": "ReqwirelessWeb/(\\d+[.\\d]+)",
    "name": "Reqwireless WebViewer",
    "version": "$1"
  },
  {
    "regex": "T\\+Browser/(\\d+[.\\d]+)",
    "name": "T+Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Private Browser/(\\d+[.\\d]+) Chrome/",
    "name": "Secure Private Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "ChanjetCloud/(\\d+[.\\d]+)",
    "name": "ChanjetCloud",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "SushiBrowser/(\\d+[.\\d]+)",
    "name": "Sushi Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "dBrowser/(\\d+[.\\d]+)",
    "name": "Peeps dBrowser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "LTBrowser/(\\d+[.\\d]+)",
    "name": "LT Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "lagatos-browser/(\\d+[.\\d]+)",
    "name": "Lagatos Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "psi-secure-browser/(\\d+[.\\d]+)",
    "name": "PSI Secure Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Harman_Browser/(\\d+[.\\d]+)",
    "name": "Harman Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "bonsai-browser/(\\d+[.\\d]+)",
    "name": "Bonsai",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "spectre-browser/(\\d+[.\\d]+)",
    "name": "Spectre Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "FlashBrowser/(\\d+[.\\d]+)",
    "name": "Flash Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Secure/(?:(\\d+[.\\d]+))?",
    "name": "Secure Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Arvin/(\\d+[.\\d]+)",
    "name": "Arvin",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Version/.+Chrome/.+EdgW/(\\d+[.\\d]+)",
    "name": "Edge WebView",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Mandarin Browser/(\\d+[.\\d]+)",
    "name": "Mandarin",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Torrent/(\\d+[.\\d]+)",
    "name": "Maelstrom",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Helio/(\\d+[.\\d]+)",
    "name": "Helio",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "7654Browser/(\\d+[.\\d]+)",
    "name": "7654 Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Qazweb/(\\d+[.\\d]+)",
    "name": "Qazweb",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Degdegan/(\\d+[.\\d]+)",
    "name": "deg-degan",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "JavaFX/(\\d+[.\\d]+)",
    "name": "JavaFX",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Chedot/(\\d+[.\\d]+)",
    "name": "Chedot",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Chrome/(\\d+\\.[.\\d]+) .*\\(Chromium GOST\\)",
    "name": "Chromium GOST",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "(?:DeledaoPersonal|DeledaoFamily)/(\\d+[.\\d]+)",
    "name": "Deledao",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "HasBrowser/(\\d+[.\\d]+)",
    "name": "HasBrowser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Byffox/(\\d+[.\\d]+)",
    "name": "Byffox",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Chrome/(\\d+\\.[.\\d]+) .*AgentWeb.+UCBrowser",
    "name": "CoolBrowser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "DotBrowser/(\\d+[.\\d]+)",
    "name": "Dot Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "CravingExplorer/(\\d+[.\\d]+)",
    "name": "Craving Explorer",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "DeskBrowse/(\\d+[.\\d]+)",
    "name": "DeskBrowse",
    "version": "$1"
  },
  {
    "regex": "Lolifox/(\\d+[.\\d]+)",
    "name": "Lolifox",
    "version": "$1"
  },
  {
    "regex": "PiBrowser/(\\d+[.\\d]+)",
    "name": "Pi Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "qutebrowser/(\\d+\\.[.\\d]+) .*Chrome",
    "name": "Qutebrowser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "qutebrowser(?:/(\\d+[.\\d]+))?",
    "name": "Qutebrowser",
    "version": "$1"
  },
  {
    "regex": "flast/(\\d+[.\\d]+)",
    "name": "Flast",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "PolyBrowser/(\\d+[.\\d]+)",
    "name": "PolyBrowser",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Chrome.+BriskBard/(\\d+[.\\d]+)",
    "name": "BriskBard",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "BriskBard(?:/(\\d+[.\\d]+))?",
    "name": "BriskBard",
    "version": "$1"
  },
  {
    "regex": "GinxDroid(?:Browser)?/(\\d+[.\\d]+)",
    "name": "GinxDroid Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Avira(?:Scout)?/(\\d+[.\\d]+)",
    "name": "Avira Secure Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "VenusBrowser/(\\d+[.\\d]+)",
    "name": "Venus Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Chrome.+Otter(?:[ /](\\d+[.\\d]+))?",
    "name": "Otter Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Otter(?:[ /](\\d+[.\\d]+))?",
    "name": "Otter Browser",
    "version": "$1"
  },
  {
    "regex": "Chrome.+Smooz/(\\d+[.\\d]+)",
    "name": "Smooz",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Smooz/(\\d+[.\\d]+)",
    "name": "Smooz",
    "version": "$1"
  },
  {
    "regex": "BanglaBrowser/(\\d+\\.[.\\d]+)",
    "name": "Bangla Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Cornowser/(\\d+[.\\d]+)",
    "name": "Cornowser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Orca/(\\d+[.\\d]+)",
    "name": "Orca",
    "version": "$1"
  },
  {
    "regex": "Android (?:[\\d.]+;) ?(?:[^;]+;)? Flow\\) AppleWebKit/537.+Chrome/\\d{3}",
    "name": "Flow Browser",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Flow/(?:(\\d+[.\\d]+))",
    "name": "Flow",
    "version": "$1",
    "engine": {
      "default": "EkiohFlow"
    }
  },
  {
    "regex": "Ekioh/(?:(\\d+[.\\d]+))",
    "name": "Flow",
    "version": "$1",
    "engine": {
      "default": "EkiohFlow"
    }
  },
  {
    "regex": "xStand/(\\d+[.\\d]+)",
    "name": "xStand",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Biyubi/(\\d+[.\\d]+)",
    "name": "Biyubi",
    "version": "$1"
  },
  {
    "regex": "(?:Perfect%20Browser(?:-iPad)?|Perfect(?:BrowserPro)?)/(\\d+[.\\d]+)",
    "name": "Perfect Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Browser/Phantom/V(\\d+[.\\d]+)",
    "name": "Phantom Browser",
    "version": "$1"
  },
  {
    "regex": "AwoX(?:/(\\d+[.\\d]+))? Browser",
    "name": "AwoX",
    "version": "$1"
  },
  {
    "regex": "Zetakey/(\\d+[.\\d]+)",
    "name": "Zetakey",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "PlayFreeBrowser/(?:(\\d+[.\\d]+))?",
    "name": "PlayFree Browser",
    "version": "$1"
  },
  {
    "regex": "(?:chimlac_browser|chimlac)/(?:(\\d+[.\\d]+))",
    "name": "Chim Lac",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Odin/(?:(\\d+[.\\d]+))",
    "name": "Odin",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Tbrowser/(\\d+[.\\d]+)",
    "name": "T-Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "com\\.tcl\\.browser",
    "name": "BrowseHere",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "WhaleBrowser/(\\d+[.\\d]+)",
    "name": "Whale TV Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "SFive(?:_Android)?/.+ Chrome/(\\d+[.\\d]+)",
    "name": "SFive",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "SFive_IOS/(\\d+[.\\d]+)",
    "name": "SFive",
    "version": "$1"
  },
  {
    "regex": "Navigateur web/(?:(\\d+[.\\d]+))?",
    "name": "Navigateur Web",
    "version": "$1"
  },
  {
    "regex": "Sraf(?:[/ ](\\d+[.\\d]+))?",
    "name": "Seraphic Sraf",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "SeewoBrowser/(?:(\\d+[.\\d]+))?",
    "name": "Seewo Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "(?:Kode(?:iOS)?/(?:(\\d+[.\\d]+))?|TansoDL)",
    "name": "Kode Browser",
    "version": "$1"
  },
  {
    "regex": "UR/(?:(\\d+[.\\d]+))",
    "name": "UR Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "OceanHero/([.\\d]+)",
    "name": "OceanHero",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Chrome/.+ SLBrowser/(?:(\\d+[.\\d]+))?",
    "name": "Smart Lenovo Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "SLBrowser/(?:(\\d+[.\\d]+))?",
    "name": "Smart Lenovo Browser",
    "version": "$1"
  },
  {
    "regex": "Browzar",
    "name": "Browzar",
    "version": ""
  },
  {
    "regex": "Stargon/(?:(\\d+[.\\d]+))?",
    "name": "Stargon",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "NFSBrowser/(?:(\\d+[.\\d]+))?",
    "name": "NFS Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Borealis/(\\d+[.\\d]+)",
    "name": "Borealis Navigator",
    "version": "$1"
  },
  {
    "regex": "YoloBrowser/(?:(\\d+[.\\d]+))?",
    "name": "Yolo Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "PHX/(?:(\\d+[.\\d]+))?",
    "name": "Phoenix Browser",
    "version": "$1"
  },
  {
    "regex": "PrivacyWall/(?:(\\d+[.\\d]+))?",
    "name": "PrivacyWall",
    "version": "$1"
  },
  {
    "regex": "Ghostery:?(\\d+[.\\d]+)?",
    "name": "Ghostery Privacy Browser",
    "version": "$1"
  },
  {
    "regex": "Cliqz",
    "name": "Cliqz",
    "version": ""
  },
  {
    "regex": "Firefox/.*(?:Turkcell-)?YaaniBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Yaani Browser",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "(?:Turkcell-)?YaaniBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Yaani Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "SEB/(?:(\\d+[.\\d]+))?",
    "name": "Safe Exam Browser",
    "version": "$1"
  },
  {
    "regex": "Colibri/(?:(\\d+[.\\d]+))?",
    "name": "Colibri",
    "version": "$1"
  },
  {
    "regex": "Xvast/(?:(\\d+[.\\d]+))?",
    "name": "Xvast",
    "version": "$1"
  },
  {
    "regex": "TungstenBrowser/(?:(\\d+[.\\d]+))?",
    "name": "Tungsten",
    "version": "$1"
  },
  {
    "regex": "Lulumi-browser/(?:(\\d+[.\\d]+))?",
    "name": "Lulumi",
    "version": "$1"
  },
  {
    "regex": "ybrowser/(?:(\\d+[.\\d]+))?",
    "name": "Yahoo! Japan Browser",
    "version": "$1"
  },
  {
    "regex": "iLunascapeLite/(?:(\\d+\\.[.\\d]+))?",
    "name": "Lunascape Lite",
    "version": "$1"
  },
  {
    "regex": "Chrome/.+ i?Lunascape(?:[/ ](\\d+\\.[.\\d]+))?",
    "name": "Lunascape",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "i?Lunascape(?:[/ ](\\d+\\.[.\\d]+))?",
    "name": "Lunascape",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "Polypane/(?:(\\d+[.\\d]+))?",
    "name": "Polypane",
    "version": "$1"
  },
  {
    "regex": "OhHaiBrowser/(?:(\\d+[.\\d]+))?",
    "name": "OhHai Browser",
    "version": "$1"
  },
  {
    "regex": "Sizzy/(?:(\\d+[.\\d]+))?",
    "name": "Sizzy",
    "version": "$1"
  },
  {
    "regex": "GlassBrowser/(?:(\\d+[.\\d]+))?",
    "name": "Glass Browser",
    "version": "$1"
  },
  {
    "regex": "ToGate/(?:(\\d+[.\\d]+))?",
    "name": "ToGate",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "(?:AirWatch Browser v|AirWatchBrowser/)(?:(\\d+[.\\d]+))?",
    "name": "VMware AirWatch",
    "version": "$1"
  },
  {
    "regex": "AOL (\\d+[.\\d]+)",
    "name": "AOL Explorer",
    "version": "$1",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "ADG/(?:(\\d+[.\\d]+))?",
    "name": "AOL Desktop",
    "version": "$1"
  },
  {
    "regex": "Elements Browser/(?:(\\d+[.\\d]+))?",
    "name": "Elements Browser",
    "version": "$1"
  },
  {
    "regex": "Light/(\\d+[.\\d]+)",
    "name": "Light",
    "version": "$1"
  },
  {
    "regex": "Valve Steam GameOverlay/(?:(\\d+[.\\d]+))?",
    "name": "Steam In-Game Overlay",
    "version": "$1"
  },
  {
    "regex": "115Browser/(?:(\\d+[.\\d]+))?",
    "name": "115 Browser",
    "version": "$1"
  },
  {
    "regex": "Atom/(?:(\\d+[.\\d]+))?",
    "name": "Atom",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Wolvic/(\\d+\\.[.\\d]+)",
    "name": "Wolvic",
    "version": "$1"
  },
  {
    "regex": "Mobile VR.+Firefox",
    "name": "Firefox Reality",
    "version": ""
  },
  {
    "regex": "AVG(?:/(\\d+[.\\d]+))?",
    "name": "AVG Secure Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "AT/(\\d+[.\\d]+)",
    "name": "AVG Secure Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Start/(?:(\\d+[.\\d]+))?",
    "name": "START Internet Browser",
    "version": "$1"
  },
  {
    "regex": "Lovense(?:/(\\d+[.\\d]+))?",
    "name": "Lovense Browser",
    "version": "$1"
  },
  {
    "regex": "(?:com\\.airfind\\.deltabrowser|AirSearch)(?:/(\\d+[.\\d]+))?",
    "name": "Delta Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "(?:Ordissimo|webissimo3)(?:/(\\d+[.\\d]+))?",
    "name": "Ordissimo",
    "version": "$1"
  },
  {
    "regex": "CCleaner(?:/(\\d+[.\\d]+))?",
    "name": "CCleaner",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "AlohaLite(?:/(\\d+[.\\d]+))?",
    "name": "Aloha Browser Lite",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "TaoBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Tao Browser",
    "version": "$1"
  },
  {
    "regex": "Falkon(?:/(\\d+[.\\d]+))?",
    "name": "Falkon",
    "version": "$1"
  },
  {
    "regex": "mCent(?:/(\\d+[.\\d]+))?",
    "name": "mCent",
    "version": "$1"
  },
  {
    "regex": "SalamWeb(?:/(\\d+[.\\d]+))?",
    "name": "SalamWeb",
    "version": "$1"
  },
  {
    "regex": "BlackHawk(?:/(\\d+[.\\d]+))?",
    "name": "BlackHawk",
    "version": "$1"
  },
  {
    "regex": "Minimo(?:/(\\d+[.\\d]+))?",
    "name": "Minimo",
    "version": "$1"
  },
  {
    "regex": "WIB(?:/(\\d+[.\\d]+))?",
    "name": "Wear Internet Browser",
    "version": "$1"
  },
  {
    "regex": "Origyn Web Browser",
    "name": "Origyn Web Browser",
    "version": ""
  },
  {
    "regex": "Kinza(?:/(\\d+[.\\d]+))?",
    "name": "Kinza",
    "version": "$1"
  },
  {
    "regex": "Beamrise(?:/(\\d+[.\\d]+))?",
    "name": "Beamrise",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "Faux(?:/(\\d+[.\\d]+))?",
    "name": "Faux Browser",
    "version": "$1"
  },
  {
    "regex": "splash Version(?:/(\\d+[.\\d]+))?",
    "name": "Splash",
    "version": "$1"
  },
  {
    "regex": "MZBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Meizu Browser",
    "version": "$1"
  },
  {
    "regex": "COSBrowser(?:/(\\d+[.\\d]+))?",
    "name": "COS Browser",
    "version": "$1"
  },
  {
    "regex": "Crusta(?:/(\\d+[.\\d]+))?",
    "name": "Crusta",
    "version": "$1"
  },
  {
    "regex": "Hawk/TurboBrowser(?:/v?(\\d+[.\\d]+))?",
    "name": "Hawk Turbo Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Hawk/QuickBrowser(?:/v?(\\d+[.\\d]+))?",
    "name": "Hawk Quick Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "FreeU(?:/(\\d+[.\\d]+))?",
    "name": "FreeU",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "NoxBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Nox Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Basilisk(?:/(\\d+[.\\d]+))?",
    "name": "Basilisk",
    "version": "$1",
    "engine": {
      "default": "Goanna"
    }
  },
  {
    "regex": "SputnikBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Sputnik Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "TNSBrowser(?:/(\\d+[.\\d]+))?",
    "name": "K.Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "OculusBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Oculus Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Jio(?:Browser|Pages|Sphere)(?:/(\\d+[.\\d]+))?",
    "name": "JioSphere",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "SY/(\\d+[.\\d]+) Chrome/",
    "name": "Stampy Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Chrome/.+ Hola(?:/(\\d+[.\\d]+))?",
    "name": "hola! Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "SlimBoat/(?:(\\d+[.\\d]+))",
    "name": "SlimBoat",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Slimjet/(?:(\\d+[.\\d]+))",
    "name": "Slimjet",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "(?:7Star|Kuaiso)/(?:(\\d+[.\\d]+))",
    "name": "7Star",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "MxNitro/(?:(\\d+[.\\d]+))",
    "name": "MxNitro",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "HuaweiBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Huawei Browser Mobile",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "HBPC/(\\d+[.\\d]+)",
    "name": "Huawei Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "ZTE ?Browser/",
    "name": "ZTE Browser",
    "version": "$1"
  },
  {
    "regex": "VivoBrowser(?:/(\\d+[.\\d]+))?",
    "name": "vivo Browser",
    "version": "$1"
  },
  {
    "regex": "RealmeBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Realme Browser",
    "version": "$1"
  },
  {
    "regex": "Beaker ?Browser(?:[/ ](\\d+[.\\d]+))?",
    "name": "Beaker Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "QwantiOS/(\\d+[.\\d]+)",
    "name": "Qwant Mobile",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Chrome/.*QwantMobile(?:/(\\d+[.\\d]+))?",
    "name": "Qwant Mobile",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "QwantMobile(?:/(\\d+[.\\d]+))?",
    "name": "Qwant Mobile",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "Qwant/(\\d+[.\\d]+)",
    "name": "Qwant Mobile",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "TenFourFox(?:/(\\d+[.\\d]+))?",
    "name": "TenFourFox",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Chrome/.+ AOLShield(?:/(\\d+[.\\d]+))?",
    "name": "AOL Shield Pro",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "AOLShield(?:/(\\d+[.\\d]+))?",
    "name": "AOL Shield",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "(?<!motorola |; )Edge[ /](\\d+[.\\d]+)",
    "name": "Microsoft Edge",
    "version": "$1",
    "engine": {
      "default": "Edge"
    }
  },
  {
    "regex": "EdgiOS[ /](\\d+[.\\d]+)",
    "name": "Microsoft Edge",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "EdgA[ /](\\d+[.\\d]+)",
    "name": "Microsoft Edge",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Edg[ /](\\d+[.\\d]+)",
    "name": "Microsoft Edge",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "QIHU 360[ES]E|QihooBrowserHD/(\\d+[.\\d]+)",
    "name": "360 Secure Browser",
    "version": "$1"
  },
  {
    "regex": "Chrome.+Safari/537\\.36/(\\d+[.\\d]+)$",
    "name": "360 Secure Browser",
    "version": "$1"
  },
  {
    "regex": "360 Aphone Browser(?:[ /]?\\(?(\\d+[.\\d]+)(?:beta)?\\)?)?",
    "name": "360 Phone Browser",
    "version": "$1"
  },
  {
    "regex": "SailfishBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Sailfish Browser",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "IceCat(?:/(\\d+[.\\d]+))?",
    "name": "IceCat",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Mobicip",
    "name": "Mobicip",
    "version": "",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Camino(?:/(\\d+[.\\d]+))?",
    "name": "Camino",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Waterfox(?:/(\\d+[.\\d]+))?",
    "name": "Waterfox",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "VertexSurf/(\\d+\\.[.\\d]+)",
    "name": "Vertex Surf",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Chrome/.+ AlohaBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Aloha Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "AlohaBrowser(?:App)?(?:/(\\d+[.\\d]+))?",
    "name": "Aloha Browser",
    "version": "$1"
  },
  {
    "regex": "Aloha/",
    "name": "Aloha Browser",
    "version": ""
  },
  {
    "regex": "Chrome.+(?:Avast(?:SecureBrowser)?|ASW|Safer)(?:/(\\d+[.\\d]+))?",
    "name": "Avast Secure Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "(?:Avast(?:SecureBrowser)?|ASW|Safer)(?:/(\\d+[.\\d]+))?",
    "name": "Avast Secure Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Epic(?:/(\\d+[.\\d]+))",
    "name": "Epic",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Fennec(?:/(\\d+[.\\d]+))?",
    "name": "Fennec",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Firefox.*Tablet browser (\\d+[.\\d]+)",
    "name": "MicroB",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Maemo Browser(?: (\\d+[.\\d]+))?",
    "name": "MicroB",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Deepnet Explorer (\\d+[.\\d]+)?",
    "name": "Deepnet Explorer",
    "version": "$1"
  },
  {
    "regex": "Avant ?Browser",
    "name": "Avant Browser",
    "version": "",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "OppoBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Oppo Browser",
    "version": "$1"
  },
  {
    "regex": "Chrome/(\\d+\\.[.\\d]+) .*MRCHROME",
    "name": "Amigo",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "AtomicBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Atomic Web Browser",
    "version": "$1"
  },
  {
    "regex": "Bunjalloo(?:/(\\d+[.\\d]+))?",
    "name": "Bunjalloo",
    "version": "$1"
  },
  {
    "regex": "Chrome/(\\d+\\.[.\\d]+).*Brave/",
    "name": "Brave",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Brave(?: Chrome)?(?:/(\\d+[.\\d]+))?",
    "name": "Brave",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Iridium(?:/(\\d+[.\\d]+))?",
    "name": "Iridium",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "Iceweasel(?:/(\\d+[.\\d]+))?",
    "name": "Iceweasel",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "WebPositive",
    "name": "WebPositive",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": ".*Goanna.*PaleMoon(?:/(\\d+[.\\d]+))?",
    "name": "Pale Moon",
    "version": "$1",
    "engine": {
      "default": "Goanna"
    }
  },
  {
    "regex": "PaleMoon(?:/(\\d+[.\\d]+))?",
    "name": "Pale Moon",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "CometBird(?:/(\\d+[.\\d]+))?",
    "name": "CometBird",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "IceDragon(?:/(\\d+[.\\d]+))?",
    "name": "IceDragon",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Flock(?:/(\\d+[.\\d]+))?",
    "name": "Flock",
    "version": "$1",
    "engine": {
      "default": "Gecko",
      "versions": {
        "3": "WebKit"
      }
    }
  },
  {
    "regex": "JigBrowserPlus/(?:(\\d+[.\\d]+))?",
    "name": "Jig Browser Plus",
    "version": "$1"
  },
  {
    "regex": "jig browser(?: web;|9i?)?(?:[/ ](\\d+[.\\d]+))?",
    "name": "Jig Browser",
    "version": "$1"
  },
  {
    "regex": "Kapiko(?:/(\\d+[.\\d]+))?",
    "name": "Kapiko",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Kylo(?:/(\\d+[.\\d]+))?",
    "name": "Kylo",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Origin/(?:(\\d+[.\\d]+))?",
    "name": "Origin In-Game Overlay",
    "version": "$1"
  },
  {
    "regex": "Cunaguaro(?:/(\\d+[.\\d]+))?",
    "name": "Cunaguaro",
    "version": "$1"
  },
  {
    "regex": "(?:TO-Browser/TOB|DT-Browser/DTB)(\\d+[.\\d]+)",
    "name": "t-online.de Browser",
    "version": "$1"
  },
  {
    "regex": "Kazehakase(?:/(\\d+[.\\d]+))?",
    "name": "Kazehakase",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "ArcticFox(?:/(\\d+[.\\d]+))?",
    "name": "Arctic Fox",
    "version": "$1",
    "engine": {
      "default": "Goanna"
    }
  },
  {
    "regex": "Mypal(?:/(\\d+[.\\d]+))?",
    "name": "Mypal",
    "version": "$1",
    "engine": {
      "default": "Goanna"
    }
  },
  {
    "regex": "Centaury(?:/(\\d+[.\\d]+))?",
    "name": "Centaury",
    "version": "$1",
    "engine": {
      "default": "Goanna"
    }
  },
  {
    "regex": "(?:Focus|Klar)(?:/(\\d+[.\\d]+))",
    "name": "Firefox Focus",
    "version": "$1"
  },
  {
    "regex": "Cyberfox(?:/(\\d+[.\\d]+))?",
    "name": "Cyberfox",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Firefox/(\\d+\\.[.\\d]+).*\\(Swiftfox\\)",
    "name": "Swiftfox",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "UCBrowserHD/(\\d[\\d.]+)",
    "name": "UC Browser HD",
    "version": "$1"
  },
  {
    "regex": "UCMini(?:[ /]?(\\d+[.\\d]+))?",
    "name": "UC Browser Mini",
    "version": "$1"
  },
  {
    "regex": "UC[ ]?Browser.* \\(UCMini\\)",
    "name": "UC Browser Mini",
    "version": ""
  },
  {
    "regex": "Chrome.+uc mini browser(\\d+[.\\d]+)?",
    "name": "UC Browser Mini",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "UCTurbo(?:[ /]?(\\d+[.\\d]+))?",
    "name": "UC Browser Turbo",
    "version": "$1"
  },
  {
    "regex": "UC[ ]?Browser.* \\(UCTurbo\\)",
    "name": "UC Browser Turbo",
    "version": ""
  },
  {
    "regex": "UC[ ]?Browser(?:[ /]?(\\d+[.\\d]+))?",
    "name": "UC Browser",
    "version": "$1"
  },
  {
    "regex": "UCWEB(?:[ /]?(\\d+[.\\d]+))?",
    "name": "UC Browser",
    "version": "$1"
  },
  {
    "regex": "UC AppleWebKit",
    "name": "UC Browser",
    "version": ""
  },
  {
    "regex": "UC%20Browser/(\\d+[.\\d]+)? CFNetwork/.+Darwin/.+(?!.*x86_64)",
    "name": "UC Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Chrome.+UC Browser(\\d+[.\\d]+)",
    "name": "UC Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Firefox.+UCKai/(\\d+[.\\d]+)",
    "name": "UC Browser",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "(?:Mobile|Tablet).*Servo.*Firefox(?:/(\\d+[.\\d]+))?",
    "name": "Firefox Mobile",
    "version": "$1",
    "engine": {
      "default": "Servo"
    }
  },
  {
    "regex": "(?:Mobile|Tablet).*Firefox(?:/(\\d+[.\\d]+))?",
    "name": "Firefox Mobile",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "FxiOS/(\\d+[.\\d]+)",
    "name": "Firefox Mobile iOS",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": ".*Servo.*Firefox(?:/(\\d+[.\\d]+))?",
    "name": "Firefox",
    "version": "$1",
    "engine": {
      "default": "Servo"
    }
  },
  {
    "regex": "(?!.*Opera[ /])Firefox(?:[ /](\\d+[.\\d]+))?",
    "name": "Firefox",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "(?:BonEcho|GranParadiso|Lorentz|Minefield|Namoroka|Shiretoko)[ /](\\d+[.\\d]+)",
    "name": "Firefox",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "ANTFresco(?:[/ ](\\d+[.\\d]+))?",
    "name": "ANT Fresco",
    "version": "$1"
  },
  {
    "regex": "ANTGalio(?:/(\\d+[.\\d]+))?",
    "name": "ANTGalio",
    "version": "$1"
  },
  {
    "regex": "(?:Espial|Escape)(?:[/ ](\\d+[.\\d]+))?",
    "name": "Espial TV Browser",
    "version": "$1"
  },
  {
    "regex": "RockMelt(?:/(\\d+[.\\d]+))?",
    "name": "RockMelt",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Fireweb Navigator(?:/(\\d+[.\\d]+))?",
    "name": "Fireweb Navigator",
    "version": "$1"
  },
  {
    "regex": "Fireweb(?:/(\\d+[.\\d]+))?",
    "name": "Fireweb",
    "version": "$1"
  },
  {
    "regex": "(?:Navigator|Netscape6?)(?:/(\\d+[.\\d]+))?",
    "name": "Netscape",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "(?:Polarity)(?:[/ ](\\d+[.\\d]+))?",
    "name": "Polarity",
    "version": "$1"
  },
  {
    "regex": "(?:QupZilla)(?:[/ ](\\d+[.\\d]+))?",
    "name": "QupZilla",
    "version": "$1"
  },
  {
    "regex": "(?:Dooble)(?:[/ ](\\d+[.\\d]+))?",
    "name": "Dooble",
    "version": "$1"
  },
  {
    "regex": "Whale/(\\d+[.\\d]+)",
    "name": "Whale Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Obigo[ ]?(?:InternetBrowser|Browser)?(?:[ /]([a-z0-9]*))?",
    "name": "Obigo",
    "version": "$1"
  },
  {
    "regex": "Obigo|Teleca",
    "name": "Obigo",
    "version": ""
  },
  {
    "regex": "Chrome/.+ OP(?:RG)?X(?:/(\\d+[.\\d]+))?",
    "name": "Opera GX",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "OP(?:RG)?X(?:/(\\d+[.\\d]+))?",
    "name": "Opera GX",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Opera%20GX/.+CFNetwork/.+Darwin/",
    "name": "Opera GX",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "(?:Opera Tablet.*Version|Opera/.+(?<!SymbOS; )Opera Mobi.+Version|Mobile.+OPR)/(\\d+[.\\d]+)",
    "name": "Opera Mobile",
    "version": "$1",
    "engine": {
      "default": "Presto",
      "versions": {
        "15": "Blink"
      }
    }
  },
  {
    "regex": "MMS/(\\d+[.\\d]+)",
    "name": "Opera Neon",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "OMI/(\\d+[.\\d]+)",
    "name": "Opera Devices",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Opera%20Touch/(\\d+[.\\d]+)? CFNetwork/.+Darwin/.+(?!.*x86_64)",
    "name": "Opera Touch",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Opera%20Touch/.+CFNetwork/.+Darwin/.+(?!.*x86_64)",
    "name": "Opera Touch",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "OPT/(\\d+[.\\d]+)",
    "name": "Opera Touch",
    "version": "$1"
  },
  {
    "regex": "Opera/(\\d+\\.[.\\d]+) .*(?<!SymbOS; )Opera Mobi",
    "name": "Opera Mobile",
    "version": "$1",
    "engine": {
      "default": "Presto",
      "versions": {
        "15": "Blink"
      }
    }
  },
  {
    "regex": "Opera ?Mini/(?:att/)?(\\d+[.\\d]+)",
    "name": "Opera Mini",
    "version": "$1",
    "engine": {
      "default": "Presto"
    }
  },
  {
    "regex": "Opera ?Mini.+Version/(\\d+[.\\d]+)",
    "name": "Opera Mini",
    "version": "$1",
    "engine": {
      "default": "Presto"
    }
  },
  {
    "regex": "OPiOS/(\\d+[.\\d]+)",
    "name": "Opera Mini iOS",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Opera%20Mini/(\\d+[.\\d]+) CFNetwork",
    "name": "Opera Mini iOS",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Opera.+Edition Next.+Version/(\\d+[.\\d]+)",
    "name": "Opera Next",
    "version": "$1",
    "engine": {
      "default": "Presto",
      "versions": {
        "15": "Blink"
      }
    }
  },
  {
    "regex": "(?:Opera|OPR)[/ ](?:9\\.80.*Version/)?(\\d+\\.[.\\d]+) .*Edition Next",
    "name": "Opera Next",
    "version": "$1",
    "engine": {
      "default": "Presto",
      "versions": {
        "15": "Blink"
      }
    }
  },
  {
    "regex": "(?:Opera[/ ]?|OPR[/ ])(?:9\\.80.*Version/)?(\\d+[.\\d]+)",
    "name": "Opera",
    "version": "$1",
    "engine": {
      "default": "",
      "versions": {
        "7": "Presto",
        "15": "Blink",
        "3.5": "Elektra"
      }
    }
  },
  {
    "regex": "Opera/.+CFNetwork",
    "name": "Opera",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Chrome.+Opera/",
    "name": "Opera",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "rekonq(?:/(\\d+[.\\d]+))?",
    "name": "Rekonq",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "CoolNovo(?:/(\\d+[.\\d]+))?",
    "name": "CoolNovo",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "(?:Comodo[ _])?Dragon/(\\d+[.\\d]+)",
    "name": "Comodo Dragon",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "ChromePlus(?:/(\\d+[.\\d]+))?",
    "name": "ChromePlus",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "Conkeror(?:/(\\d+[.\\d]+))?",
    "name": "Conkeror",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Konqueror(?:/(\\d+[.\\d]+))?",
    "name": "Konqueror",
    "version": "$1",
    "engine": {
      "default": "KHTML",
      "versions": {
        "4": ""
      }
    }
  },
  {
    "regex": "bdhonorbrowser/(\\d+[.\\d]+)",
    "name": "HONOR Browser",
    "version": "$1"
  },
  {
    "regex": "(?:baidubrowser|bdbrowser(?:(?:hd)?_i18n)?|FlyFlow|BaiduHD)(?:[/ ](\\d+[.\\d]*))?",
    "name": "Baidu Browser",
    "version": "$1"
  },
  {
    "regex": "(?:(?:BD)?Spark(?:Safe)?|BIDUBrowser)[/ ](\\d+[.\\d]*)",
    "name": "Baidu Spark",
    "version": "$1"
  },
  {
    "regex": "YaBrowser(?:/(\\d+[.\\d]*)) YaApp_iOS",
    "name": "Yandex Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "iP(?:hone|ad).*YaBrowser(?:/(\\d+[.\\d]*))",
    "name": "Yandex Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "YaBrowser(?:/(\\d+[.\\d]*)) \\(lite\\)?",
    "name": "Yandex Browser Lite",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "YaBrowser/(\\d+[.\\d]*).*corp",
    "name": "Yandex Browser Corp",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "YaBrowser(?:/(\\d+[.\\d]*))(?: \\((alpha|beta)\\))?",
    "name": "Yandex Browser",
    "version": "$1 $2",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Ya(?:ndex)?SearchBrowser(?:/(\\d+[.\\d]*))",
    "name": "Yandex Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Viv(?:aldi)?/(\\d+[.\\d]+)",
    "name": "Vivaldi",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "TweakStyle(?:/(\\d+[.\\d]+))?",
    "name": "TweakStyle",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Chrome.+Midori Browser/(\\d+[.\\d]+)",
    "name": "Midori",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Midori(?:[ /](\\d+[.\\d]+))?",
    "name": "Midori",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Mercury/(?:(\\d+[.\\d]+))?",
    "name": "Mercury",
    "version": "$1"
  },
  {
    "regex": "Chrome.+Maxthon/\\d{4}",
    "name": "Maxthon",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Chrome.+(?:MxBrowser|Maxthon)(?:.+\\(portable\\))?/(\\d+\\.[.\\d]+)",
    "name": "Maxthon",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "4.2": "Blink"
      }
    }
  },
  {
    "regex": "(?:Maxthon(?:%20Browser)?|MxBrowser(?:-inhouse|-iPhone)?|MXiOS)[ /](\\d+[.\\d]+)?",
    "name": "Maxthon",
    "version": "$1",
    "engine": {
      "default": "",
      "versions": {
        "3": "WebKit"
      }
    }
  },
  {
    "regex": "(?:Maxthon|MyIE2)",
    "name": "Maxthon",
    "version": "",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "Puffin/(\\d+[.\\d]+)FP",
    "name": "Puffin Cloud Browser",
    "version": "$1"
  },
  {
    "regex": "Puffin/(\\d+[.\\d]+)(?:[LMW]D)",
    "name": "Puffin Secure Browser",
    "version": "$1"
  },
  {
    "regex": "Puffin/(\\d+[.\\d]+)(?:[AILW][PT]|M)?",
    "name": "Puffin Web Browser",
    "version": "$1"
  },
  {
    "regex": "MobileIron(?:/(\\d+[.\\d]+))?",
    "name": "Iron Mobile",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Chrome(?:/(\\d+\\.[.\\d]+) )?.*Iron",
    "name": "Iron",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "Iron/(\\d+[.\\d]+)",
    "name": "Iron",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "Epiphany(?:/(\\d+[.\\d]+))?",
    "name": "GNOME Web",
    "version": "$1",
    "engine": {
      "default": "Gecko",
      "versions": {
        "2.9.16": "",
        "2.28": "WebKit"
      }
    }
  },
  {
    "regex": "LieBaoFast(?:[ /](\\d+[.\\d]+))?",
    "name": "LieBaoFast",
    "version": "$1"
  },
  {
    "regex": "LBBrowser(?:[ /](\\d+[.\\d]+))?",
    "name": "Cheetah Browser",
    "version": "$1"
  },
  {
    "regex": "SE (\\d+[.\\d]+)",
    "name": "Sogou Explorer",
    "version": "$1"
  },
  {
    "regex": "QQBrowserLite/([\\d.]+)",
    "name": "QQ Browser Lite",
    "version": "$1"
  },
  {
    "regex": "M?QQBrowser/Mini([.\\d]+)?",
    "name": "QQ Browser Mini",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "M?QQ(?:Browser|浏览器)(?:/([.\\d]+))?",
    "name": "QQ Browser",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "(?:MIUIBrowser|MiuiBrowser)(?:/(\\d+[.\\d]+))?",
    "name": "Mi Browser",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "(?:coc_coc_browser|coccocbrowser|CocCoc)(?:/(\\d+[.\\d]+))?",
    "name": "Coc Coc",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "(?:DuckDuckGo|Ddg)/(\\d+[.\\d]*)",
    "name": "DuckDuckGo Privacy Browser",
    "version": "$1"
  },
  {
    "regex": "(?:DDG-Android-|ddg_android/)(\\d+[.\\d]*)",
    "name": "DuckDuckGo Privacy Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Samsung ?Browser(?:[/ ](\\d+[.\\d]+))?",
    "name": "Samsung Browser",
    "version": "$1"
  },
  {
    "regex": "(?:SFB(?:rowser)?)/(\\d+[.\\d]+)",
    "name": "Super Fast Browser",
    "version": "$1"
  },
  {
    "regex": "com\\.browser\\.tssomas(?:/(\\d+[.\\d]+))?",
    "name": "Super Fast Browser",
    "version": "$1"
  },
  {
    "regex": "EUI Browser(?:/(\\d+[.\\d]+))?",
    "name": "EUI Browser",
    "version": "$1"
  },
  {
    "regex": "UBrowser(?:/(\\d+[.\\d]+))?",
    "name": "UBrowser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Streamy(?:/(\\d+[.\\d]+))?",
    "name": "Streamy",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "isivioo",
    "name": "Isivioo",
    "version": "",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "Chrome/.+Tenta/(\\d+[.\\d]+)",
    "name": "Tenta Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Tenta/(\\d+[.\\d]+)",
    "name": "Tenta Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Rocket/(\\d+[.\\d]+)",
    "name": "Firefox Rocket",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Web Explorer/(\\d+\\.[.\\d]+) .*Chrome",
    "name": "Web Explorer",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "webexplorer/(\\d+)",
    "name": "Web Explorer",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "Chrome.+SznProhlizec/(\\d+[.\\d]+)",
    "name": "Seznam Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "SznProhlizec/(\\d+[.\\d]+)",
    "name": "Seznam Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "SogouMobileBrowser/(\\d+[.\\d]+)",
    "name": "Sogou Mobile Browser",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "Mint Browser/(\\d+[.\\d]+)",
    "name": "Mint Browser",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "Ecosia (?:android|ios)@(\\d+[.\\d]+)",
    "name": "Ecosia",
    "version": "$1",
    "engine": {
      "default": "",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "ACHEETAHI",
    "name": "CM Browser",
    "version": "",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "Chrome/.+ (?:LeBrowser|MobileLenovoBrowser)(?:/(\\d+[.\\d]+))?",
    "name": "Lenovo Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "LeBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Lenovo Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Kiwi Chrome",
    "name": "Kiwi",
    "version": "",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "Mb2345Browser/(\\d+[.\\d]+)",
    "name": "2345 Browser",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "Silk/(\\d+[.\\d]+) like Chrome",
    "name": "Mobile Silk",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Silk(?:/(\\d+[.\\d]+))?",
    "name": "Mobile Silk",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "iBrowser/Mini(\\d+\\.\\d+)",
    "name": "iBrowser Mini",
    "version": "$1"
  },
  {
    "regex": "iBrowser/(\\d+\\.[.\\d]+)?",
    "name": "iBrowser",
    "version": "$1"
  },
  {
    "regex": "IBrowse(?:[ /](\\d+[.\\d]+))?",
    "name": "IBrowse",
    "version": "$1"
  },
  {
    "regex": "UP\\.Browser(?:/(\\d+[.\\d]+))?",
    "name": "Openwave Mobile Browser",
    "version": "$1"
  },
  {
    "regex": "Openwave(?:/(\\d+[.\\d]+))?",
    "name": "Openwave Mobile Browser",
    "version": "$1"
  },
  {
    "regex": "OneBrowser(?:[ /](\\d+[.\\d]+))?",
    "name": "ONE Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "GoBrowser(?:/(\\d+[.\\d]+))?",
    "name": "GoBrowser",
    "version": "$1"
  },
  {
    "regex": "(?:NokiaBrowser|BrowserNG|WicKed|Nokia-Communicator-WWW-Browser)(?:/(\\d+[.\\d]+))?",
    "name": "Nokia Browser",
    "version": "$1"
  },
  {
    "regex": "Series60/5\\.0",
    "name": "Nokia Browser",
    "version": "7.0"
  },
  {
    "regex": "Series60/(\\d+[.\\d]+)",
    "name": "Nokia OSS Browser",
    "version": "$1"
  },
  {
    "regex": "S40OviBrowser/(\\d+[.\\d]+)",
    "name": "Nokia Ovi Browser",
    "version": "$1"
  },
  {
    "regex": "^Nokia|Nokia[EN]?\\d+",
    "name": "Nokia Browser",
    "version": ""
  },
  {
    "regex": "Sleipnir(?:(?:%20Browser)?[ /](\\d+[.\\d]+))?",
    "name": "Sleipnir",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "NTENTBrowser(?:/(\\d+[.\\d]+))?",
    "name": "NTENT Browser",
    "version": "$1"
  },
  {
    "regex": "TV Bro/(\\d+[.\\d]+)",
    "name": "TV Bro",
    "version": "$1"
  },
  {
    "regex": "Quark(?:/(\\d+[.\\d]+))?",
    "name": "Quark",
    "version": "$1"
  },
  {
    "regex": "MonumentBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Monument Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "BlueBrowser(?:/(\\d+[.\\d]+))?",
    "name": "Blue Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "JAPAN Browser(?:/(\\d+[.\\d]+))?",
    "name": "Japan Browser",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "OpenFin/(?:(\\d+[.\\d]+))",
    "name": "OpenFin",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "SuperBird(?:/(\\d+[.\\d]+))?",
    "name": "SuperBird",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "Soul(?:Browser)?$|Soul/",
    "name": "Soul Browser",
    "version": "",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "LG Browser(?:/(\\d+[.\\d]+))",
    "name": "LG Browser",
    "version": "$1"
  },
  {
    "regex": "QtWebEngine/(\\d+[.\\d]+)",
    "name": "QtWebEngine",
    "version": "$1",
    "engine": {
      "default": ""
    }
  },
  {
    "regex": "(?: wv\\)|Version/).* Chrome(?:/(\\d+[.\\d]+))?",
    "name": "Chrome Webview",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "CrMo(?:/(\\d+[.\\d]+))?",
    "name": "Chrome Mobile",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "CriOS(?:/(\\d+[.\\d]+))?",
    "name": "Chrome Mobile iOS",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Chrome(?:/(\\d+[.\\d]+))? Mobile",
    "name": "Chrome Mobile",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "chromeframe(?:/(\\d+[.\\d]+))?",
    "name": "Chrome Frame",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Chromium(?:/(\\d+[.\\d]+))?",
    "name": "Chromium",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": ".*Chromium(?:_(\\d+[.\\d]+))?",
    "name": "Chromium",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "HeadlessChrome(?:/(\\d+[.\\d]+))?",
    "name": "Headless Chrome",
    "version": "$1",
    "engine": {
      "default": "Blink"
    }
  },
  {
    "regex": "Chrome(?!book)(?:/(\\d+[.\\d]+))?",
    "name": "Chrome",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "28": "Blink"
      }
    }
  },
  {
    "regex": "PocketBook/",
    "name": "PocketBook Browser",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "(?:Tizen|SLP) ?Browser(?:/(\\d+[.\\d]+))?",
    "name": "Tizen Browser",
    "version": "$1"
  },
  {
    "regex": "Tizen (?:\\d+\\.[.\\d]+)[^\\.\\d].* Version/(\\d+[.\\d]+) (?:TV|Mobile|like)",
    "name": "Tizen Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit",
      "versions": {
        "4": "Blink"
      }
    }
  },
  {
    "regex": "Blazer(?:/(\\d+[.\\d]+))?",
    "name": "Palm Blazer",
    "version": "$1"
  },
  {
    "regex": "Pre/(\\d+[.\\d]+)",
    "name": "Palm Pre",
    "version": "$1"
  },
  {
    "regex": "(?:hpw|web)OS/(\\d+[.\\d]+)",
    "name": "wOSBrowser",
    "version": "$1"
  },
  {
    "regex": "WebPro(?:[ /](\\d+[.\\d]+))?",
    "name": "Palm WebPro",
    "version": "$1"
  },
  {
    "regex": "Palmscape(?:[ /](\\d+[.\\d]+))?",
    "name": "Palmscape",
    "version": "$1"
  },
  {
    "regex": "Jasmine(?:[ /](\\d+[.\\d]+))?",
    "name": "Jasmine",
    "version": "$1"
  },
  {
    "regex": "Lynx(?:/(\\d+[.\\d]+))?",
    "name": "Lynx",
    "version": "$1",
    "engine": {
      "default": "Text-based"
    }
  },
  {
    "regex": "NCSA_Mosaic(?:/(\\d+[.\\d]+))?",
    "name": "NCSA Mosaic",
    "version": "$1"
  },
  {
    "regex": "VMS_Mosaic(?:/(\\d+[.\\d]+))?",
    "name": "VMS Mosaic",
    "version": "$1"
  },
  {
    "regex": "ABrowse(?: (\\d+[.\\d]+))?",
    "name": "ABrowse",
    "version": "$1"
  },
  {
    "regex": "amaya(?:/(\\d+[.\\d]+))?",
    "name": "Amaya",
    "version": "$1"
  },
  {
    "regex": "AmigaVoyager(?:/(\\d+[.\\d]+))?",
    "name": "Amiga Voyager",
    "version": "$1"
  },
  {
    "regex": "Amiga-Aweb(?:/(\\d+[.\\d]+))?",
    "name": "Amiga Aweb",
    "version": "$1"
  },
  {
    "regex": "Arora(?:/(\\d+[.\\d]+))?",
    "name": "Arora",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Beonex(?:/(\\d+[.\\d]+))?",
    "name": "Beonex",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "bline(?:/(\\d+[.\\d]+))?",
    "name": "B-Line",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "BrowseX \\((\\d+[.\\d]+)",
    "name": "BrowseX",
    "version": "$1"
  },
  {
    "regex": "Charon(?:[/ ](\\d+[.\\d]+))?",
    "name": "Charon",
    "version": "$1"
  },
  {
    "regex": "Cheshire(?:/(\\d+[.\\d]+))?",
    "name": "Cheshire",
    "version": "$1"
  },
  {
    "regex": "dbrowser",
    "name": "dbrowser",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Dillo(?:/(\\d+[.\\d]+))?",
    "name": "Dillo",
    "version": "$1",
    "engine": {
      "default": "Dillo"
    }
  },
  {
    "regex": "Dolfin(?:/(\\d+[.\\d]+))?|dolphin",
    "name": "Dolphin",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Elinks(?:[ /](\\d+[.\\d]+))?",
    "name": "Elinks",
    "version": "$1",
    "engine": {
      "default": "Text-based"
    }
  },
  {
    "regex": "Element Browser(?:[ /](\\d+[.\\d]+))?",
    "name": "Element Browser",
    "version": "$1"
  },
  {
    "regex": "eZBrowser(?:/(\\d+[.\\d]+))?",
    "name": "eZ Browser",
    "version": "$1"
  },
  {
    "regex": "Firebird(?! Build)(?:/(\\d+[.\\d]+))?",
    "name": "Firebird",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Fluid(?:/(\\d+[.\\d]+))?",
    "name": "Fluid",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Galeon(?:/(\\d+[.\\d]+))?",
    "name": "Galeon",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "(?:Google Earth Pro|Google%20Earth%20Pro)(?:/(\\d+[.\\d]+))?",
    "name": "Google Earth Pro",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "GoogleEarth/(\\d+\\.[.\\d]+)[^\\.\\d].*client:(?:Plus|Pro)",
    "name": "Google Earth Pro",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Google ?Earth(?:/v?(\\d+[.\\d]+))?",
    "name": "Google Earth",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "HotJava(?:/(\\d+[.\\d]+))?",
    "name": "HotJava",
    "version": "$1"
  },
  {
    "regex": "iCabMobile(?:[ /](\\d+[.\\d]+))?",
    "name": "iCab Mobile",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "iCab(?:[ /](\\d+[.\\d]+))?",
    "name": "iCab",
    "version": "$1",
    "engine": {
      "default": "iCab",
      "versions": {
        "4": "WebKit"
      }
    }
  },
  {
    "regex": "Crazy Browser (\\d+[.\\d]+)",
    "name": "Crazy Browser",
    "version": "$1",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "IEMobile[ /](\\d+[.\\d]+)",
    "name": "IE Mobile",
    "version": "$1",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "MSIE (\\d+\\.[.\\d]+)[^\\.\\d].*XBLWP7",
    "name": "IE Mobile",
    "version": "$1",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "MSIE.*Trident/4\\.0",
    "name": "Internet Explorer",
    "version": "8.0",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "MSIE.*Trident/5\\.0",
    "name": "Internet Explorer",
    "version": "9.0",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "MSIE.*Trident/6\\.0",
    "name": "Internet Explorer",
    "version": "10.0",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "Trident/[78]\\.0",
    "name": "Internet Explorer",
    "version": "11.0",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "MSIE (\\d+[.\\d]+)",
    "name": "Internet Explorer",
    "version": "$1",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "IE[ /](\\d+[.\\d]+)",
    "name": "Internet Explorer",
    "version": "$1",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "(?:MSPIE|Pocket Internet Explorer)[ /](\\d+[.\\d]+)",
    "name": "Pocket Internet Explorer",
    "version": "$1",
    "engine": {
      "default": "Trident"
    }
  },
  {
    "regex": "Kindle/(\\d+[.\\d]+)",
    "name": "Kindle Browser",
    "version": "$1"
  },
  {
    "regex": "K-meleon(?:/(\\d+[.\\d]+))?",
    "name": "K-meleon",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "Links(?: \\((\\d+[.\\d]+))?",
    "name": "Links",
    "version": "$1",
    "engine": {
      "default": "Text-based"
    }
  },
  {
    "regex": "LuaKit(?:/(\\d+[.\\d]+))?",
    "name": "LuaKit",
    "version": "$1"
  },
  {
    "regex": "OmniWeb(?:/[v]?(\\d+[.\\d]+))?",
    "name": "OmniWeb",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "(?<!/)Phoenix(?:/(\\d+[.\\d]+))?",
    "name": "Phoenix",
    "version": "$1"
  },
  {
    "regex": "NetFrontLifeBrowser(?:/(\\d+[.\\d]+))?",
    "name": "NetFront Life",
    "version": "$1",
    "engine": {
      "default": "NetFront"
    }
  },
  {
    "regex": "Browser/(?:NetFont-|NF|NetFront)(\\d+[.\\d]+)",
    "name": "NetFront",
    "version": "$1",
    "engine": {
      "default": "NetFront"
    }
  },
  {
    "regex": "NetFront(?:/(\\d+[.\\d]+))?",
    "name": "NetFront",
    "version": "$1",
    "engine": {
      "default": "NetFront"
    }
  },
  {
    "regex": "PLAYSTATION|NINTENDO 3|AppleWebKit.+ N[XF]/\\d+\\.\\d+\\.\\d+",
    "name": "NetFront",
    "version": ""
  },
  {
    "regex": "NetPositive(?:/(\\d+[.\\d]+))?",
    "name": "NetPositive",
    "version": "$1"
  },
  {
    "regex": "Odyssey Web Browser(?:.*OWB/(\\d+[.\\d]+))?",
    "name": "Odyssey Web Browser",
    "version": "$1"
  },
  {
    "regex": "OffByOne",
    "name": "Off By One",
    "version": ""
  },
  {
    "regex": "(?:Oregano|OreganMediaBrowser)(?:[ /](\\d+[.\\d]+))?",
    "name": "Oregano",
    "version": "$1"
  },
  {
    "regex": "(?:Polaris|Embider)(?:[/ ](\\d+[.\\d]+))?",
    "name": "Polaris",
    "version": "$1"
  },
  {
    "regex": "SEMC-Browser(?:[/ ](\\d+[.\\d]+))?",
    "name": "SEMC-Browser",
    "version": "$1"
  },
  {
    "regex": "Shiira(?:[/ ](\\d+[.\\d]+))?",
    "name": "Shiira",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Skyfire(?:[/ ](\\d+[.\\d]+))?",
    "name": "Skyfire",
    "version": "$1"
  },
  {
    "regex": "Snowshoe(?:/(\\d+[.\\d]+))?",
    "name": "Snowshoe",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Sunrise(?:Browser)?(?:/(\\d+[.\\d]+))?",
    "name": "Sunrise",
    "version": "$1"
  },
  {
    "regex": "WeTab-Browser",
    "name": "WeTab Browser",
    "version": ""
  },
  {
    "regex": "Xiino(?:/(\\d+[.\\d]+))?",
    "name": "Xiino",
    "version": "$1"
  },
  {
    "regex": "BlackBerry|PlayBook|BB10",
    "name": "BlackBerry Browser",
    "version": ""
  },
  {
    "regex": "Browlser/(\\d+[.\\d]+)",
    "name": "Browlser",
    "version": "$1"
  },
  {
    "regex": "(?<! like )Android(?!\\.)",
    "name": "Android Browser",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Coast(?:/(\\d+[.\\d]+))?",
    "name": "Coast",
    "version": "$1"
  },
  {
    "regex": "Opera%20Coast/(\\d+[.\\d]+)? CFNetwork/.+Darwin/.+(?!.*x86_64)",
    "name": "Coast",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Surf(?:/(\\d+[.\\d]+))?",
    "name": "surf",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Safari%20Technology%20Preview/(\\d+[.\\d]+)",
    "name": "Safari Technology Preview",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "(?:(?:iPod|iPad|iPhone).+Version|MobileSafari)/(\\d+[.\\d]+)",
    "name": "Mobile Safari",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "NetworkingExtension/.+ Network/.+ iOS/(\\d+[.\\d]+)",
    "name": "Mobile Safari",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "(?:Version/(\\d+\\.[.\\d]+) .*)?Mobile.*Safari/",
    "name": "Mobile Safari",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "(?!^AppleCoreMedia/1\\.0\\.0)(?:iPod|(?<!Apple TV; U; CPU )iPhone|iPad)",
    "name": "Mobile Safari",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Version/(\\d+\\.[.\\d]+) .*Safari/|(?:Safari|Safari(?:%20)?%E6%B5%8F%E8%A7%88%E5%99%A8)/?\\d+",
    "name": "Safari",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "NetworkingExtension/(\\d+[.\\d]+).+ CFNetwork",
    "name": "Safari",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "Macintosh",
    "name": "Safari",
    "version": "",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "(?:\\w{1,5}[_ ])?Dorado(?: WAP-Browser)?(?:[/ ]?(\\d+[.\\d]+))?",
    "name": "Dorado",
    "version": "$1"
  },
  {
    "regex": "NetSurf(?:/(\\d+[.\\d]+))?",
    "name": "NetSurf",
    "version": "$1",
    "engine": {
      "default": "NetSurf"
    }
  },
  {
    "regex": "Uzbl",
    "name": "Uzbl",
    "version": ""
  },
  {
    "regex": "SimpleBrowser",
    "name": "SimpleBrowser",
    "version": ""
  },
  {
    "regex": "Zvu(?:/(\\d+[.\\d]+))?",
    "name": "Zvu",
    "version": "$1",
    "engine": {
      "default": "Gecko"
    }
  },
  {
    "regex": "GOGGalaxyClient/(\\d+[.\\d]+)?",
    "name": "GOG Galaxy",
    "version": "$1"
  },
  {
    "regex": "WAP Browser/MAUI|(?:\\w*)Maui Wap Browser|MAUI[- ]Browser",
    "name": "MAUI WAP Browser",
    "version": ""
  },
  {
    "regex": "SP%20Browser/(\\d+[.\\d]+)",
    "name": "SP Browser",
    "version": "$1",
    "engine": {
      "default": "WebKit"
    }
  },
  {
    "regex": "(?<!like )Gecko(?!/\\d+ SlimerJS)",
    "name": "Firefox",
    "version": "",
    "engine": {
      "default": "Gecko"
    }
  }
];
