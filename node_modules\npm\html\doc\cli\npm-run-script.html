<!doctype html>
<html>
  <title>npm-run-script</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/cli/npm-run-script.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../cli/npm-run-script.html">npm-run-script</a></h1> <p>Run arbitrary package scripts</p>
<h2 id="synopsis">SYNOPSIS</h2>
<pre><code>npm run-script [command] [-- &lt;args&gt;]
npm run [command] [-- &lt;args&gt;]
</code></pre><h2 id="description">DESCRIPTION</h2>
<p>This runs an arbitrary command from a package&#39;s <code>&quot;scripts&quot;</code> object.  If no
<code>&quot;command&quot;</code> is provided, it will list the available scripts.  <code>run[-script]</code> is
used by the test, start, restart, and stop commands, but can be called
directly, as well. When the scripts in the package are printed out, they&#39;re
separated into lifecycle (test, start, restart) and directly-run scripts.</p>
<p>As of <a href="http://blog.npmjs.org/post/98131109725/npm-2-0-0"><code>npm@2.0.0</code></a>, you can
use custom arguments when executing scripts. The special option <code>--</code> is used by
<a href="http://goo.gl/KxMmtG">getopt</a> to delimit the end of the options. npm will pass
all the arguments after the <code>--</code> directly to your script:</p>
<pre><code>npm run test -- --grep=&quot;pattern&quot;
</code></pre><p>The arguments will only be passed to the script specified after <code>npm run</code>
and not to any pre or post script.</p>
<p>The <code>env</code> script is a special built-in command that can be used to list
environment variables that will be available to the script at runtime. If an
&quot;env&quot; command is defined in your package it will take precedence over the
built-in.</p>
<p>In addition to the shell&#39;s pre-existing <code>PATH</code>, <code>npm run</code> adds
<code>node_modules/.bin</code> to the <code>PATH</code> provided to scripts. Any binaries provided by
locally-installed dependencies can be used without the <code>node_modules/.bin</code>
prefix. For example, if there is a <code>devDependency</code> on <code>tap</code> in your package,
you should write:</p>
<pre><code>&quot;scripts&quot;: {&quot;test&quot;: &quot;tap test/\*.js&quot;}
</code></pre><p>instead of <code>&quot;scripts&quot;: {&quot;test&quot;: &quot;node_modules/.bin/tap test/\*.js&quot;}</code> to run your tests.</p>
<h2 id="see-also">SEE ALSO</h2>
<ul>
<li><a href="../misc/npm-scripts.html">npm-scripts(7)</a></li>
<li><a href="../cli/npm-test.html">npm-test(1)</a></li>
<li><a href="../cli/npm-start.html">npm-start(1)</a></li>
<li><a href="../cli/npm-restart.html">npm-restart(1)</a></li>
<li><a href="../cli/npm-stop.html">npm-stop(1)</a></li>
</ul>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">npm-run-script &mdash; npm@2.15.12</p>

