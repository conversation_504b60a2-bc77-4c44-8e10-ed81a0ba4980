module.exports = [
  {
    "regex": "PhantomJS(?:/(\\d+[.\\d]+))?",
    "name": "PhantomJS",
    "version": "$1",
    "url": "https://phantomjs.org/"
  },
  {
    "regex": "IPinfoClient/.*/(\\d+[.\\d]+)",
    "name": "IPinfo",
    "version": "$1",
    "url": "https://github.com/ipinfo"
  },
  {
    "regex": "kiwi-tcms/(\\d+[.\\d]+)",
    "name": "Kiwi TCMS",
    "version": "$1",
    "url": "https://kiwitcms.org"
  },
  {
    "regex": "tcms-api/(\\d+[.\\d]+)",
    "name": "Kiwi TCMS API",
    "version": "$1",
    "url": "https://kiwitcms.org"
  },
  {
    "regex": "Fuzz Faster U Fool v(\\d+[.\\d]+)",
    "name": "FFUF",
    "version": "$1",
    "url": "https://github.com/ffuf/ffuf"
  },
  {
    "regex": "Slim Framework",
    "name": "Slim Framework",
    "version": "",
    "url": "https://www.slimframework.com/"
  },
  {
    "regex": "msray-plus",
    "name": "Msray-Plus",
    "version": "",
    "url": "https://github.com/super-l/msray"
  },
  {
    "regex": "HTMLParser(?:/(\\d+[.\\d]+))?",
    "name": "HTML Parser",
    "version": "$1",
    "url": "https://htmlparser.sourceforge.net/"
  },
  {
    "regex": "^got(?:/(\\d+\\.[.\\d]+))? \\(",
    "name": "got",
    "version": "$1",
    "url": "https://github.com/sindresorhus/got"
  },
  {
    "regex": "Typhoeus",
    "name": "Typhoeus",
    "version": "",
    "url": "https://github.com/typhoeus/typhoeus"
  },
  {
    "regex": "req/v([.\\d]+)",
    "name": "req",
    "version": "$1",
    "url": "https://github.com/imroc/req"
  },
  {
    "regex": "quic-go[ -]HTTP/3",
    "name": "quic-go",
    "version": "",
    "url": "https://github.com/lucas-clemente/quic-go"
  },
  {
    "regex": "azure-data-factory(?:/(\\d+[.\\d]+))?",
    "name": "Azure Data Factory",
    "version": "$1",
    "url": "https://azure.microsoft.com/en-us/products/data-factory/"
  },
  {
    "regex": "Dart/(\\d+[.\\d]+)",
    "name": "Dart",
    "version": "$1",
    "url": "https://dart.dev/"
  },
  {
    "regex": "r-curl(?:/(\\d+[.\\d]+))?",
    "name": "r-curl",
    "version": "$1",
    "url": "https://github.com/jeroen/curl"
  },
  {
    "regex": "python-httpx(?:/(\\d+[.\\d]+))?",
    "name": "HTTPX",
    "version": "$1",
    "url": "https://www.python-httpx.org/"
  },
  {
    "regex": "fasthttp(?:/(\\d+[.\\d]+))?",
    "name": "fasthttp",
    "version": "$1",
    "url": "https://github.com/valyala/fasthttp"
  },
  {
    "regex": "geoipupdate(?:/(\\d+[.\\d]+))?",
    "name": "GeoIP Update",
    "version": "$1",
    "url": "https://github.com/maxmind/geoipupdate"
  },
  {
    "regex": "PHP-Curl-Class(?:/(\\d+[.\\d]+))?",
    "name": "PHP cURL Class",
    "version": "$1",
    "url": "https://github.com/php-curl-class/php-curl-class"
  },
  {
    "regex": "Cpanel-HTTP-Client(?:/(\\d+[.\\d]+))?",
    "name": "cPanel HTTP Client",
    "version": "$1",
    "url": "https://www.cpanel.net/"
  },
  {
    "regex": "AnyEvent-HTTP(?:/(\\d+[.\\d]+))?",
    "name": "AnyEvent HTTP",
    "version": "$1",
    "url": "http://software.schmorp.de/pkg/AnyEvent"
  },
  {
    "regex": "SlimerJS/(\\d+[.\\d]+)",
    "name": "SlimerJS",
    "version": "$1",
    "url": "https://www.slimerjs.org/"
  },
  {
    "regex": "Jaunt/(\\d+[.\\d]+)",
    "name": "Jaunt",
    "version": "$1",
    "url": "https://jaunt-api.com/"
  },
  {
    "regex": "Cypress/(\\d+[.\\d]+)",
    "name": "Cypress",
    "version": "$1",
    "url": "https://github.com/cypress-io/cypress"
  },
  {
    "regex": "Wget(?:/(\\d+[.\\d]+))?",
    "name": "Wget",
    "version": "$1"
  },
  {
    "regex": "Guzzle(?:Http)?(?:/(\\d+[.\\d]+))?",
    "name": "Guzzle (PHP HTTP Client)",
    "version": "$1"
  },
  {
    "regex": "^Symfony HttpClient/",
    "name": "Symfony",
    "version": "$1"
  },
  {
    "regex": "(?:lib)?curl(?:/(\\d+[.\\d]+))?",
    "name": "curl",
    "version": "$1"
  },
  {
    "regex": "python-requests(?:/(\\d+[.\\d]+))?",
    "name": "Python Requests",
    "version": "$1"
  },
  {
    "regex": "Python-httplib2(?:/(\\d+[.\\d]+))?",
    "name": "httplib2",
    "version": "$1",
    "url": "https://pypi.org/project/httplib2/"
  },
  {
    "regex": "Python-urllib3?(?:/?(\\d+[.\\d]+))?",
    "name": "Python urllib",
    "version": "$1"
  },
  {
    "regex": "Apache-HttpClient(?:/?(\\d+[.\\d]+))?",
    "name": "Apache HTTP Client",
    "version": "$1"
  },
  {
    "regex": "Java-http-client(?:/?(\\d+[.\\d]+))?",
    "name": "Java HTTP Client",
    "version": "$1"
  },
  {
    "regex": "Java/?(\\d+[.\\d]+)",
    "name": "Java",
    "version": "$1"
  },
  {
    "regex": "(?:perlclient|libwww-perl)(?:/?(\\d+[.\\d]+))?",
    "name": "Perl",
    "version": "$1"
  },
  {
    "regex": "grpc-java-okhttp/([\\d.]+)",
    "name": "gRPC-Java",
    "version": "$1",
    "url": "https://github.com/grpc/grpc-java"
  },
  {
    "regex": "(?:okhttp|network-okhttp3)/([\\d.]+)",
    "name": "OkHttp",
    "version": "$1"
  },
  {
    "regex": "okhttp3-([\\d.]+)",
    "name": "OkHttp",
    "version": "$1"
  },
  {
    "regex": "HTTP_Request2(?:/(\\d+[.\\d]+))?",
    "name": "HTTP_Request2",
    "version": "$1",
    "url": "https://pear.php.net/package/http_request2"
  },
  {
    "regex": "Mechanize(?:/(\\d+[.\\d]+))?",
    "name": "Mechanize",
    "version": "$1",
    "url": "https://github.com/sparklemotion/mechanize"
  },
  {
    "regex": "aiohttp(?:/(\\d+[.\\d]+))?",
    "name": "aiohttp",
    "version": "$1"
  },
  {
    "regex": "Google-HTTP-Java-Client(?:/(\\d+[\\.\\w-]+))?",
    "name": "Google HTTP Java Client",
    "version": "$1"
  },
  {
    "regex": "WWW-Mechanize(?:/(\\d+[.\\d]+))?",
    "name": "WWW-Mechanize",
    "version": "$1"
  },
  {
    "regex": "Faraday(?: v(\\d+[.\\d]+))?",
    "name": "Faraday",
    "version": "$1",
    "url": "https://github.com/lostisland/faraday"
  },
  {
    "regex": "(?:Go-http-client|^Go )/?(?:(\\d+[.\\d]+))?(?: package http)?",
    "name": "Go-http-client",
    "version": "$1"
  },
  {
    "regex": "urlgrabber(?:/(\\d+[.\\d]+))?",
    "name": "urlgrabber (yum)",
    "version": "$1"
  },
  {
    "regex": "libdnf(?:/(\\d+[.\\d]+))?",
    "name": "libdnf",
    "version": "$1"
  },
  {
    "regex": "HTTPie(?:/(\\d+[.\\d]+))?",
    "name": "HTTPie",
    "version": "$1"
  },
  {
    "regex": "rest-client/(\\d+\\.[.\\d]+) .*ruby",
    "name": "REST Client for Ruby",
    "version": "$1"
  },
  {
    "regex": "RestSharp/(\\d+[.\\d]+)",
    "name": "RestSharp",
    "version": "$1",
    "url": "https://github.com/restsharp/RestSharp"
  },
  {
    "regex": "scalaj-http/(\\d+[.\\d]+)",
    "name": "ScalaJ HTTP",
    "version": "$1",
    "url": "https://github.com/scalaj/scalaj-http"
  },
  {
    "regex": "REST::Client/(\\d+)",
    "name": "Perl REST::Client",
    "version": "$1",
    "url": "https://metacpan.org/pod/REST::Client"
  },
  {
    "regex": "node-fetch/?(\\d+[.\\d]+)?",
    "name": "Node Fetch",
    "version": "$1",
    "url": "https://github.com/node-fetch/node-fetch"
  },
  {
    "regex": "electron-fetch/?(\\d+[.\\d]+)?",
    "name": "Electron Fetch",
    "version": "$1",
    "url": "https://github.com/arantes555/electron-fetch"
  },
  {
    "regex": "ReactorNetty/(\\d+[.\\d]+)",
    "name": "ReactorNetty",
    "version": "$1",
    "url": "https://github.com/reactor/reactor-netty"
  },
  {
    "regex": "PostmanRuntime(?:/(\\d+[.\\d]+))?",
    "name": "Postman Desktop",
    "version": "$1",
    "url": "https://github.com/postmanlabs/postman-runtime"
  },
  {
    "regex": "insomnia(?:/(\\d+[.\\d]+))?",
    "name": "Insomnia REST Client",
    "version": "$1",
    "url": "https://insomnia.rest"
  },
  {
    "regex": "Jakarta Commons-HttpClient/([.\\d]+)",
    "name": "Jakarta Commons HttpClient",
    "version": "$1",
    "url": "https://hc.apache.org/httpclient-3.x"
  },
  {
    "regex": "WinHttp\\.WinHttpRequest.+([.\\d]+)",
    "name": "WinHttp WinHttpRequest",
    "version": "$1"
  },
  {
    "regex": "WinHTTP",
    "name": "Windows HTTP",
    "version": ""
  },
  {
    "regex": "Embarcadero URI Client/([.\\d]+)",
    "name": "Embarcadero URI Client",
    "version": "$1"
  },
  {
    "regex": "Mikrotik/([.\\d]+)",
    "name": "Mikrotik Fetch",
    "version": "$1"
  },
  {
    "regex": "GRequests(?:/(\\d+[.\\d]+))?",
    "name": "GRequests",
    "version": "$1"
  },
  {
    "regex": "akka-http/([.\\d]+)",
    "name": "Akka HTTP",
    "version": "$1"
  },
  {
    "regex": "aria2(?:/(\\d+[.\\d]+))?",
    "name": "Aria2",
    "version": "$1"
  },
  {
    "regex": "(?:BTWebClient/|^uTorrent/)",
    "name": "uTorrent",
    "version": ""
  },
  {
    "regex": "gvfs/(?:(\\d+[.\\d]+))?",
    "name": "gvfs",
    "version": "$1"
  },
  {
    "regex": "uclient-fetch",
    "name": "uclient-fetch",
    "version": ""
  },
  {
    "regex": "cpprestsdk/([.\\d]+)",
    "name": "C++ REST SDK",
    "version": "$1"
  },
  {
    "regex": "lua-resty-http/([.\\d]+).+ngx_",
    "name": "LUA OpenResty NGINX",
    "version": "$1"
  },
  {
    "regex": "unirest-java/([.\\d]+)",
    "name": "Unirest for Java",
    "version": "$1"
  },
  {
    "regex": "jsdom/([.\\d]+)",
    "name": "jsdom",
    "version": "$1"
  },
  {
    "regex": "hackney/([.\\d]+)",
    "name": "hackney",
    "version": "$1"
  },
  {
    "regex": "go-resty/([.\\d]+)",
    "name": "Resty",
    "version": "$1"
  },
  {
    "regex": "pa11y/([.\\d]+)",
    "name": "Pa11y",
    "version": "$1"
  },
  {
    "regex": "ultimate_sitemap_parser/([.\\d]+)",
    "name": "Ultimate Sitemap Parser",
    "version": "$1"
  },
  {
    "regex": "Artifactory/([.\\d]+)",
    "name": "Artifactory",
    "version": "$1"
  },
  {
    "regex": "BSRPC ([.\\d]+)",
    "name": "Open Build Service",
    "version": "$1"
  },
  {
    "regex": "Buildah/([.\\d]+)",
    "name": "Buildah",
    "version": "$1"
  },
  {
    "regex": "buildkit/v?([.\\d]+)",
    "name": "BuildKit",
    "version": "$1"
  },
  {
    "regex": "containerd/v?([.\\d]+)",
    "name": "Containerd",
    "version": "$1"
  },
  {
    "regex": "containers/([.\\d]+)",
    "name": "containers",
    "version": "$1"
  },
  {
    "regex": "cri-o/([.\\d]+)",
    "name": "cri-o",
    "version": "$1"
  },
  {
    "regex": "docker/([.\\d]+)",
    "name": "docker",
    "version": "$1"
  },
  {
    "regex": "go-containerregistry/v([.\\d]+)",
    "name": "go-container registry",
    "version": "$1"
  },
  {
    "regex": "libpod/([.\\d]+)",
    "name": "libpod",
    "version": "$1"
  },
  {
    "regex": "skopeo/([.\\d]+)",
    "name": "Skopeo",
    "version": "$1"
  },
  {
    "regex": "Helm/([.\\d]+)",
    "name": "Helm",
    "version": "$1"
  },
  {
    "regex": "harbor-registry-client",
    "name": "Harbor registry client",
    "version": ""
  },
  {
    "regex": "axios(?:/?(\\d+[.\\d]+))?",
    "name": "Axios",
    "version": "$1"
  },
  {
    "regex": "^CarrierWave/(\\d+\\.[.\\d]+)",
    "name": "CarrierWave",
    "version": "$1"
  },
  {
    "regex": "^Deno/(\\d+\\.[.\\d]+)",
    "name": "Deno",
    "version": "$1"
  },
  {
    "regex": "^Down/(\\d+\\.[.\\d]+)",
    "name": "Down",
    "version": "$1"
  },
  {
    "regex": "^Lavf/",
    "name": "ffmpeg",
    "version": "$1"
  },
  {
    "regex": "^FileDownloader/(\\d+\\.[.\\d]+)",
    "name": "FileDownloader",
    "version": "$1"
  },
  {
    "regex": "^git-annex/(\\d+\\.[.\\d]+)",
    "name": "git-annex",
    "version": "$1"
  },
  {
    "regex": "^GStreamer(?: souphttpsrc)[ /](\\d+\\.[.\\d]+)?",
    "name": "GStreamer",
    "version": "$1"
  },
  {
    "regex": "^HTTP-Tiny/(\\d+\\.[.\\d]+)",
    "name": "HTTP:Tiny",
    "version": "$1"
  },
  {
    "regex": "KaiOS Downloader",
    "name": "KaiOS Downloader",
    "version": ""
  },
  {
    "regex": "^libsoup/(\\d+\\.[.\\d]+)",
    "name": "libsoup",
    "version": "$1"
  },
  {
    "regex": "^Android\\.LVLDM$",
    "name": "Android License Verification Library",
    "version": "$1"
  },
  {
    "regex": "^PRDownloader$",
    "name": "PRDownloader",
    "version": ""
  },
  {
    "regex": "^reqwest/(\\d+\\.[.\\d]+)",
    "name": "reqwest",
    "version": "$1"
  },
  {
    "regex": "^resty-requests",
    "name": "resty-requests",
    "version": ""
  },
  {
    "regex": "^Ruby",
    "name": "ruby",
    "version": ""
  },
  {
    "regex": "^SafariViewService/(\\d+\\.[.\\d]+)",
    "name": "Safari View Service",
    "version": "$1"
  },
  {
    "regex": "^undici$",
    "name": "undici",
    "version": ""
  },
  {
    "regex": "^URL/Emacs Emacs/(\\d+\\.[.\\d]+)",
    "name": "Emacs",
    "version": "$1"
  },
  {
    "regex": "^FDM[ /]([\\d.]+)",
    "name": "Free Download Manager",
    "version": "$1"
  },
  {
    "regex": "OkDownload/([\\d.]+)",
    "name": "OKDownload Library",
    "version": "$1"
  },
  {
    "regex": "^Libsyn4-?(?:peek|download)$",
    "name": "Libsyn",
    "version": ""
  },
  {
    "regex": "AppleCoreMedia/1\\.0\\.0",
    "name": "iOS Application",
    "version": ""
  },
  {
    "regex": "cpp-httplib(?:/(\\d+[.\\d]+))?",
    "name": "cpp-httplib",
    "version": "$1",
    "url": "https://github.com/yhirose/cpp-httplib"
  },
  {
    "regex": "Definitely-Not-Requests",
    "name": "Requests",
    "version": "",
    "url": "https://github.com/psf/requests"
  },
  {
    "regex": "Stealer ([\\d.]+)",
    "name": "Stealer",
    "version": "$1",
    "url": "https://github.com/hotrush/stealer/"
  },
  {
    "regex": "Mandrill-PHP(?:/(\\d+[.\\d]+))?",
    "name": "Mandrill PHP",
    "version": "$1",
    "url": "https://bitbucket.org/mailchimp/mandrill-api-php/src/master/"
  },
  {
    "regex": "^Podgrab",
    "name": "Podgrab",
    "version": "",
    "url": "https://github.com/akhilrex/podgrab"
  },
  {
    "regex": "^Podcast Provider.*?Radio Downloader ([\\d.]+)",
    "name": "Radio Downloader",
    "version": "$1",
    "url": "https://nerdoftheherd.com/tools/radiodld/"
  },
  {
    "regex": "^ESP32 HTTP Client/([\\d.]+)",
    "name": "ESP32 HTTP Client",
    "version": "$1",
    "url": "https://github.com/espressif/arduino-esp32"
  },
  {
    "regex": "babashka\\.http-client(?:/(\\d+[.\\d]+))?",
    "name": "Babashka HTTP Client",
    "version": "$1",
    "url": "https://github.com/babashka/http-client"
  },
  {
    "regex": "http\\.rb(?:/(\\d+[.\\d]+))?",
    "name": "http.rb",
    "version": "$1",
    "url": "https://github.com/httprb/http"
  },
  {
    "regex": "node-superagent(?:/(\\d+[.\\d]+))?",
    "name": "superagent",
    "version": "$1",
    "url": "https://github.com/ladjs/superagent"
  },
  {
    "regex": "CakePHP",
    "name": "CakePHP",
    "version": "",
    "url": "https://www.cakephp.org/"
  },
  {
    "regex": "request\\.js",
    "name": "request",
    "version": "",
    "url": "https://github.com/request/request"
  },
  {
    "regex": "qbhttp(?:/(\\d+[.\\d]+))?",
    "name": "QbHttp",
    "version": "$1",
    "url": "https://github.com/OpenQb/QbHttp"
  },
  {
    "regex": "httprs(?:/(\\d+[.\\d]+))?",
    "name": "httprs",
    "version": "$1",
    "url": "https://github.com/http-server-rs/http-server"
  },
  {
    "regex": "Boto3(?:/(\\d+[.\\d]+))?",
    "name": "Boto3",
    "version": "$1",
    "url": "https://github.com/boto/boto3"
  },
  {
    "regex": "Python-xmlrpc(?:/(\\d+[.\\d]+))?",
    "name": "XML-RPC",
    "version": "$1",
    "url": "https://docs.python.org/3/library/xmlrpc.html"
  },
  {
    "regex": "ICAP-Client-Library(?:/(\\d+[.\\d]+))?",
    "name": "ICAP Client",
    "version": "$1",
    "url": "https://github.com/Peoplecantfly/icapserver"
  },
  {
    "regex": "Cygwin-Setup(?:/(\\d+[.\\d]+))?",
    "name": "Cygwin",
    "version": "$1",
    "url": "https://www.cygwin.com/"
  },
  {
    "regex": "azsdk-python-storage-blob(?:/(\\d+[.\\d]+))?",
    "name": "Azure Blob Storage",
    "version": "$1",
    "url": "https://learn.microsoft.com/en-us/azure/storage/blobs/storage-quickstart-blobs-python"
  },
  {
    "regex": "trafilatura(?:/(\\d+[.\\d]+))?",
    "name": "trafilatura",
    "version": "$1",
    "url": "https://github.com/adbar/trafilatura"
  },
  {
    "regex": "sqlmap(?:/(\\d+[.\\d]+))?",
    "name": "sqlmap",
    "version": "$1",
    "url": "https://sqlmap.org/"
  },
  {
    "regex": "vimeo\\.php(?: (\\d+[.\\d]+))?",
    "name": "vimeo.php",
    "version": "$1",
    "url": "https://github.com/vimeo/vimeo.php"
  },
  {
    "regex": "^PHP/?(\\d+[.\\d]+)",
    "name": "PHP",
    "version": "$1",
    "url": ""
  },
  {
    "regex": "go-network-v(\\d+[.\\d]+)",
    "name": "go-network",
    "version": "$1",
    "url": ""
  }
];
