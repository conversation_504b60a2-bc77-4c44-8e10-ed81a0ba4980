.TH "NPM\-VIEW" "1" "March 2017" "" ""
.SH "NAME"
\fBnpm-view\fR \- View registry info
.SH SYNOPSIS
.P
.RS 2
.nf
npm view [@<scope>/]<name>[@<version>] [<field>[\.<subfield>]\.\.\.]
npm v [@<scope>/]<name>[@<version>] [<field>[\.<subfield>]\.\.\.]
.fi
.RE
.SH DESCRIPTION
.P
This command shows data about a package and prints it to the stream
referenced by the \fBoutfd\fP config, which defaults to stdout\.
.P
To show the package registry entry for the \fBconnect\fP package, you can do
this:
.P
.RS 2
.nf
npm view connect
.fi
.RE
.P
The default version is "latest" if unspecified\.
.P
Field names can be specified after the package descriptor\.
For example, to show the dependencies of the \fBronn\fP package at version
0\.3\.5, you could do the following:
.P
.RS 2
.nf
npm view ronn@0\.3\.5 dependencies
.fi
.RE
.P
You can view child fields by separating them with a period\.
To view the git repository URL for the latest version of npm, you could
do this:
.P
.RS 2
.nf
npm view npm repository\.url
.fi
.RE
.P
This makes it easy to view information about a dependency with a bit of
shell scripting\.  For example, to view all the data about the version of
opts that ronn depends on, you can do this:
.P
.RS 2
.nf
npm view opts@$(npm view ronn dependencies\.opts)
.fi
.RE
.P
For fields that are arrays, requesting a non\-numeric field will return
all of the values from the objects in the list\.  For example, to get all
the contributor names for the "express" project, you can do this:
.P
.RS 2
.nf
npm view express contributors\.email
.fi
.RE
.P
You may also use numeric indices in square braces to specifically select
an item in an array field\.  To just get the email address of the first
contributor in the list, you can do this:
.P
.RS 2
.nf
npm view express contributors[0]\.email
.fi
.RE
.P
Multiple fields may be specified, and will be printed one after another\.
For exampls, to get all the contributor names and email addresses, you
can do this:
.P
.RS 2
.nf
npm view express contributors\.name contributors\.email
.fi
.RE
.P
"Person" fields are shown as a string if they would be shown as an
object\.  So, for example, this will show the list of npm contributors in
the shortened string format\.  (See npm help 5 \fBpackage\.json\fP for more on this\.)
.P
.RS 2
.nf
npm view npm contributors
.fi
.RE
.P
If a version range is provided, then data will be printed for every
matching version of the package\.  This will show which version of jsdom
was required by each matching version of yui3:
.P
.RS 2
.nf
npm view yui3@'>0\.5\.4' dependencies\.jsdom
.fi
.RE
.P
To show the \fBconnect\fP package version history, you can do
this:
.P
.RS 2
.nf
npm view connect versions
.fi
.RE
.SH OUTPUT
.P
If only a single string field for a single version is output, then it
will not be colorized or quoted, so as to enable piping the output to
another command\. If the field is an object, it will be output as a JavaScript object literal\.
.P
If the \-\-json flag is given, the outputted fields will be JSON\.
.P
If the version range matches multiple versions, than each printed value
will be prefixed with the version it applies to\.
.P
If multiple fields are requested, than each of them are prefixed with
the field name\.
.SH SEE ALSO
.RS 0
.IP \(bu 2
npm help search
.IP \(bu 2
npm help 7 registry
.IP \(bu 2
npm help config
.IP \(bu 2
npm help 7 config
.IP \(bu 2
npm help 5 npmrc
.IP \(bu 2
npm help docs

.RE

