<!doctype html>
<html>
  <title>semver</title>
  <meta charset="utf-8">
  <link rel="stylesheet" type="text/css" href="../../static/style.css">
  <link rel="canonical" href="https://www.npmjs.org/doc/misc/semver.html">
  <script async=true src="../../static/toc.js"></script>

  <body>
    <div id="wrapper">

<h1><a href="../misc/semver.html">semver</a></h1> <p>The semantic versioner for npm</p>
<h2 id="usage">Usage</h2>
<pre><code>$ npm install semver

semver.valid(&#39;1.2.3&#39;) // &#39;1.2.3&#39;
semver.valid(&#39;a.b.c&#39;) // null
semver.clean(&#39;  =v1.2.3   &#39;) // &#39;1.2.3&#39;
semver.satisfies(&#39;1.2.3&#39;, &#39;1.x || &gt;=2.5.0 || 5.0.0 - 7.2.3&#39;) // true
semver.gt(&#39;1.2.3&#39;, &#39;9.8.7&#39;) // false
semver.lt(&#39;1.2.3&#39;, &#39;9.8.7&#39;) // true
</code></pre><p>As a command-line utility:</p>
<pre><code>$ semver -h

Usage: semver &lt;version&gt; [&lt;version&gt; [...]] [-r &lt;range&gt; | -i &lt;inc&gt; | --preid &lt;identifier&gt; | -l | -rv]
Test if version(s) satisfy the supplied range(s), and sort them.

Multiple versions or ranges may be supplied, unless increment
option is specified.  In that case, only a single version may
be used, and it is incremented by the specified level

Program exits successfully if any valid version satisfies
all supplied ranges, and prints all satisfying versions.

If no versions are valid, or ranges are not satisfied,
then exits failure.

Versions are printed in ascending order, so supplying
multiple versions to the utility will just sort them.
</code></pre><h2 id="versions">Versions</h2>
<p>A &quot;version&quot; is described by the <code>v2.0.0</code> specification found at
<a href="http://semver.org/">http://semver.org/</a>.</p>
<p>A leading <code>&quot;=&quot;</code> or <code>&quot;v&quot;</code> character is stripped off and ignored.</p>
<h2 id="ranges">Ranges</h2>
<p>A <code>version range</code> is a set of <code>comparators</code> which specify versions
that satisfy the range.</p>
<p>A <code>comparator</code> is composed of an <code>operator</code> and a <code>version</code>.  The set
of primitive <code>operators</code> is:</p>
<ul>
<li><code>&lt;</code> Less than</li>
<li><code>&lt;=</code> Less than or equal to</li>
<li><code>&gt;</code> Greater than</li>
<li><code>&gt;=</code> Greater than or equal to</li>
<li><code>=</code> Equal.  If no operator is specified, then equality is assumed,
so this operator is optional, but MAY be included.</li>
</ul>
<p>For example, the comparator <code>&gt;=1.2.7</code> would match the versions
<code>1.2.7</code>, <code>1.2.8</code>, <code>2.5.3</code>, and <code>1.3.9</code>, but not the versions <code>1.2.6</code>
or <code>1.1.0</code>.</p>
<p>Comparators can be joined by whitespace to form a <code>comparator set</code>,
which is satisfied by the <strong>intersection</strong> of all of the comparators
it includes.</p>
<p>A range is composed of one or more comparator sets, joined by <code>||</code>.  A
version matches a range if and only if every comparator in at least
one of the <code>||</code>-separated comparator sets is satisfied by the version.</p>
<p>For example, the range <code>&gt;=1.2.7 &lt;1.3.0</code> would match the versions
<code>1.2.7</code>, <code>1.2.8</code>, and <code>1.2.99</code>, but not the versions <code>1.2.6</code>, <code>1.3.0</code>,
or <code>1.1.0</code>.</p>
<p>The range <code>1.2.7 || &gt;=1.2.9 &lt;2.0.0</code> would match the versions <code>1.2.7</code>,
<code>1.2.9</code>, and <code>1.4.6</code>, but not the versions <code>1.2.8</code> or <code>2.0.0</code>.</p>
<h3 id="prerelease-tags">Prerelease Tags</h3>
<p>If a version has a prerelease tag (for example, <code>1.2.3-alpha.3</code>) then
it will only be allowed to satisfy comparator sets if at least one
comparator with the same <code>[major, minor, patch]</code> tuple also has a
prerelease tag.</p>
<p>For example, the range <code>&gt;1.2.3-alpha.3</code> would be allowed to match the
version <code>1.2.3-alpha.7</code>, but it would <em>not</em> be satisfied by
<code>3.4.5-alpha.9</code>, even though <code>3.4.5-alpha.9</code> is technically &quot;greater
than&quot; <code>1.2.3-alpha.3</code> according to the SemVer sort rules.  The version
range only accepts prerelease tags on the <code>1.2.3</code> version.  The
version <code>3.4.5</code> <em>would</em> satisfy the range, because it does not have a
prerelease flag, and <code>3.4.5</code> is greater than <code>1.2.3-alpha.7</code>.</p>
<p>The purpose for this behavior is twofold.  First, prerelease versions
frequently are updated very quickly, and contain many breaking changes
that are (by the author&#39;s design) not yet fit for public consumption.
Therefore, by default, they are excluded from range matching
semantics.</p>
<p>Second, a user who has opted into using a prerelease version has
clearly indicated the intent to use <em>that specific</em> set of
alpha/beta/rc versions.  By including a prerelease tag in the range,
the user is indicating that they are aware of the risk.  However, it
is still not appropriate to assume that they have opted into taking a
similar risk on the <em>next</em> set of prerelease versions.</p>
<h4 id="prerelease-identifiers">Prerelease Identifiers</h4>
<p>The method <code>.inc</code> takes an additional <code>identifier</code> string argument that
will append the value of the string as a prerelease identifier:</p>
<pre><code class="lang-javascript">&gt; semver.inc(&#39;1.2.3&#39;, &#39;prerelease&#39;, &#39;beta&#39;)
&#39;1.2.4-beta.0&#39;
</code></pre>
<p>command-line example:</p>
<pre><code class="lang-shell">$ semver 1.2.3 -i prerelease --preid beta
1.2.4-beta.0
</code></pre>
<p>Which then can be used to increment further:</p>
<pre><code class="lang-shell">$ semver 1.2.4-beta.0 -i prerelease
1.2.4-beta.1
</code></pre>
<h3 id="advanced-range-syntax">Advanced Range Syntax</h3>
<p>Advanced range syntax desugars to primitive comparators in
deterministic ways.</p>
<p>Advanced ranges may be combined in the same way as primitive
comparators using white space or <code>||</code>.</p>
<h4 id="hyphen-ranges-x-y-z-a-b-c-">Hyphen Ranges <code>X.Y.Z - A.B.C</code></h4>
<p>Specifies an inclusive set.</p>
<ul>
<li><code>1.2.3 - 2.3.4</code> := <code>&gt;=1.2.3 &lt;=2.3.4</code></li>
</ul>
<p>If a partial version is provided as the first version in the inclusive
range, then the missing pieces are replaced with zeroes.</p>
<ul>
<li><code>1.2 - 2.3.4</code> := <code>&gt;=1.2.0 &lt;=2.3.4</code></li>
</ul>
<p>If a partial version is provided as the second version in the
inclusive range, then all versions that start with the supplied parts
of the tuple are accepted, but nothing that would be greater than the
provided tuple parts.</p>
<ul>
<li><code>1.2.3 - 2.3</code> := <code>&gt;=1.2.3 &lt;2.4.0</code></li>
<li><code>1.2.3 - 2</code> := <code>&gt;=1.2.3 &lt;3.0.0</code></li>
</ul>
<h4 id="x-ranges-1-2-x-1-x-1-2-">X-Ranges <code>1.2.x</code> <code>1.X</code> <code>1.2.*</code> <code>*</code></h4>
<p>Any of <code>X</code>, <code>x</code>, or <code>*</code> may be used to &quot;stand in&quot; for one of the
numeric values in the <code>[major, minor, patch]</code> tuple.</p>
<ul>
<li><code>*</code> := <code>&gt;=0.0.0</code> (Any version satisfies)</li>
<li><code>1.x</code> := <code>&gt;=1.0.0 &lt;2.0.0</code> (Matching major version)</li>
<li><code>1.2.x</code> := <code>&gt;=1.2.0 &lt;1.3.0</code> (Matching major and minor versions)</li>
</ul>
<p>A partial version range is treated as an X-Range, so the special
character is in fact optional.</p>
<ul>
<li><code>&quot;&quot;</code> (empty string) := <code>*</code> := <code>&gt;=0.0.0</code></li>
<li><code>1</code> := <code>1.x.x</code> := <code>&gt;=1.0.0 &lt;2.0.0</code></li>
<li><code>1.2</code> := <code>1.2.x</code> := <code>&gt;=1.2.0 &lt;1.3.0</code></li>
</ul>
<h4 id="tilde-ranges-1-2-3-1-2-1-">Tilde Ranges <code>~1.2.3</code> <code>~1.2</code> <code>~1</code></h4>
<p>Allows patch-level changes if a minor version is specified on the
comparator.  Allows minor-level changes if not.</p>
<ul>
<li><code>~1.2.3</code> := <code>&gt;=1.2.3 &lt;1.(2+1).0</code> := <code>&gt;=1.2.3 &lt;1.3.0</code></li>
<li><code>~1.2</code> := <code>&gt;=1.2.0 &lt;1.(2+1).0</code> := <code>&gt;=1.2.0 &lt;1.3.0</code> (Same as <code>1.2.x</code>)</li>
<li><code>~1</code> := <code>&gt;=1.0.0 &lt;(1+1).0.0</code> := <code>&gt;=1.0.0 &lt;2.0.0</code> (Same as <code>1.x</code>)</li>
<li><code>~0.2.3</code> := <code>&gt;=0.2.3 &lt;0.(2+1).0</code> := <code>&gt;=0.2.3 &lt;0.3.0</code></li>
<li><code>~0.2</code> := <code>&gt;=0.2.0 &lt;0.(2+1).0</code> := <code>&gt;=0.2.0 &lt;0.3.0</code> (Same as <code>0.2.x</code>)</li>
<li><code>~0</code> := <code>&gt;=0.0.0 &lt;(0+1).0.0</code> := <code>&gt;=0.0.0 &lt;1.0.0</code> (Same as <code>0.x</code>)</li>
<li><code>~1.2.3-beta.2</code> := <code>&gt;=1.2.3-beta.2 &lt;1.3.0</code> Note that prereleases in
the <code>1.2.3</code> version will be allowed, if they are greater than or
equal to <code>beta.2</code>.  So, <code>1.2.3-beta.4</code> would be allowed, but
<code>1.2.4-beta.2</code> would not, because it is a prerelease of a
different <code>[major, minor, patch]</code> tuple.</li>
</ul>
<h4 id="caret-ranges-1-2-3-0-2-5-0-0-4-">Caret Ranges <code>^1.2.3</code> <code>^0.2.5</code> <code>^0.0.4</code></h4>
<p>Allows changes that do not modify the left-most non-zero digit in the
<code>[major, minor, patch]</code> tuple.  In other words, this allows patch and
minor updates for versions <code>1.0.0</code> and above, patch updates for
versions <code>0.X &gt;=0.1.0</code>, and <em>no</em> updates for versions <code>0.0.X</code>.</p>
<p>Many authors treat a <code>0.x</code> version as if the <code>x</code> were the major
&quot;breaking-change&quot; indicator.</p>
<p>Caret ranges are ideal when an author may make breaking changes
between <code>0.2.4</code> and <code>0.3.0</code> releases, which is a common practice.
However, it presumes that there will <em>not</em> be breaking changes between
<code>0.2.4</code> and <code>0.2.5</code>.  It allows for changes that are presumed to be
additive (but non-breaking), according to commonly observed practices.</p>
<ul>
<li><code>^1.2.3</code> := <code>&gt;=1.2.3 &lt;2.0.0</code></li>
<li><code>^0.2.3</code> := <code>&gt;=0.2.3 &lt;0.3.0</code></li>
<li><code>^0.0.3</code> := <code>&gt;=0.0.3 &lt;0.0.4</code></li>
<li><code>^1.2.3-beta.2</code> := <code>&gt;=1.2.3-beta.2 &lt;2.0.0</code> Note that prereleases in
the <code>1.2.3</code> version will be allowed, if they are greater than or
equal to <code>beta.2</code>.  So, <code>1.2.3-beta.4</code> would be allowed, but
<code>1.2.4-beta.2</code> would not, because it is a prerelease of a
different <code>[major, minor, patch]</code> tuple.</li>
<li><code>^0.0.3-beta</code> := <code>&gt;=0.0.3-beta &lt;0.0.4</code>  Note that prereleases in the
<code>0.0.3</code> version <em>only</em> will be allowed, if they are greater than or
equal to <code>beta</code>.  So, <code>0.0.3-pr.2</code> would be allowed.</li>
</ul>
<p>When parsing caret ranges, a missing <code>patch</code> value desugars to the
number <code>0</code>, but will allow flexibility within that value, even if the
major and minor versions are both <code>0</code>.</p>
<ul>
<li><code>^1.2.x</code> := <code>&gt;=1.2.0 &lt;2.0.0</code></li>
<li><code>^0.0.x</code> := <code>&gt;=0.0.0 &lt;0.1.0</code></li>
<li><code>^0.0</code> := <code>&gt;=0.0.0 &lt;0.1.0</code></li>
</ul>
<p>A missing <code>minor</code> and <code>patch</code> values will desugar to zero, but also
allow flexibility within those values, even if the major version is
zero.</p>
<ul>
<li><code>^1.x</code> := <code>&gt;=1.0.0 &lt;2.0.0</code></li>
<li><code>^0.x</code> := <code>&gt;=0.0.0 &lt;1.0.0</code></li>
</ul>
<h3 id="range-grammar">Range Grammar</h3>
<p>Putting all this together, here is a Backus-Naur grammar for ranges,
for the benefit of parser authors:</p>
<pre><code class="lang-bnf">range-set  ::= range ( logical-or range ) *
logical-or ::= ( &#39; &#39; ) * &#39;||&#39; ( &#39; &#39; ) *
range      ::= hyphen | simple ( &#39; &#39; simple ) * | &#39;&#39;
hyphen     ::= partial &#39; - &#39; partial
simple     ::= primitive | partial | tilde | caret
primitive  ::= ( &#39;&lt;&#39; | &#39;&gt;&#39; | &#39;&gt;=&#39; | &#39;&lt;=&#39; | &#39;=&#39; | ) partial
partial    ::= xr ( &#39;.&#39; xr ( &#39;.&#39; xr qualifier ? )? )?
xr         ::= &#39;x&#39; | &#39;X&#39; | &#39;*&#39; | nr
nr         ::= &#39;0&#39; | [&#39;1&#39;-&#39;9&#39;][&#39;0&#39;-&#39;9&#39;]+
tilde      ::= &#39;~&#39; partial
caret      ::= &#39;^&#39; partial
qualifier  ::= ( &#39;-&#39; pre )? ( &#39;+&#39; build )?
pre        ::= parts
build      ::= parts
parts      ::= part ( &#39;.&#39; part ) *
part       ::= nr | [-0-9A-Za-z]+
</code></pre>
<h2 id="functions">Functions</h2>
<p>All methods and classes take a final <code>loose</code> boolean argument that, if
true, will be more forgiving about not-quite-valid semver strings.
The resulting output will always be 100% strict, of course.</p>
<p>Strict-mode Comparators and Ranges will be strict about the SemVer
strings that they parse.</p>
<ul>
<li><code>valid(v)</code>: Return the parsed version, or null if it&#39;s not valid.</li>
<li><code>inc(v, release)</code>: Return the version incremented by the release
type (<code>major</code>,   <code>premajor</code>, <code>minor</code>, <code>preminor</code>, <code>patch</code>,
<code>prepatch</code>, or <code>prerelease</code>), or null if it&#39;s not valid<ul>
<li><code>premajor</code> in one call will bump the version up to the next major
version and down to a prerelease of that major version.
<code>preminor</code>, and <code>prepatch</code> work the same way.</li>
<li>If called from a non-prerelease version, the <code>prerelease</code> will work the
same as <code>prepatch</code>. It increments the patch version, then makes a
prerelease. If the input version is already a prerelease it simply
increments it.</li>
</ul>
</li>
<li><code>major(v)</code>: Return the major version number.</li>
<li><code>minor(v)</code>: Return the minor version number.</li>
<li><code>patch(v)</code>: Return the patch version number.</li>
</ul>
<h3 id="comparison">Comparison</h3>
<ul>
<li><code>gt(v1, v2)</code>: <code>v1 &gt; v2</code></li>
<li><code>gte(v1, v2)</code>: <code>v1 &gt;= v2</code></li>
<li><code>lt(v1, v2)</code>: <code>v1 &lt; v2</code></li>
<li><code>lte(v1, v2)</code>: <code>v1 &lt;= v2</code></li>
<li><code>eq(v1, v2)</code>: <code>v1 == v2</code> This is true if they&#39;re logically equivalent,
even if they&#39;re not the exact same string.  You already know how to
compare strings.</li>
<li><code>neq(v1, v2)</code>: <code>v1 != v2</code> The opposite of <code>eq</code>.</li>
<li><code>cmp(v1, comparator, v2)</code>: Pass in a comparison string, and it&#39;ll call
the corresponding function above.  <code>&quot;===&quot;</code> and <code>&quot;!==&quot;</code> do simple
string comparison, but are included for completeness.  Throws if an
invalid comparison string is provided.</li>
<li><code>compare(v1, v2)</code>: Return <code>0</code> if <code>v1 == v2</code>, or <code>1</code> if <code>v1</code> is greater, or <code>-1</code> if
<code>v2</code> is greater.  Sorts in ascending order if passed to <code>Array.sort()</code>.</li>
<li><code>rcompare(v1, v2)</code>: The reverse of compare.  Sorts an array of versions
in descending order when passed to <code>Array.sort()</code>.</li>
<li><code>diff(v1, v2)</code>: Returns difference between two versions by the release type
(<code>major</code>, <code>premajor</code>, <code>minor</code>, <code>preminor</code>, <code>patch</code>, <code>prepatch</code>, or <code>prerelease</code>),
or null if the versions are the same.</li>
</ul>
<h3 id="ranges">Ranges</h3>
<ul>
<li><code>validRange(range)</code>: Return the valid range or null if it&#39;s not valid</li>
<li><code>satisfies(version, range)</code>: Return true if the version satisfies the
range.</li>
<li><code>maxSatisfying(versions, range)</code>: Return the highest version in the list
that satisfies the range, or <code>null</code> if none of them do.</li>
<li><code>gtr(version, range)</code>: Return <code>true</code> if version is greater than all the
versions possible in the range.</li>
<li><code>ltr(version, range)</code>: Return <code>true</code> if version is less than all the
versions possible in the range.</li>
<li><code>outside(version, range, hilo)</code>: Return true if the version is outside
the bounds of the range in either the high or low direction.  The
<code>hilo</code> argument must be either the string <code>&#39;&gt;&#39;</code> or <code>&#39;&lt;&#39;</code>.  (This is
the function called by <code>gtr</code> and <code>ltr</code>.)</li>
</ul>
<p>Note that, since ranges may be non-contiguous, a version might not be
greater than a range, less than a range, <em>or</em> satisfy a range!  For
example, the range <code>1.2 &lt;1.2.9 || &gt;2.0.0</code> would have a hole from <code>1.2.9</code>
until <code>2.0.0</code>, so the version <code>1.2.10</code> would not be greater than the
range (because <code>2.0.1</code> satisfies, which is higher), nor less than the
range (since <code>1.2.8</code> satisfies, which is lower), and it also does not
satisfy the range.</p>
<p>If you want to know if a version satisfies or does not satisfy a
range, use the <code>satisfies(version, range)</code> function.</p>

</div>

<table border=0 cellspacing=0 cellpadding=0 id=npmlogo>
<tr><td style="width:180px;height:10px;background:rgb(237,127,127)" colspan=18>&nbsp;</td></tr>
<tr><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td><td style="width:40px;height:10px;background:#fff" colspan=4>&nbsp;</td><td rowspan=4 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td><td colspan=6 style="width:60px;height:10px;background:#fff">&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=4>&nbsp;</td></tr>
<tr><td colspan=2 style="width:20px;height:30px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=4 colspan=2>&nbsp;</td><td style="width:10px;height:20px;background:rgb(237,127,127)" rowspan=2>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:20px;height:10px;background:#fff" rowspan=3 colspan=2>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:#fff" rowspan=3>&nbsp;</td><td style="width:10px;height:10px;background:rgb(237,127,127)" rowspan=3>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff" rowspan=2>&nbsp;</td></tr>
<tr><td style="width:10px;height:10px;background:#fff">&nbsp;</td></tr>
<tr><td style="width:60px;height:10px;background:rgb(237,127,127)" colspan=6>&nbsp;</td><td colspan=10 style="width:10px;height:10px;background:rgb(237,127,127)">&nbsp;</td></tr>
<tr><td colspan=5 style="width:50px;height:10px;background:#fff">&nbsp;</td><td style="width:40px;height:10px;background:rgb(237,127,127)" colspan=4>&nbsp;</td><td style="width:90px;height:10px;background:#fff" colspan=9>&nbsp;</td></tr>
</table>
<p id="footer">semver &mdash; npm@2.15.12</p>

